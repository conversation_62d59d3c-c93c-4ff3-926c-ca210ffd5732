from flask_restx import Resource, Namespace

from api.common.helper import create_response
from tests.init_request import InitTestRequest

test_user_api = Namespace('test_user_api', description='Test User related API')

@test_user_api.doc(security='bearer')
@test_user_api.route('/me')
class TestUserMeAPI(Resource):
    def get(self):
        client = InitTestRequest()
        res = {}

        ## step 1: Get User Me
        response = client.get('api/app/user/me')
        res['step 1: Get User Me'] = response.status_code
        
        ## step 2: Get My Stats
        response = client.get('api/app/user/stats')
        res['step 2: Get My Stats'] = response.status_code


        return create_response('Test Finished', data=res)