from flask_restx import Resource, Namespace

from api.common.helper import create_response
from tests.init_request import InitTestRequest

test_quest_api = Namespace('test_quest_api', description='Test Quest related API')

@test_quest_api.doc(security='bearer')
@test_quest_api.route('/cms/create')
class TestCMSQuestCreateAPI(Resource):
    def get(self):
        client = InitTestRequest()
        res = {}
        ## step 1: Get Category List
        response = client.get('api/cms/attributes/category/list')
        res['step 1: Get Category List'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        categories = response.json()['data']


        ## step 2: Get Tag List
        response = client.get('api/cms/attributes/tag/list')
        res['step 2: Get Tag List'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        tags = response.json()['data']

        ## step 3: Get Facet List
        response = client.get('api/cms/attributes/facet/list')
        res['step 3: Get Facet List'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        facets = response.json()['data']

        ## step 4: Create Quest
        quest_model = {
            "name": "Test Quest",
            "description": "Random description",
            "learning_objective": "Random learning objective",
            "level": 0,
            "category_ids": [
                categories[0]['id'],
                categories[1]['id'],
            ],
            "tag_ids": [
                tags[0]['id'],
                tags[1]['id']
            ],
            "facet_ids": [
                facets[0]['id'],
                facets[1]['id']
            ],
            "image": "",
            "configs": {},
            "chest": {
                "xp": 50,
                "coins": 0,
                "gems": 0,
                "keys": 0,
                "asset_ids": [
                ]
            }
        }
        response = client.post('/api/cms/quests', json=quest_model)
        res['step 4: Create Quest'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        ## step 5: Get Quest
        quest_id = response.json()['data']['id']
        response = client.get(f'/api/cms/quests/{quest_id}')
        res['step 5: Get Quest'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        ## step 6: Update Quest
        quest_model['name'] = 'Updated Quest'
        response = client.put(f'/api/cms/quests/{quest_id}', json=quest_model)
        res['step 6: Update Quest'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        ## step 7: Test Node API
        ## step 701: Create Node
        node_model = {
            "quest_id": quest_id,
            "name": "Node 1",
            "screen_type": "test_node",
            "title": "Example Node 1",
            "sub_title": "Random subtitle",
            "points": 0,
            "next_node_id": "",
            "transcripts": [
                {
                    "character_id": "Test Character",
                    "animation": "Test Animation",
                    "show_icon": True,
                    "text": "Random text",
                    "audio_en": "",
                    "duration": 10
                }
            ],
            "branches_from_ids": [],
            "branches": [
            ],
            "quest_character_id": "Test Character",
            "quest_character_animation": "Test Animation",
            "quest_character_audio": "",
            "quest_text": "Random text",
            "answer_placeholder": "",
            "button_text": "Continue",
            "previous_node_id": ""
        }

        response = client.post(f'/api/cms/nodes', json=node_model)
        res['step 701: Create Node'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        ## step 702: Get Node
        node_id = response.json()['data']['id']
        response = client.get(f'/api/cms/nodes/{node_id}')
        res['step 702: Get Node'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        ## step 703: Update Node
        node_model['name'] = 'Updated Node'
        response = client.put(f'/api/cms/nodes/{node_id}', json=node_model)
        res['step 703: Update Node'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)


        ## step 704: Get Node List
        response = client.get(f'/api/cms/nodes/list?quest_id={quest_id}')
        res['step 704: Get Node List'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)

        ## step 8: Delete Quest and Node
        response = client.delete(f'/api/cms/quests/{quest_id}?force=true')
        res['step 8: Delete Quest and Node'] = response.status_code


        return create_response('Test Finished', data=res)
    


quest_parser = test_quest_api.parser()
quest_parser.add_argument('xperience_id', type=str, required=False, default="", help='Xperience ID')
quest_parser.add_argument('full_test', type=bool, required=False, default=False, help='Full Test')
quest_parser.add_argument('test_quest_reset', type=bool, required=False, default=False, help='Test Quest Reset')


@test_quest_api.doc(security='bearer')
@test_quest_api.route('/app/read')
class TestAPPQuestReadAPI(Resource):
    @test_quest_api.expect(quest_parser)
    def get(self):
        client = InitTestRequest()
        res = {}
        res_data = {}
        ## step 1 - 1: Get Xperience List
        response = client.get('api/app/xperience/list')
        res['step 1 - 1: Get Xperience List'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)
        
        xperiences = response.json()['data']
        
        ## step 1 - 2: Get Xperience Detail
        ## get xperience_id from args
        args = quest_parser.parse_args()
        xperience_id = args.get('xperience_id', "")
        if xperience_id == "":
            xperience_id = xperiences[0]['id']

        response = client.get(f'api/app/xperience/{xperience_id}')
        res['step 1 - 2: Get Xperience Detail'] = response.status_code

        if response.status_code != 200:
            return create_response('Test Finished', data=res)
        
        xperience = response.json()['data']

        ## step 2: Get Quest List
        quest_list = xperience.get('quest_list', [])

        full_test = args.get('full_test', False)
        if not full_test:
            quest_list = quest_list[:1]

        quest_index = 0
        for quest in quest_list:
            quest_id = quest['id']
            quest_index = quest_index + 1

            ## step 2: Get Quest
            response = client.get(f'api/app/quest/{quest_id}')
            res[f'Quest {quest_index} - step 2: Get Quest'] = response.status_code

            if response.status_code != 200:
                return create_response('Test Finished', data=res)
            
            quest = response.json()['data']
            
            ## step 3: Start a quest
            start_json = {
                "xperience_id": xperience_id,
            }

            response = client.post(f'api/app/quest/{quest_id}/start', json=start_json)
            res[f'Quest {quest_index} - step 3: Start a quest'] = response.status_code

            if response.status_code != 200:
                return create_response('Test Finished', data=res)

            # ## get response data
            # res_data['quest_start'] = response.json()['data']
            
            ## step 4: Done Nodes and reset the last node in the quest
            nodes = quest['nodes']
            previous_node_id = ""
            index = 1
            for node in nodes:  
                node_id = node['node_id']
                node_model = {
                    "quest_id": quest_id,
                    "node_id": node['node_id'],
                    "node_type": node['node_type'],
                    "value": "Random",
                    "points": 0,
                    "duration": 0,
                    "options": node['value_list']['options'],
                    "previous_node_id": previous_node_id,
                    "next_node_id": node['next_node_id'],
                    "is_retry": False
                }
                previous_node_id = node['node_id']
                index = index + 1

                response = client.post(f'api/app/quest/{quest_id}/{node_id}/done', json=node_model)
                res[f'Quest {quest_index} - step 4 - Node {index}: Done Node'] = response.status_code

                if node['next_node_id'] == "":
                    response = client.post(f'api/app/quest/{quest_id}/{node_id}/reset', json={})
                    res[f'step 4 - {index}: Reset Node'] = response.status_code

                    if response.status_code != 200:
                        return create_response('Test Finished', data=res)

                    response = client.post(f'api/app/quest/{quest_id}/{node_id}/done', json=node_model)
                    res[f'Quest {quest_index} - step 4 - Node {index}: Done Node Again'] = response.status_code


            ## step 5: Complete the quest
            complete_json = {
                "xperience_id": xperience_id,
            }
            response = client.post(f'api/app/quest/{quest_id}/complete', json=complete_json)
            res[f'Quest {quest_index} step 5: Complete the quest'] = response.status_code

            if response.status_code != 200:
                return create_response('Test Finished', data=res)
            
            ## add response data to res_data
            # res_data['rewards'].append(response.json()['rewards'])
            rewards = response.json()['rewards']
            badge = rewards.get('badge', [])
            if badge:
                if 'badge' not in res_data:
                    res_data['badge'] = []
                res_data['badge'].append(badge)

            chest = rewards.get('chest', [])
            if chest:
                if 'chest' not in res_data:
                    res_data['chest'] = []
                res_data['chest'].append(chest)
            
            # step 6: Restart the quest
            # test_quest_reset = args.get('test_quest_reset', False)
            # if test_quest_reset and test_quest_reset == True:
            #     response = client.post(f'api/app/quest/{quest_id}/restart', json={})
            #     res[f'Quest {quest_index} step 6: Restart the quest'] = response.status_code
        
        return create_response('Test Finished', result=res, data=res_data)
