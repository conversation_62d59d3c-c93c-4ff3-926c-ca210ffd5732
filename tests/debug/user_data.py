import datetime
import secrets

from flask import g
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from clientmodels import MasterUser, Quest, User, UserAsset, UserAssignment, UserBadge, UserBoost, UserChest, UserNode, UserNodeHistory, \
    UserPowerUp, UserProgram, UserQuest, UserStore, UserStreak, UserStats, UserCheckin, get_db_session
from clientmodels import UserXP, UserGems, UserSpending, UserXperience, UserXAPADay

debug_user_data_api = Namespace('api_debug_user_data', description='Debug User Data API')

streak_parser = debug_user_data_api.parser()
streak_parser.add_argument('date', type=str, help='Streak date', required=True, default='2024-12-20')
streak_parser.add_argument('email', type=str, help='User email', required=True, default='')

@debug_user_data_api.doc(security='bearer')
@debug_user_data_api.route('/add_streak')
class DebugStreak(Resource):
    @debug_user_data_api.expect(streak_parser)
    def get(self):
        db_session = get_db_session("global")
        g.db_session = db_session

        ## get user email from request
        args = streak_parser.parse_args()
        email = args.get('email').lower().strip()

        if not email:
            return create_response("Email is required", status=400)
        
        user = g.db_session.query(User).filter_by(email=email).first()
        if not user:
            return create_response("User not found", status=404)
        
        user_id = user.id

        # Parse the date from the request
        date_str = args.get('date')
        time_str = args.get('time', '')
 
        try:
            if not time_str:
                datetime_str = date_str + ' 07:00:00'
            else:
                datetime_str = date_str + ' ' + time_str

            streak_date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
            streak_datetime = datetime.datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return create_response("Invalid date format. Use YYYY-MM-DD", status=400)
        
        # Check if the streak already exists for the given date
        existing_streak = g.db_session.query(UserStreak).filter_by(user_id=user_id, date=streak_date).first()
        if existing_streak:
            return create_response("Streak already exists for the given date", status=400)
        
        # Insert the streak for the given date
        streak = UserStreak(user_id=user_id, source="debug", date_created=streak_datetime, date=streak_date)
        g.db_session.add(streak)
        g.db_session.commit()

        # Get the user's streaks
        streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).order_by(UserStreak.date_created.desc()).all()
        streak_list = []

        ## update user stats
        today = streak.date_created.date()
        yesterday = today - datetime.timedelta(days=1)

        streak_count = 1
        for streak in streaks:
            if streak.date:
                streak_day = streak.date
            else:
                streak_day = streak.date_created.date()
            streak_list.append(streak_day.strftime('%Y-%m-%d'))

            if streak_day == today:
                continue
            elif streak_day == yesterday:
                streak_count += 1
                yesterday -= datetime.timedelta(days=1)
            else:
                break

        data = {
            'streaks_count': streak_count,
            'streak_list': streak_list
        }

        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)

        user_stats.streaks_count = streak_count
        g.db_session.commit()
        
        return create_response("Streak added successfully for the given date", data=data)



clear_parser = debug_user_data_api.parser()
clear_parser.add_argument('email', type=str, help='User email', required=True, default='')
clear_parser.add_argument('passcode', type=str, help='Passcode to confirm', required=True, default='')

@debug_user_data_api.doc(security='bearer')
@debug_user_data_api.route('/clear_all')
class ClearStats(Resource):
    def get(self):
        db_session = get_db_session("global")
        g.db_session = db_session

        ## add passcode to avoid accidental reset
        args = clear_parser.parse_args()
        passcode = args.get('passcode', '').lower().strip()
        if passcode != "xapa123":
            return create_response("Invalid passcode", status=400)

        ## get user email from request
        args = clear_parser.parse_args()
        email = args.get('email').lower().strip()

        if not email:
            return create_response("Email is required", status=400)
        
        user = g.db_session.query(User).filter_by(email=email).first()
        if not user:
            return create_response("User not found", status=404)
        
        user_id = user.id
        
        # Clear user stats
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id)
        for stats in user_stats:
            g.db_session.delete(stats)
        
        # Clear user chests
        user_chests = g.db_session.query(UserChest).filter_by(user_id=user_id).all()
        for chest in user_chests:
            g.db_session.delete(chest)
        
        # Clear user quests
        user_quests = g.db_session.query(UserQuest).filter_by(user_id=user_id).all()
        for user_quest in user_quests:
            g.db_session.delete(user_quest)

        # Clear user xperience records
        user_xperiences = g.db_session.query(UserXperience).filter_by(user_id=user_id).all()
        for user_xperience in user_xperiences:
            g.db_session.delete(user_xperience)

        # Clear user program records
        user_programs = g.db_session.query(UserProgram).filter_by(user_id=user_id).all()
        for user_program in user_programs:
            g.db_session.delete(user_program)

        # Clear user nodes
        user_nodes = g.db_session.query(UserNode).filter_by(user_id=user_id).all()
        for user_node in user_nodes:
            g.db_session.delete(user_node)

        # clear user node history
        user_node_histories = g.db_session.query(UserNodeHistory).filter_by(user_id=user_id).all()
        for user_node_history in user_node_histories:
            g.db_session.delete(user_node_history)
        
        # Clear user streaks
        user_streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).all()
        for user_streak in user_streaks:
            g.db_session.delete(user_streak)
        
        # Clear user checkins
        user_checkins = g.db_session.query(UserCheckin).filter_by(user_id=user_id).order_by(UserCheckin.date_created.desc()).first()
        if user_checkins:
            g.db_session.delete(user_checkins)

        # Clear user xapa day
        user_xapa_days = g.db_session.query(UserXAPADay).filter_by(user_id=user_id).order_by(UserXAPADay.date_created.desc()).first()
        if user_xapa_days:
            g.db_session.delete(user_xapa_days)
        
        # Clear user XP
        user_xps = g.db_session.query(UserXP).filter_by(user_id=user_id).all()
        for user_xp in user_xps:
            g.db_session.delete(user_xp)
        
        # Clear user gems
        user_gems = g.db_session.query(UserGems).filter_by(user_id=user_id).all()
        for user_gem in user_gems:
            g.db_session.delete(user_gem)
        
        # Clear user spending
        user_spendings = g.db_session.query(UserSpending).filter_by(user_id=user_id).all()
        for user_spending in user_spendings:
            g.db_session.delete(user_spending)

        # Clear user boost
        user_boosts = g.db_session.query(UserBoost).filter_by(user_id=user_id).all()
        for user_boost in user_boosts:
            g.db_session.delete(user_boost)

        ## Clear user powerups
        user_powerups = g.db_session.query(UserPowerUp).filter_by(user_id=user_id).all()
        for user_powerup in user_powerups:
            g.db_session.delete(user_powerup)

        ## Clear user store
        user_stores = g.db_session.query(UserStore).filter_by(user_id=user_id).all()
        for user_store in user_stores:
            g.db_session.delete(user_store)

        ## Clear user badges
        user_badges = g.db_session.query(UserBadge).filter_by(user_id=user_id).all()
        for user_badge in user_badges:
            g.db_session.delete(user_badge)
        
        g.db_session.commit()
        
        return create_response("User stats and records cleared successfully")
    


@debug_user_data_api.doc(security='bearer')
@debug_user_data_api.route('/reset_onboarding')
class ResetOnboarding(Resource):
    def get(self):
        db_session = get_db_session("global")
        g.db_session = db_session

        ## add a confirmation passcode to avoid accidental reset
        args = clear_parser.parse_args()
        passcode = args.get('passcode', '').lower().strip()
        if passcode != "xapa123":
            return create_response("Invalid passcode", status=400)

        ## get user email from request
        args = clear_parser.parse_args()
        email = args.get('email').lower().strip()

        if not email:
            return create_response("Email is required", status=400)
        
        user = g.db_session.query(User).filter_by(email=email).first()
        if not user:
            return create_response("User not found", status=404)
        
        user_id = user.id

        # Clear user streaks
        user_streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).all()
        for user_streak in user_streaks:
            g.db_session.delete(user_streak)
        
        g.db_session.commit()
        
        return create_response("User onboarding reset successfully")



@debug_user_data_api.doc(security='bearer')
@debug_user_data_api.route('/reset_home')
class ResetHome(Resource):
    def get(self):
        ## add a confirmation passcode to avoid accidental reset
        args = clear_parser.parse_args()
        passcode = args.get('passcode', '').lower().strip()
        if passcode != "xapa123":
            return create_response("Invalid passcode", status=400)

        db_session = get_db_session("global")
        g.db_session = db_session

        ## get user email from request
        args = clear_parser.parse_args()
        email = args.get('email').lower().strip()

        if not email:
            return create_response("Email is required", status=400)
        
        user = g.db_session.query(User).filter_by(email=email).first()
        if not user:
            return create_response("User not found", status=404)
        
        user_id = user.id

        # Clear user streaks
        user_streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).order_by(UserStreak.date_created.desc()).first()
        if user_streaks:
            g.db_session.delete(user_streaks)

        ## Check if user has a streak for pass 5 days, if no then add one
        today = datetime.datetime.today()
        for i in range(1, 6):
            date = today - datetime.timedelta(days=i)
            user_streak = g.db_session.query(UserStreak).filter_by(user_id=user_id, date=date.date()).first()
            if not user_streak:
                streak = UserStreak(user_id=user_id, source="debug", date=date, date_created=date)
                g.db_session.add(streak)

        # Reset user checkin for today
        user_checkin = g.db_session.query(UserCheckin).filter_by(user_id=user_id).order_by(UserCheckin.date_created.desc()).first()
        if user_checkin:
            g.db_session.delete(user_checkin)

        # Reset user xapa day for today
        user_xapa_day = g.db_session.query(UserXAPADay).filter_by(user_id=user_id).order_by(UserXAPADay.date_created.desc()).first()
        if user_xapa_day:
            g.db_session.delete(user_xapa_day)

        ## Reset user quest records and chests
        user_quests = g.db_session.query(UserQuest).filter_by(user_id=user.id).filter(UserQuest.status=='completed').all()
        for user_quest in user_quests:
            quest = g.db_session.query(Quest).filter_by(id=user_quest.quest_id).first()
            if quest:
                chest_id = quest.chest_id
                if chest_id:
                    user_chests = g.db_session.query(UserChest).filter_by(user_id=user.id, chest_id=chest_id).all()
                    for user_chest in user_chests:
                        g.db_session.delete(user_chest)

            g.db_session.delete(user_quest)

        ## Reset Chests
        user_chests = g.db_session.query(UserChest).filter_by(user_id=user.id).all()
        for user_chest in user_chests:
            g.db_session.delete(user_chest)

        ## Reset Assets
        user_assets = g.db_session.query(UserAsset).filter_by(user_id=user.id).all()
        for user_asset in user_assets:
            g.db_session.delete(user_asset)

        ## Reset User Xperience
        user_xperiences = g.db_session.query(UserXperience).filter_by(user_id=user.id).all()
        for user_xperience in user_xperiences:
            g.db_session.delete(user_xperience)
        
        g.db_session.commit()
        
        return create_response("User home data reset successfully")



add_resources_parser = debug_user_data_api.parser()
add_resources_parser.add_argument('email', type=str, help='User email', required=True, default='')
add_resources_parser.add_argument('resource_type', type=str, help='Resource type (xp, coins, gems, keys)', required=True, default='')
add_resources_parser.add_argument('amount', type=int, help='Amount to add', required=True, default=0)

@debug_user_data_api.doc(security='bearer')
@debug_user_data_api.route('/add_resources')
class AddResources(Resource):
    @debug_user_data_api.expect(add_resources_parser)
    def get(self):
        db_session = get_db_session("global")
        g.db_session = db_session

        ## get user email from request
        args = add_resources_parser.parse_args()
        email = args.get('email').lower().strip()
        resource_type = args.get('resource_type').lower().strip()
        amount = args.get('amount')

        if not email:
            return create_response("Email is required", status=400)
        
        if resource_type not in ['xp', 'coins', 'gems', 'keys']:
            return create_response("Invalid resource type. Must be one of: xp, coins, gems, keys", status=400)
        
        if amount <= 0:
            return create_response("Amount must be greater than zero", status=400)
        
        try:
            amount = int(amount)
        except Exception as e:
            return create_response("Amount must be greater than zero", status=400)
        
        user = g.db_session.query(User).filter_by(email=email).first()
        if not user:
            return create_response("User not found", status=404)
        
        user_id = user.id

        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)
        
        if resource_type == 'xp':
            xp_count = user_stats.xp_count if user_stats.xp_count else 0
            user_stats.xp_count = xp_count + amount
        elif resource_type == 'coins':
            coins_count = user_stats.coins_count if user_stats.coins_count else 0
            user_stats.coins_count = coins_count + amount
        elif resource_type == 'gems':
            gems_count = user_stats.gems_count if user_stats.gems_count else 0
            user_stats.gems_count = gems_count + amount
        elif resource_type == 'keys':
            keys_count = user_stats.keys_count if user_stats.keys_count else 0
            user_stats.keys_count = keys_count + amount
        
        g.db_session.commit()
        
        return create_response(f"{amount} {resource_type} added successfully to the user")



@debug_user_data_api.route('/add_test_users')
class AddTestUsers(Resource):
    def get(self):
        db_session = get_db_session("global")
        g.db_session = db_session
        for i in range(1, 201):
            email = f"tester{i}@xapa.ai"
            ## check if user already exists
            user = g.db_session.query(User).filter_by(email=email).first()
            if not user:
                user = User(email=email)
                g.db_session.add(user)
                g.db_session.commit()

                ## add user assginments
                client_id = "067934c3-c732-75ac-8000-059233c31274"
                assginment = g.db_session.query(UserAssignment).filter_by(user_id=user.id).first()
                if assginment is None:
                    ## create a new user assignment and master user account
                    master_user = MasterUser(
                        username=secrets.token_hex(8),
                        first_name=user.first_name,
                        last_name=user.last_name
                    )
                    g.db_session.add(master_user)
                    g.db_session.commit()
                    master_user_id = master_user.id
                else:
                    master_user_id = assginment.master_user_id
                
                master_user_id = master_user_id
                assignment = UserAssignment(
                    user_id=user.id,
                    master_user_id=master_user_id,
                    client_id=client_id
                )
                g.db_session.add(assignment)


        g.db_session.commit()
        
        return create_response("200 test users added successfully")
    


@debug_user_data_api.route('/remove_test_users')
class RemoveTestUsers(Resource):
    def get(self):
        from api.common.users_utils import delete_user
        
        db_session = get_db_session("global")
        g.db_session = db_session

        for i in range(1, 201):
            email = f"tester{i}@xapa.ai"
            user = g.db_session.query(User).filter_by(email=email).first()
            if user:
                result = delete_user(user)

                ## remove user from user assignment
                user_assignment = g.db_session.query(UserAssignment).filter_by(user_id=user.id).first()
                if user_assignment:
                    ## remove master account
                    master_user = g.db_session.query(MasterUser).filter_by(id=user_assignment.master_user_id).first()
                    if master_user:
                        g.db_session.delete(master_user)

                    g.db_session.delete(user_assignment)

                g.db_session.commit()
                print(result)
        
        return create_response("200 test users removed successfully")