import unittest

from api.account.authentication import generate_tokens
# Assuming your application factory function is named create_app
from app import create_app
from clientmodels import *
from models import *


class TestAPI(unittest.TestCase):
    def setUp(self):
        # Create an instance of the app with the testing configuration
        self.app = create_app('config.test')
        self.client = self.app.test_client()  # Create the test client
        self.app_context = self.app.app_context()
        self.app_context.push()
        with self.app.app_context():
            db.create_all()

        # Load all the bindings
        for bind in app.config.get('SQLALCHEMY_BINDS', {}):
            print(bind)
            # Get the engine for the bind
            engine = db.get_engine(bind=bind)

            # Reflect the models to the metadata
            db.Model.metadata.reflect(bind=engine)

            # Create all tables in the metadata
            db.Model.metadata.create_all(bind=engine)

        self.db_session = get_db_session("test")

        # Create a test user and token
        self.user = User(email='<EMAIL>', first_name='Test', last_name='User')
        self.db_session.add(self.user)
        self.db_session.commit()

        print('Add User ID:', self.user.id)

        token = generate_tokens(self.user.id, 'test')
        self.token = token[0]
        self.refresh_token = token[1]
        self.user_token = UserToken(
            user_id=self.user.id,
            token=self.token,
            refresh_token=self.refresh_token,
            date_expired=datetime.datetime.utcnow() + datetime.timedelta(days=1),
            device_id="",
            login_type="test"
        )
        self.db_session.add(self.user_token)
        self.db_session.commit()

        print('Add Test User Token:')


        # Add sample data if needed
        # ...

    def tearDown(self):
        # Tear down the database
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_api_calls(self):
        headers = {
            'Authorization': self.token
        }


        ## step 1: Get Category List
        response = self.client.get('/api/cms/attributes/category/list', headers=headers)
        self.assertEqual(response.status_code, 200)
        categories = response.json['data']

        ## step 2: Get Tag List
        response = self.client.get('/api/cms/attributes/tag/list', headers=headers)
        self.assertEqual(response.status_code, 200)
        tags = response.json['data']

        ## step 3: Get Facet List
        response = self.client.get('/api/cms/attributes/facet/list', headers=headers)
        self.assertEqual(response.status_code, 200)
        facets = response.json['data']

        ## step 4: Create Quest
        quest_model = {
            "name": "Test Quest",
            "description": "Random description",
            "learning_objective": "Random learning objective",
            "level": 0,
            "category_ids": [
                categories[0]['id'],
                categories[1]['id'],
            ],
            "tag_ids": [
                tags[0]['id'],
                tags[1]['id']
            ],
            "facet_ids": [
                facets[0]['id'],
                facets[1]['id']
            ],
            "image": "",
            "configs": {},
            "chest": {
                "xp": 50,
                "coins": 0,
                "gems": 0,
                "keys": 0,
                "asset_ids": [
                ]
            }
        }

        response = self.client.post('/api/quests', json=quest_model, headers=headers)
        self.assertEqual(response.status_code, 200)

        ## step 5: Get Quest
        quest_id = response.json['data']['id']
        response = self.client.get(f'/api/quests/{quest_id}', headers=headers)
        self.assertEqual(response.status_code, 200)

        ## step 6: Update Quest
        quest_model['name'] = 'Updated Quest'
        response = self.client.put(f'/api/quests/{quest_id}', json=quest_model, headers=headers)
        self.assertEqual(response.status_code, 200)

        ## step 7: Test Node API
        ## step 701: Create Node
        node_model = {
            "quest_id": quest_id,
            "name": "Node 1",
            "screen_type": "test_node",
            "title": "Example Node 1",
            "sub_title": "Random subtitle",
            "points": 0,
            "next_node_id": "",
            "transcripts": [
                {
                    "character_id": "Test Character",
                    "animation": "Test Animation",
                    "show_icon": True,
                    "text": "Random text",
                    "audio_en": "",
                    "duration": 10
                }
            ],
            "branches_from_ids": [],
            "branches": [
            ],
            "quest_character_id": "Test Character",
            "quest_character_animation": "Test Animation",
            "quest_character_audio": "",
            "quest_text": "Random text",
            "answer_placeholder": "",
            "button_text": "Continue",
            "previous_node_id": ""
        }

        response = self.client.post(f'/api/nodes', json=node_model, headers=headers)
        self.assertEqual(response.status_code, 200)

        ## step 702: Get Node
        node_id = response.json['data']['id']
        response = self.client.get(f'/api/nodes/{node_id}', headers=headers)
        self.assertEqual(response.status_code, 200)

        ## step 703: Update Node
        node_model['name'] = 'Updated Node'
        response = self.client.put(f'/api/nodes/{node_id}', json=node_model, headers=headers)
        self.assertEqual(response.status_code, 200)

        ## step 704: Get Node List
        response = self.client.get(f'/api/nodes/list?quest_id={quest_id}', headers=headers)
        self.assertEqual(response.status_code, 200)

        ## step 8: Delete Quest and Node
        response = self.client.delete(f'/api/quests/{quest_id}?force=true', headers=headers)
        self.assertEqual(response.status_code, 200)


if __name__ == '__main__':
    unittest.main()