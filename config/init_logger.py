import logging

from flask import <PERSON>las<PERSON>

def init_logger(app: Flask) -> None:
    """Configure global logging for the application.
    
    Args:
        app: Flask application instance
    """
    # Set the basic logging level
    log_level = logging.INFO
    
    # Set log level for Flask logger
    app.logger.setLevel(log_level)
    
    # Set root logger level
    logging.getLogger().setLevel(log_level)
    
    app.logger.info('Logger initialized')
