import logging

from flask import Flask
from flask_compress import Compress

logger = logging.getLogger(__name__)

# Initialize Flask-Compress for gzip compression with optimized settings
compress = Compress()

def init_compress(app: Flask) -> None:
    
    # Get environment from app config
    environment = app.config.get('ENVIRONMENT', 'dev')
    
    # Configure compression settings
    app.config['COMPRESS_MIMETYPES'] = [
        'text/html',
        'text/css',
        'text/xml',
        'application/json',
        'application/javascript',
        'application/x-javascript',
        'application/xml',
        'application/xml+rss',
        'text/javascript',
        'text/plain',
        'image/svg+xml',
    ]
    
    # Adjust compression level based on environment
    if environment == 'dev':
        # Lower compression in development for faster response
        app.config['COMPRESS_LEVEL'] = 1
        app.config['COMPRESS_MIN_SIZE'] = 1000  # Only compress responses larger than 1KB
        logger.info("Development environment: Using minimal compression")
    else:
        # Higher compression in staging/production
        app.config['COMPRESS_LEVEL'] = 6  # Compression level between 1-9 (higher = better compression but slower)
        app.config['COMPRESS_MIN_SIZE'] = 500  # Compress responses larger than 500 bytes
        logger.info(f"{environment} environment: Using standard compression")
    
    app.config['COMPRESS_ALGORITHM'] = 'gzip'  # Use gzip algorithm
    app.config['COMPRESS_REGISTER'] = True  # Auto-register compression

    compress.init_app(app)
    logger.info(f"Compression initialized for {environment} environment")

    return compress