import logging

from flask import Flask
from opencensus.ext.azure.log_exporter import <PERSON>zureLogHandler
from opencensus.ext.azure.trace_exporter import AzureExporter
from opencensus.ext.flask.flask_middleware import FlaskMiddleware
from opencensus.trace.samplers import ProbabilitySampler
from opencensus.trace.tracer import Tracer

logger = logging.getLogger(__name__)

def init_app_insights(app: Flask) -> None:
    """
    Initialize Azure Application Insights for the Flask application.
    
    Args:
        app: Flask application instance
        
    Returns:
        FlaskMiddleware instance if running in production, None otherwise
    """
    # Get environment from app config
    is_production = app.config.get('IS_PRODUCTION', False)
    if not is_production:
        return None

    # Get environment from app config
    environment = app.config.get('ENVIRONMENT', 'dev')
    
    # Get instrumentation key from app config
    instrumentation_key = app.config.get('INSTRUMENTATION_KEY')
    if not instrumentation_key:
        logger.warning(f"Application Insights instrumentation key not found in config for {environment} environment")
        return None
        
    connection_string = f'InstrumentationKey={instrumentation_key}'
    
    # Set up logging
    logger.addHandler(AzureLogHandler(connection_string=connection_string))
    
    # Set up tracing
    tracer = Tracer(
        exporter=AzureExporter(connection_string=connection_string),
        sampler=ProbabilitySampler(1.0),
    )
    with tracer.span(name='example'):
        logger.info(f'Tracing example for {environment} environment')
    
    # Log successful configuration
    logger.info(f'Application Insights configured successfully for {environment} environment')
    
    # Initialize Flask middleware for request tracking
    middleware = FlaskMiddleware(
        app,
        exporter=AzureExporter(connection_string=connection_string),
        sampler=ProbabilitySampler(1.0),
    )
    
    return middleware