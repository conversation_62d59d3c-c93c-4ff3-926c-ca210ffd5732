import os
import logging

from flask import Flask
from flask_caching import <PERSON><PERSON>

import functools
from services.azure_key_vault import AzureKeyVault
from redis.exceptions import RedisError

logger = logging.getLogger(__name__)

class XapaCache(Cache):
    def invalidate_cache(self, pattern):
        """
        Decorator to invalidate cache after a function is executed using a pattern
        
        Args:
            pattern (str): Pattern to match cache keys to invalidate
                Examples: 'global_setting:city', 'store:list', 'home:banners', 'xperience:*'
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Execute the original function
                result = func(*args, **kwargs)
                
                # Extract resource ID from kwargs if available
                resource_id = None
                if hasattr(kwargs, 'get'):
                    for key in ['id', 'xperience_id', 'store_id', 'banner_id']:
                        if key in kwargs:
                            resource_id = kwargs[key]
                            break
                
                # If we have a resource ID, append it to the pattern
                cache_pattern = f"{pattern}:{resource_id}" if resource_id else pattern
                
                # Invalidate cache using pattern
                try:
                    # Get the underlying cache client
                    if hasattr(self, 'cache') and hasattr(self.cache, '_client'):
                        client = self.cache._client
                        # For Redis cache
                        if hasattr(client, 'keys'):
                            # Create search pattern with wildcard
                            prefix = self.config.get('CACHE_KEY_PREFIX', '')
                            search_pattern = f"{prefix}*{cache_pattern}*"
                            keys = client.keys(search_pattern)
                            count = 0
                            
                            for key in keys:
                                # Convert bytes to string if necessary
                                if isinstance(key, bytes):
                                    key_str = key.decode('utf-8')
                                else:
                                    key_str = key
                                    
                                # Remove the prefix for the delete operation if needed
                                if key_str.startswith(prefix):
                                    cache_key = key_str[len(prefix):]
                                else:
                                    cache_key = key_str
                                    
                                if self.delete(cache_key):
                                    count += 1
                                    
                            logger.info(f"Invalidated {count} cache keys matching pattern '{cache_pattern}'")
                        else:
                            # Simple cache implementation - just delete the specific key
                            self.delete(cache_pattern)
                except Exception as e:
                    # Log error but don't fail the request
                    logger.error(f"Error invalidating cache pattern '{cache_pattern}': {str(e)}")
                
                return result
            return wrapper
        return decorator

# Initialize cache outside of create_app to make it available for imports
cache = XapaCache()

def init_cache(app: Flask) -> None:
    """Initialize and configure Flask-Caching with Redis.
    
    Args:
        app: Flask application instance
        
    Returns:
        Cache: Initialized cache instance
    """
    # Cache configuration
    cache_config = {
        "CACHE_TYPE": "RedisCache",
        "CACHE_DEFAULT_TIMEOUT": int(os.getenv("CACHE_TIMEOUT", 300)),  # 5 minutes default
        "CACHE_KEY_PREFIX": os.getenv("CACHE_KEY_PREFIX", "xapa:"),  # Add prefix to all keys
    }

    try:
        # Get environment from app config
        is_production = app.config.get('IS_PRODUCTION', False)
        environment = app.config.get('ENVIRONMENT', 'dev')
        if environment == 'prod':
            db_index = 0
        elif environment == 'stage':
            db_index = 1
        else:
            db_index = 2
        
        # Get Redis configuration based on environment
        if is_production:
            # Use connection string from Azure Key Vault in production or staging
            get_secret = AzureKeyVault().get_secret
            # Use environment-specific Redis URL if available
            redis_url = get_secret('redis-url')
            if not redis_url:
                raise ValueError(f"Redis URL not found in Azure Key Vault for {environment} environment")

            # Update cache config with Redis URL
            cache_config.update({
                "CACHE_REDIS_URL": redis_url,
                "CACHE_REDIS_DB": int(os.getenv("REDIS_DB", db_index)) 
            })
            logger.info(f"Redis cache initialized with {environment} configuration")
        else:
            # Use environment variable for development Redis URL
            redis_url = os.getenv('REDIS_URL')
            if not redis_url:
                raise ValueError("REDIS_URL environment variable not set")
            # Update cache config with Redis URL
            cache_config.update({
                "CACHE_REDIS_URL": redis_url,
                "CACHE_REDIS_DB": int(os.getenv("REDIS_DB", 15))
            })
            logger.info("Redis cache initialized with development configuration")
        
        # Initialize the cache extension
        cache.init_app(app, config=cache_config)
        
        # Test the connection
        cache.get('test_key')
        return cache
        
    except (RedisError, ValueError) as e:
        logger.error(f"Failed to initialize Redis cache: {str(e)}")
        logger.warning("Falling back to SimpleCache")
        
        # Fallback to simple cache if Redis is unavailable
        fallback_config = {
            "CACHE_TYPE": "SimpleCache",
            "CACHE_DEFAULT_TIMEOUT": int(os.getenv("CACHE_TIMEOUT", 300))
        }
        cache.init_app(app, config=fallback_config)
        return cache
    except Exception as e:
        logger.critical(f"Unexpected error initializing cache: {str(e)}")
        return None
