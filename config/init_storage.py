import logging
import os

from api.common.storage import Storage

logger = logging.getLogger(__name__)

# Global storage instance
storage_instance = None

def init_storage(app):
    """Initializes and returns a Storage service instance.
    
    Args:
        app: The Flask application instance
        
    Returns:
        Storage: Configured storage service instance
    """
    global storage_instance
    
    # Get storage configuration from environment or config
    storage_type = os.environ.get("STORAGE_TYPE", "azure")
    
    # Initialize the storage instance
    storage_instance = Storage(storage_type)
    
    # Attach storage to app context
    app.storage = storage_instance
    
    logger.info(f"Storage initialized with provider: {storage_type}")
    
    return storage_instance
