import os

from services.azure_key_vault import AzureKeyVault

# SECURITY WARNING: keep the secret key used in production secret!
# Use this py command to create secret 
# python -c 'import secrets; print(secrets.token_hex())'
SECRET_KEY = os.getenv('AZURE_SECRET_KEY')

# Configure allowed host names that can be served and trusted origins for Azure Container Apps.
ALLOWED_HOSTS = ['.azurecontainerapps.io']
CSRF_TRUSTED_ORIGINS = ['https://*.azurecontainerapps.io']
DEBUG = False

USER_ENABLE_EMAIL = False

## Load the environment variables from azure keyvault
get_secret = AzureKeyVault().get_secret
SECRET_KEY = get_secret("azure-secret-key")


DATABASE_URI = get_secret("db-global-uri")

## load binds from key vault
DATABASE_BINDS = {}
bind_names = get_secret("db-bind-names").split(",")
for bind in bind_names:
    DATABASE_BINDS[bind] = get_secret(f"db-{bind}-uri")


## load redirect urls from key vault
CMS_REDIRECT_URL = get_secret("cms-redirect-url")
APP_REDIRECT_URL = get_secret("app-redirect-url")
WEB_REDIRECT_URL = get_secret("web-redirect-url") 

CHATBOT_WEBHOOK_URL = get_secret("chatbot-webhook-url")

INSTRUMENTATION_KEY = get_secret("instrumentation-key")
