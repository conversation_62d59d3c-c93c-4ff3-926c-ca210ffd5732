import logging

from flask import Flask, g
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

logger = logging.getLogger(__name__)

# Initialize SQLAlchemy instance
db = SQLAlchemy()

# Initialize Flask-Migrate instance
migrate = None

def init_database(app: Flask) -> None:
    """Initialize database configuration and connection"""
    # Initialize database configuration
    global db, migrate
    
    # Get environment from app config
    environment = app.config.get('ENVIRONMENT', 'dev')
    logger.info(f"Initializing database for {environment} environment")

    with app.app_context():
        app.config.update(
            SQLALCHEMY_DATABASE_URI=app.config.get('DATABASE_URI'),
            SQLALCHEMY_TRACK_MODIFICATIONS=False,
            SQLALCHEMY_BINDS=app.config.get('DATABASE_BINDS')
        )

    # Initialize database connection
    db.init_app(app)
    
    # Enable Flask-Migrate commands
    migrate = Migrate(app, db)

    # Initialize database models
    with app.app_context():
        # Import models here to avoid circular imports
        import models
        import clientmodels
        
        db.create_all()

        # Load all the bindings
        for bind in app.config.get('SQLALCHEMY_BINDS', {}):
            # Get the engine for the bind
            engine = db.get_engine(bind=bind)

            # Reflect the models to the metadata
            db.Model.metadata.reflect(bind=engine)

            # Create all tables in the metadata
            db.Model.metadata.create_all(bind=engine)

    # Register teardown handler
    @app.teardown_appcontext
    def teardown_db(exception=None):
        """Clean up database resources when the app context ends"""
        db_session = g.pop('db_session', None)
        if db_session is not None:
            db_session.close()

        client_db_session = g.pop('client_db_session', None)
        if client_db_session is not None:
            client_db_session.close()
