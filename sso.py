import datetime
import secrets
from urllib.parse import urlparse

from flask import Blueprint, request, redirect, url_for, session
from sqlalchemy import func

from api.common.helper import create_response
from app import app
from clientmodels import USER_TENANT
from clientmodels import get_db_session, User, SSOConfiguration, Client
from app import auth0

sso = Blueprint('sso', __name__)


## load redirect urls
CMS_REDIRECT_URL = app.config.get("CMS_REDIRECT_URL", "")
APP_REDIRECT_URL = app.config.get("APP_REDIRECT_URL", "")
WEB_REDIRECT_URL = app.config.get("WEB_REDIRECT_URL", "")


@sso.route('/sso/login', methods=['GET'], endpoint='ssologin')
def sso_login():
    db_session = get_db_session(USER_TENANT)
    
    sso_key = request.args.get('sso_key', '')
    email = request.args.get('email', "")
    state = request.args.get('state', "")
    print("sso_key: " + sso_key)
    print("email: " + email)
    print("state: " + state)
    if not email and not sso_key:
        return "Invalid sso method"

    ## if sso_key is provided and email is not, then use the sso_key to find the email
    try:
        if sso_key:
            sso_config = db_session.query(SSOConfiguration).filter_by(id_key=sso_key).first()
            if not sso_config:
                return "Invalid sso method"
        elif email:
            sso_config = find_user_sso_config(email)
            if not sso_config:
                return "Invalid corporate user account, Please contact your administrator"
        else:
            return "Invalid sso method"
    except Exception as e:
        print(e)
        return "Invalid sso method"

    if sso_config.sso_type == 'auth0':
        return redirect(url_for('sso.auth0_login', state=state))
    
    return "Invalid sso method"


@sso.route('/auth0/login', methods=['GET'], endpoint='auth0_login')
def auth0_login(**kwargs):
    state = request.args.get('state', "")
    print("sso state: " + state)
    session['login_type'] = state
    if state:
        session["state"] = state + '_' + secrets.token_hex(8)
    else:
        session["state"] = secrets.token_hex(8)

    print("url state: " + session["state"])

    redirect_uri = url_for('sso.auth0_callback', _external=True)
    return auth0.authorize_redirect(redirect_uri, state=session["state"])


@sso.route('/auth0/callback', methods=['GET'], endpoint='auth0_callback')
def callback():
    token = auth0.authorize_access_token()
    print(token)
    try:
        user_info = token.get('userinfo')
        email = user_info.get('email', "").lower()
        sub = user_info.get('sub', "")

        # Determine the SSO method based on the sub
        if 'google' in sub:
            sso_method = 'google'
        elif 'waad' in sub:
            sso_method = 'microsoft'
        elif 'apple' in sub:
            sso_method = 'apple'
        else:
            sso_method = 'passwordless'

        print(user_info)
        
        if email:
            email = email.lower()
            first_name = user_info.get("first_name", user_info.get("given_name", ""))
            last_name = user_info.get("last_name", user_info.get("family_name", ""))
            
            if not first_name and "nickname" in user_info:
                first_name = user_info.get("nickname", "")

            args = {
                "first_name": first_name,
                "last_name": last_name
            }

            user_info = get_user_info(email, sso_type=sso_method, **args)
            print(user_info)

            issuer = user_info.get("issuer", "")
            token = user_info.get("token", "")

            ## try to get the login type from the session
            state = session.get('login_type', "")
            if not state:
                state = request.args.get('state', "")
                if '_' in state:
                    state = state.split('_')[0]

            print("login type: " + state)

            response = create_sso_response(state, issuer, token, user_info, email)
            return response

                
        return create_response("Logged in successfully", data=user_info)

    except Exception as e:
        print(e)
        return "Error"


def prepare_flask_request(request):
    url_data = urlparse(request.url)
    return {
        'https': 'on' if request.scheme == 'https' else 'off',
        'http_host': request.host,
        'server_port': url_data.port,
        'script_name': request.path,
        'get_data': request.args.copy(),
        'post_data': request.form.copy()
    }


def find_user_sso_config(email):
    db_session = get_db_session()
    try:
        email_suffix = email.split('@')[1]
    except Exception as e:
        print(e)
        return None

    ## find sso config by email suffix, case insensitive
    sso_config = db_session.query(SSOConfiguration).filter(SSOConfiguration.email_suffix.ilike(f"%{email_suffix}%")).first()

    if not sso_config:
        return None
    
    return sso_config


def find_user_tenant(email):
    db_session = get_db_session()
    try:
        email_suffix = email.split('@')[1]
    except Exception as e:
        print(e)

    ## find sso config by email suffix, case insensitive
    tenant = db_session.query(Client).filter(
        func.lower(Client.email_suffix).contains(func.lower(email_suffix))
    ).first()

    ## if we cannot find the tenant, then use the global tenant
    ## TODO: remove this when we have all the tenants
    if not tenant:
        tenant = db_session.query(Client).filter_by(id_key=USER_TENANT).first()

    return tenant


def get_user_info(email, sso_type="", **kwargs):
    ## get api user
    user_info = {}
    print(email)
    tenant = find_user_tenant(email)
    # db_session = get_db_session(tenant.id_key)

    access_token = kwargs.get('access_token', None)
    refresh_token = kwargs.get('refresh_token', None)
    exp = kwargs.get('exp', None)

    first_name = kwargs.get('first_name', "")
    last_name = kwargs.get('last_name', "")

    client_id = tenant.id
    db_session = get_db_session(USER_TENANT)

    token = secrets.token_hex(50)

    user = db_session.query(User).filter_by(email=email).first()
    if user:
        user.sso_redirect_token = token
        user.sso_type = sso_type
        user.last_login = datetime.datetime.utcnow()
        user.client_id = client_id

        db_session.add(user)

    else:
        ## create a new user
        user = User()
        user.email = email
        user.first_name = first_name
        user.last_name = last_name

        user.sso_redirect_token = token
        user.sso_type = sso_type
        user.last_login = datetime.datetime.utcnow()
        user.client_id = client_id

        user.is_active = False
        user.status = 'unassigned'

        db_session.add(user)

    if not user.first_name:
        user.first_name = "Enter First Name"
    if not user.last_name:
        user.last_name = "Enter Last Name"

    db_session.commit()

    user_info['id'] = user.id
    user_info['first_name'] = user.first_name
    user_info['last_name'] = user.last_name
    user_info['email'] = user.email
    user_info['token'] = user.sso_redirect_token
    user_info['issuer'] = tenant.id_key

    return user_info


def create_sso_response(login_type, issuer, token, user_info, email):
    ## for dev environment, redirect back to local if the host is local
    if login_type == 'cms':
        ## redirect to the cms
        if not CMS_REDIRECT_URL:
            return create_response("Logged in successfully", data=user_info)
        
        redirect_url = CMS_REDIRECT_URL.replace("{email}", email).replace("{issuer}", issuer).replace("{token}", token)
        return redirect(redirect_url)
        # return create_response("Logged in successfully", data=user_info)
    elif login_type == 'app':
        ## redirect to the app
        if not APP_REDIRECT_URL:
            return create_response("Logged in successfully", data=user_info)
        
        redirect_url = APP_REDIRECT_URL.replace("{issuer}", issuer).replace("{token}", token)
        return redirect(redirect_url)

    elif login_type == 'web':
        ## redirect to the web
        if not WEB_REDIRECT_URL:
            return create_response("Logged in successfully", data=user_info)
        
        redirect_url = WEB_REDIRECT_URL.replace("{issuer}", issuer).replace("{token}", token)
        return redirect(redirect_url)

    else:
        return create_response("Logged in successfully", data=user_info)
