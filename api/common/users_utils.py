from flask import g, current_app
from sqlalchemy import inspect

from clientmodels import USER_TENANT, Character, get_db_session, User
import logging

logger = logging.getLogger(__name__)

def sync_user(user, tenant_id=None):
    if not tenant_id:
        tenant_id = g.tenant_id
    
    print(f"Syncing user {user.id} to tenant {tenant_id}")

    if tenant_id == USER_TENANT:
        return
    
    client_tenant_session = get_db_session(tenant_id)
    client_user = client_tenant_session.query(User).filter_by(id=user.id).first()
    if not client_user:
        # client_user = User(
        #     id=user.id
        # )
        ## check if user with the same email exist
        client_user = client_tenant_session.query(User).filter_by(email=user.email).first()
        if not client_user:
            client_user = User(
                id=user.id
            )

        
    for column in inspect(client_user).mapper.column_attrs:
        key = column.key
        if hasattr(user, key):
            setattr(client_user, key, getattr(user, key))

    client_tenant_session.add(client_user)
    client_tenant_session.commit()

    return client_user




def delete_user(user):
    from clientmodels import UserStats, UserChest, UserQuest, UserNode, UserNodeHistory, UserStreak, UserCheckin, UserXAPADay, UserXP, UserGems, UserSpending, UserBoost, UserPowerUp, UserStore, UserBadge
    from clientmodels import FeedComment, Feed, FeedLike, FeedFlag, XircleMember, Xircle
    from clientmodels import UserToken, UserLoginLog, UserGraphToken
    from clientmodels import UserProgram, UserXperience, UserCoins, UserKeys, UserAsset
    from clientmodels import Character

    ## exclude any user with id ********-0000-0000-0000-************
    if user.id == '********-0000-0000-0000-************':
        return False
    
    ## exclude any user that is in the character table
    if g.db_session.query(Character).filter_by(user_id=user.id).first():
        return False

    ## delete all user related data
    # Clear user stats
    user_id = user.id
    user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id)
    for stats in user_stats:
        g.db_session.delete(stats)
    
    # Clear user chests
    user_chests = g.db_session.query(UserChest).filter_by(user_id=user_id).all()
    for chest in user_chests:
        g.db_session.delete(chest)
    
    # Clear user quests
    user_quests = g.db_session.query(UserQuest).filter_by(user_id=user_id).all()
    for user_quest in user_quests:
        g.db_session.delete(user_quest)

    # Clear user nodes
    user_nodes = g.db_session.query(UserNode).filter_by(user_id=user_id).all()
    for user_node in user_nodes:
        g.db_session.delete(user_node)

    # clear user node history
    user_node_histories = g.db_session.query(UserNodeHistory).filter_by(user_id=user_id).all()
    for user_node_history in user_node_histories:
        g.db_session.delete(user_node_history)
    
    # Clear user streaks
    user_streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).all()
    for user_streak in user_streaks:
        g.db_session.delete(user_streak)
    
    # Clear user checkins
    user_checkins = g.db_session.query(UserCheckin).filter_by(user_id=user_id).order_by(UserCheckin.date_created.desc()).first()
    if user_checkins:
        g.db_session.delete(user_checkins)

    # Clear user xapa day
    user_xapa_days = g.db_session.query(UserXAPADay).filter_by(user_id=user_id).order_by(UserXAPADay.date_created.desc()).first()
    if user_xapa_days:
        g.db_session.delete(user_xapa_days)
    
    # Clear user XP
    user_xps = g.db_session.query(UserXP).filter_by(user_id=user_id).all()
    for user_xp in user_xps:
        g.db_session.delete(user_xp)
    
    # Clear user gems
    user_gems = g.db_session.query(UserGems).filter_by(user_id=user_id).all()
    for user_gem in user_gems:
        g.db_session.delete(user_gem)

    # Clear user Coins
    user_coins = g.db_session.query(UserCoins).filter_by(user_id=user_id).all()
    for user_coin in user_coins:
        g.db_session.delete(user_coin)

    # Clear user keys
    user_keys = g.db_session.query(UserKeys).filter_by(user_id=user_id).all()
    for user_key in user_keys:
        g.db_session.delete(user_key)
    
    # Clear user spending
    user_spendings = g.db_session.query(UserSpending).filter_by(user_id=user_id).all()
    for user_spending in user_spendings:
        g.db_session.delete(user_spending)

    # Clear user boost
    user_boosts = g.db_session.query(UserBoost).filter_by(user_id=user_id).all()
    for user_boost in user_boosts:
        g.db_session.delete(user_boost)

    ## Clear user powerups
    user_powerups = g.db_session.query(UserPowerUp).filter_by(user_id=user_id).all()
    for user_powerup in user_powerups:
        g.db_session.delete(user_powerup)

    ## Clear user store
    user_stores = g.db_session.query(UserStore).filter_by(user_id=user_id).all()
    for user_store in user_stores:
        g.db_session.delete(user_store)

    ## Clear user badges
    user_badges = g.db_session.query(UserBadge).filter_by(user_id=user_id).all()
    for user_badge in user_badges:
        g.db_session.delete(user_badge)

    ## Clear user asset
    user_assets = g.db_session.query(UserAsset).filter_by(user_id=user_id).all()
    for user_asset in user_assets:
        g.db_session.delete(user_asset)

    ## Clear User Feed Comments
    user_feed_comments = g.db_session.query(FeedComment).filter_by(user_id=user_id).all()
    for user_feed_comment in user_feed_comments:
        g.db_session.delete(user_feed_comment)

    ## Clear User Feeds
    user_feeds = g.db_session.query(Feed).filter_by(user_id=user_id).all()
    for user_feed in user_feeds:
        g.db_session.delete(user_feed)

    ## Clear User Feed Likes
    user_feed_likes = g.db_session.query(FeedLike).filter_by(user_id=user_id).all()
    for user_feed_like in user_feed_likes:
        g.db_session.delete(user_feed_like)

    ## Clear User Feed Flags
    user_feed_flags = g.db_session.query(FeedFlag).filter_by(user_id=user_id).all()
    for user_feed_flag in user_feed_flags:
        g.db_session.delete(user_feed_flag)

    ## Clear User Xircles
    user_xircles = g.db_session.query(XircleMember).filter_by(user_id=user_id).all()
    for user_xircle in user_xircles:
        g.db_session.delete(user_xircle)

    ## Clear User Xircle Created
    ## check super admin account
    admin_user = g.db_session.query(User).filter(User.id=='********-0000-0000-0000-************').first()
    if not admin_user:
        admin_user = User()
        admin_user.id = '********-0000-0000-0000-************'
        admin_user.first_name = 'Xapa'
        admin_user.email = 'admin@xapa'
        admin_user.is_active = False
        admin_user.is_deleted = False
        g.db_session.add(admin_user)
        g.db_session.commit()

    user_created_xircles = g.db_session.query(Xircle).filter_by(creator_id=user_id).all()
    for user_created_xircle in user_created_xircles:
        user_created_xircle.creator_id = "********-0000-0000-0000-************"

    ## Clear User Token
    user_token = g.db_session.query(UserToken).filter_by(user_id=user_id).all()
    for token in user_token:
        g.db_session.delete(token)
    
    ## Clear User Login Log
    user_login_logs = g.db_session.query(UserLoginLog).filter_by(user_id=user_id).all()
    for login_log in user_login_logs:
        g.db_session.delete(login_log)

    ## Clear User Graph Token
    user_graph_tokens = g.db_session.query(UserGraphToken).filter_by(user_id=user_id).all()
    for graph_token in user_graph_tokens:
        g.db_session.delete(graph_token)

    ## Clear User Program Progress
    user_program_progress = g.db_session.query(UserProgram).filter_by(user_id=user_id).all()
    for program_progress in user_program_progress:
        g.db_session.delete(program_progress)

    ## Clear User Xperience
    user_xperiences = g.db_session.query(UserXperience).filter_by(user_id=user_id).all()
    for user_xperience in user_xperiences:
        g.db_session.delete(user_xperience)

    ## Clear Character
    user_characters = g.db_session.query(Character).filter_by(user_id=user_id).all()
    for user_character in user_characters:
        user_character.user_id = None

    g.db_session.commit()

    ## TODO: Remove user data in other tenant

    ## delete user
    g.db_session.delete(user)
    g.db_session.commit()

    return True



def delete_client_user(user, tenant_id=None):
    from clientmodels import FeedComment, Feed, FeedLike, FeedFlag, XircleMember, Xircle
    from clientmodels import Character
    from clientmodels import EntitlementGroupUserAssignment

    if not tenant_id:
        return False
    if tenant_id == USER_TENANT:
        return False
    if user.id == '********-0000-0000-0000-************':
        return False
    if g.db_session.query(Character).filter_by(user_id=user.id).first():
        return False
    
    ## delete all user related xircle, feed, entitlement group data
    ## get client tenant
    client_tenant_session = get_db_session(tenant_id)
    client_user = client_tenant_session.query(User).filter_by(id=user.id).first()
    if not client_user:
        return False
    user_id = client_user.id

    ## Clear User Feed Comments
    user_feed_comments = client_tenant_session.query(FeedComment).filter_by(user_id=user_id).all()
    for user_feed_comment in user_feed_comments:
        client_tenant_session.delete(user_feed_comment)

    ## Clear User Feed Likes
    user_feed_likes = client_tenant_session.query(FeedLike).filter_by(user_id=user_id).all()
    for user_feed_like in user_feed_likes:
        client_tenant_session.delete(user_feed_like)

    ## Clear User Feed Flags
    user_feed_flags = client_tenant_session.query(FeedFlag).filter_by(user_id=user_id).all()
    for user_feed_flag in user_feed_flags:
        client_tenant_session.delete(user_feed_flag)

    ## Clear User Feeds
    user_feeds = client_tenant_session.query(Feed).filter_by(user_id=user_id).all()
    for user_feed in user_feeds:
        client_tenant_session.delete(user_feed)

    ## Clear User Xircles
    user_xircles = client_tenant_session.query(XircleMember).filter_by(user_id=user_id).all()
    for user_xircle in user_xircles:
        client_tenant_session.delete(user_xircle)
    
    ## Clear User Xircle Created
    user_created_xircles = client_tenant_session.query(Xircle).filter_by(creator_id=user_id).all()
    for user_created_xircle in user_created_xircles:
        user_created_xircle.creator_id = "********-0000-0000-0000-************"

    ## Clear User Entitlement Groups
    user_entitlement_groups = client_tenant_session.query(EntitlementGroupUserAssignment).filter_by(user_id=user_id).all()
    for user_entitlement_group in user_entitlement_groups:
        client_tenant_session.delete(user_entitlement_group)
        

    client_tenant_session.commit()
    
    ## delete user
    client_tenant_session.delete(client_user)
    client_tenant_session.commit()

    return True


def get_user_entitlement_groups(user):
    """Get all entitlement groups that a user belongs to, including both direct assignments and directory mappings.
    
    Args:
        user: User object with entitlement_groups relationship loaded
        
    Returns:
        list: List of entitlement group IDs the user belongs to
    """
    from clientmodels import EntitlementGroup
    from sqlalchemy import or_

    try:
        # Get all entitlement groups in a single query using union of direct assignments and directory mappings
        query = g.client_db_session.query(EntitlementGroup).filter(
            or_(
                # Direct assignments through relationship
                EntitlementGroup.id.in_([group.id for group in user.entitlement_groups]),
                # Directory mapping based assignments
                EntitlementGroup.directory_mapping_key.isnot(None),
                EntitlementGroup.directory_mapping_key != '',
                EntitlementGroup.directory_mapping_value.isnot(None),
                EntitlementGroup.directory_mapping_value != ''
            )
        )

        # Use set for O(1) lookups
        result_ids = {group.id for group in user.entitlement_groups}
        
        # Process directory mappings
        for group in query.all():
            if (group.directory_mapping_key and group.directory_mapping_value and 
                getattr(user, group.directory_mapping_key, None) == group.directory_mapping_value):
                result_ids.add(group.id)

        return list(result_ids)

    except Exception as e:
        # Rollback the transaction to avoid breaking subsequent operations
        g.client_db_session.rollback()
        g.db_session.rollback()
        
        print(f"Error getting entitlement groups for user {user.id}: {str(e)}")
        logger.error(f"Error getting entitlement groups for user {user.id}: {str(e)}")
        return []
