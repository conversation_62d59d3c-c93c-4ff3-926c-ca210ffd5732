import logging
import os
from services.azure_communication import AzureCommunicationEmail

logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self):
        self.email_service = os.environ.get('EMAIL_SERVICE', 'azure')
        if self.email_service == 'azure':
            self.email_client = AzureCommunicationEmail()
        else:
            logger.error('Invalid email service')

    def send_email(self, to_email, subject, content, **kwargs):
        try:
            response = self.email_client.send_email(to_email, subject, content, **kwargs)
            return response
        except Exception as e:
            logger.error(f'Error sending email: {str(e)}')
            return None
        
    def send_invitation_email(self, user, **kwargs):
        try:
            to_email = user.email
            if not to_email:
                logger.error('User email is required')
                return None
            
            ## get email template from storage
            from app import storage
            
            template_name = 'invite.json'
            data = storage.download_file(f'email_template', template_name)
            if not data:
                logger.error('Failed to download email template')
                return None
            
            ## Parse JSON content
            try:
                import json
                content = json.loads(data.read().decode('utf-8'))
                subject = content.get('subject', None)
                body = content.get('body', None)
                sender = content.get('sender', None)
                if not subject or not body:
                    logger.error('Invalid email template')
                    return None
                
                ## replace placeholders
                body = body.replace('{{ user_name }}', user.first_name + ' ' + user.last_name)
                body = body.replace('{{ user_email }}', user.email)
                body = body.replace('{{ invite_link }}', kwargs.get('invite_link', ''))
                body = body.replace('{{ sender_name }}', kwargs.get('sender_name', ''))
                body = body.replace('{{ sender_email }}', kwargs.get('sender_email', ''))
                body = body.replace('{{ message }}', kwargs.get('message', ''))
                
                kwargs['subject'] = subject
                kwargs['content'] = body

                if sender:
                    kwargs['sender'] = sender

            except Exception as e:
                logger.error(f'Failed to parse email template: {str(e)}')
                return None

            response = self.email_client.send_email(to_email, **kwargs)
            return response
        except Exception as e:
            logger.error(f'Error sending invitation email: {str(e)}')
            return None
