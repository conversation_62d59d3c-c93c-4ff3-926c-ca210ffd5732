import functools
from flask import g

from api.common.helper import create_response
from clientmodels import Admin


def check_user_permission(access_level="user"):
    """Decorator to check user permissions
    
    Args:
        access_level (str): Required access level. Options are:
            - "user": Any authenticated user
            - "admin": User with admin or super_admin role
            - "super_admin": User with super_admin role only
    
    Returns:
        Function decorator that checks permissions before executing the wrapped function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check if user is authenticated
            user_id = getattr(g, 'user_id', None)
            if not user_id:
                return create_response("api.permission_denied", status=422)
                
            # For regular users, just being authenticated is enough
            if access_level == "user":
                pass
            # For admin and super_admin, check roles
            elif access_level in ["admin", "super_admin"]:
                try:
                    admin = g.db_session.query(Admin).filter_by(user_id=user_id).first()
                    role = admin.role if admin else None
                    
                    if access_level == "admin" and role not in ["admin", "super_admin"]:
                        return create_response("api.permission_denied", status=422)
                    elif access_level == "super_admin" and role != "super_admin":
                        return create_response("api.permission_denied", status=422)
                except Exception as e:
                    print(f"Error checking permissions: {str(e)}")
                    return create_response("api.permission_denied", status=422)
            
            # If all checks pass, execute the function
            return func(*args, **kwargs)
        return wrapper
    return decorator
