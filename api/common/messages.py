import json

class Messages:
    def __init__(self):
        try:
            with open('static/files/translation.json', 'r') as f:
                self.messages = json.load(f)
        except FileNotFoundError:
            self.messages = {}

    def get_message(self, message_key, language):
        message =  self.messages.get(message_key, {}).get(language, "")
        if not message:
            message = message_key

        return message

