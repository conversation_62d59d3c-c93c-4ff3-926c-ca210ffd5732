import datetime

import jwt
from flask import request, session, g

from api.common.helper import create_response
from clientmodels import User, UserToken, get_db_session, USER_TENANT

import logging


def add_middleware(app):
    @app.before_request
    def check_access_token():
        check_token_expired = True

        prefixes = ['/api/app', '/api/cms', '/api/account', '/api/file', '/api/test']
        if any(request.path.startswith(prefix) for prefix in prefixes):
            # List of endpoints to exclude
            excluded_prefixes = [
            '/api/app/document', 
            '/api/cms/document', 
            '/api/file/document',
            '/api/test/document',
            '/api/account/document', 
            '/api/account/authenticate',
            '/api/account/login',
            '/api/account/refresh_token',
            '/api/test/debug',
            '/api/app/global_settings/'
        ]
            # Check if the current endpoint is in the excluded list
            if any(request.path.startswith(excluded_prefix) for excluded_prefix in excluded_prefixes):
                return
            
            ## exclude swagger document
            if "swagger" in request.path:
                return
            
            if request.path.startswith("/api/account/logout") or request.path.startswith("/api/account/refresh_token"):
                check_token_expired = False
            
            # check user token
            authorization_header = request.headers.get('Authorization')
            token_expired = False
            if not authorization_header:
                logging.info("no header")
                return create_response("api.no_header", status=401)
            else:
                # Split the header into parts
                parts = authorization_header.split()
                
                # Check if the header is formed correctly
                if parts[0].lower() != 'bearer' or len(parts) == 1 or len(parts) > 2:
                    logging.info("header incorrect format")
                    return create_response('api.header_incorrect_format', status=401)
                
                token = parts[1]
                ## get jwt information from token if tenant is not set
                secret_key = app.config.get('SECRET_KEY')
                try:
                    decoded_token = jwt.decode(token, secret_key, algorithms=["HS256"], audience='api')
                except Exception as e:
                    print(e)
                    try:
                        decoded_token = jwt.decode(token, secret_key, algorithms=["HS256"], audience='api', options={"verify_signature": False})
                        token_expired = True
                    except Exception as e:
                        logging.info("token cannot be decoded")
                        return create_response("api.invalid_token_expire_jwt", status=401)
                        
                tenant_id = decoded_token.get("iss", "")
                if not tenant_id:
                    logging.info("tenant id is empty")
                    return create_response("api.invalid_tenant", status=401)
                else:
                    client_db_session = get_db_session(tenant_id)
                    # print(tenant_id)
                    user_db_session = get_db_session(USER_TENANT)
                    g.db_session = user_db_session
                    g.client_db_session = client_db_session
                    session['tenant_id_key'] = tenant_id
                    g.tenant_id = tenant_id
                
                user_token = user_db_session.query(UserToken).filter_by(access_token=token).first()
                if not user_token:
                    logging.info("token not exist in database")
                    logging.info(token)
                    return create_response("api.invalid_token_not_exist", status=401)
                else:  
                    if check_token_expired:
                        expired_time = user_token.date_expired
                        if datetime.datetime.utcnow() > expired_time or token_expired:
                            logging.info("token expired")
                            return create_response("api.token_expired", status=401)
                ## set user id in g
                g.user_id = user_token.user_id

                ## set user login type ex cms, web, app
                g.user_login_type = user_token.login_type

                ## check user status here, if user is not active or deleted, do not allow them to use the api
                user = user_db_session.query(User).filter_by(id=user_token.user_id).first()
                if not user:
                    logging.info("user not found")
                    return create_response("api.user_not_found", status=401)
                if not user.is_active:
                    if not request.path.startswith("/api/app/user/me"):
                        logging.info("user is not active")
                        return create_response("api.user_not_active_or_deleted", status=401)
                if user.is_deleted:
                    logging.info("user is deleted")
                    return create_response("api.user_not_active_or_deleted", status=401)

                ## save user token in session
                session['user_id'] = user_token.user_id
                ## get user timezone from header and try to save it in session
                timezone = request.headers.get('X-Timezone', '')
                if timezone:
                    g.timezone = timezone
        return

    @app.after_request
    def add_security_headers(response):
        # Add the X-Frame-Options header
        response.headers['X-Frame-Options'] = 'DENY'
        # Add other security headers if needed
        response.headers['Content-Security-Policy'] = (
            f"default-src 'self'; "
            f"script-src 'self' https://ajax.googleapis.com https://cdn.jsdelivr.net 'unsafe-inline'; "
            f"style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; "
            f"img-src 'self'; "
            f"connect-src 'self'; "
            f"font-src 'self' https://cdnjs.cloudflare.com; "
            f"frame-src 'self';"
        )
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['Referrer-Policy'] = 'no-referrer'

        return response