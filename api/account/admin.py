# admin.py
import datetime

from flask import g, request, session
from flask_restx import Namespace, Resource, fields

from api.account.authentication import generate_tokens
from api.common.decorator import check_user_permission
from api.common.helper import create_response
from clientmodels import Admin, User, Client, UserAssignment, UserToken, get_db_session
from clientmodels import CLIENT_TENANT, MASTER_TENANT

account_admin_api = Namespace('api_account_admin', description='Admin Account related operations')

## Super Admin switch client
switch_client_model = account_admin_api.model('SwitchClient', {
    'client_id': fields.String(required=True, description='Client ID')
})

@account_admin_api.doc(security='bearer')
@account_admin_api.route('/switch_client', endpoint='account.switch_client')
class SwitchClient(Resource):
    @account_admin_api.expect(switch_client_model)
    # @check_user_permission("super_admin")
    def post(self):
        ## get user information
        user_id = g.user_id
        client_id = request.json.get('client_id', '')

        ## get client information
        db_session = get_db_session(CLIENT_TENANT)

        tenant = db_session.query(Client).filter_by(id=client_id).first()
        if not tenant:
            return create_response("Invalid tenant", status=401)
        tenant_id = tenant.id_key
        client_id = tenant.id

        user = g.db_session.query(User).filter_by(id=user_id).first()
        first_name = user.first_name
        last_name = user.last_name
        email = user.email

        ## get the current tenant token and copy it to new tenant, then delete the old token
        authorization_header = request.headers.get('Authorization')
        parts = authorization_header.split()
        token = parts[1]
        user_token = g.db_session.query(UserToken).filter_by(access_token=token).first()
        if not user_token:
            return create_response("Invalid or expired token", status=401)
        
        device_id = user_token.device_id
        device_token = user_token.device_token
        device_type = user_token.device_type
        login_type = user_token.login_type

        ## remove old token
        g.db_session.delete(user_token)
        g.db_session.commit()


        ## if user is not super admin, sync user data to new tenant
        ## check if user is super admin
        admin = db_session.query(Admin).filter_by(user_id=user_id).first()
        role = admin.role if admin else None

        if role != "super_admin":
            ## check if user is assigned to the tenant
            user_assignment = g.db_session.query(UserAssignment).filter_by(user_id=user_id, client_id=client_id).first()
            if not user_assignment:
                return create_response("User is not assigned to the client", status=403)


            ## check if user account exist in new tenant, if not, create it and create associate admin account
            user = g.db_session.query(User).filter_by(email=email).first()
            try:
                user = g.db_session.query(User).filter_by(email=email).first()
                if not user:
                    user = User(first_name=first_name, last_name=last_name, email=email)
                    g.db_session.add(user)
                    g.db_session.commit()
            except Exception as e:
                g.db_session.rollback()
                user = g.db_session.query(User).filter_by(email=email).first()

            ## sync user data to new tenant
            from api.common.users_utils import sync_user
            sync_user(user, tenant_id)
        
        if not tenant_id or not user.id:
            return create_response("Invalid tenant or user", status=400)
        ## switch tenant
        session['tenant_id_key'] = tenant_id
        g.user_id = user.id

        ## generate new token
        try:
            token = generate_tokens(user.id, tenant_id)
        except Exception as e:
            return create_response("Failed to generate token", status=500)

        data = {
            "access_token": token[0],
            "refresh_token": token[1],
            "expires_time": token[2].strftime("%Y-%m-%d %H:%M:%S"),
            "user_id": user.id
        }

        ## save user token
        user_token = UserToken(
            user_id=user.id,
            access_token=token[0],
            refresh_token=token[1],
            date_expired=token[2],
            device_id=device_id,
            device_token=device_token,
            device_type=device_type,
            login_type=login_type
        )
        try:
            # Database operations
            g.db_session.add(user_token)
            g.db_session.commit()
        except Exception as e:
            g.db_session.rollback()
            return create_response("An error occurred while processing the request", status=500)

        cookie = {
            "access_token": token[0],
            "refresh_token": token[1],
            f"refresh_token_{login_type}": token[1],
            "expires_time": token[2].strftime("%Y-%m-%d %H:%M:%S"),
            "user_id": user.id
        }

        session['user_info'] = {
            "id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "access_token": token[0]
        }

        return create_response("Tenant switched successfully", data=data, cookie=cookie)



@account_admin_api.doc(security='bearer')
@account_admin_api.route('/clients', endpoint='account.clients')
class Clients(Resource):
    @check_user_permission("super_admin")
    def get(self):
        ## get user information
        user_id = g.user_id

        ## get client information
        db_session = get_db_session(CLIENT_TENANT)
        tenants = db_session.query(Client).all()

        data = []
        for tenant in tenants:
            data.append({
                "id": tenant.id,
                "name": tenant.name,
                "is_client": True if tenant.id_key != MASTER_TENANT else False
            })

        return create_response("Tenants retrieved successfully", data=data)