import logging
import mimetypes
from io import BytesIO

import werkzeug
from flask import request, send_file, g
from flask_restx import Namespace, Resource, reqparse
from PIL import Image

from api.common.helper import create_response
from api.common.file import FileService
from app import storage, cache
from clientmodels import Client, get_db_session
import clientmodels

# Configure logger
logger = logging.getLogger(__name__)

api_file = Namespace('api_file', description='File related operations')

file_upload_parser = reqparse.RequestParser()
file_upload_parser.add_argument('file', type=werkzeug.datastructures.FileStorage, location='files', required=True, help='File to upload')

@api_file.doc(security='bearer')
@api_file.route('<path:folder>/<filename>', methods=['GET'])
@api_file.route('', methods=['POST'])
class FileObject(Resource):
    def get(self, folder, filename):
        file_data = storage.download_file(folder, filename)
        if file_data:
            # Read the file data into bytes
            file_bytes = file_data.read()
            stream = BytesIO(file_bytes)
            content_type, _ = mimetypes.guess_type(filename)
            if content_type is None:
                content_type = 'application/octet-stream'
            # Get file size
            file_size = len(file_bytes)
            # Create response data
            response_data = {
                'size': file_size,
                'content_type': content_type
            }
            # If it's an image, get additional metadata
            if content_type and content_type.startswith('image/'):
                try:
                    # Create a copy of the stream for image processing
                    image_stream = BytesIO(file_bytes)
                    with Image.open(image_stream) as img:
                        response_data.update({
                            'width': img.width,
                            'height': img.height,
                            'format': img.format.lower()
                        })
                except Exception as e:
                    logger.error(f"Error getting image metadata: {str(e)}")
            # Reset stream position for file sending
            stream.seek(0)
            if content_type.startswith('application/'):
                return send_file(
                    stream, 
                    mimetype=content_type, 
                    as_attachment=True,
                    download_name=filename
                )
            else:
                response = send_file(
                    stream, 
                    mimetype=content_type, 
                    as_attachment=False
                )
                # Add metadata headers
                response.headers['X-File-Metadata'] = str(response_data)
                return response
        else:
            return create_response("api.file.not_found", 404)

    @api_file.expect(file_upload_parser, validate=True)
    @api_file.doc(description='Upload a file.')
    def post(self):
        try:
            args = file_upload_parser.parse_args()
            file_data = args.get('file')

            if not file_data:
                logger.error("No file provided in request")
                return create_response("No file provided", status=400)

            success, result = FileService.process_upload(file_data=file_data)

            if success:
                return create_response("File uploaded", data={"filename": result})
            else:
                logger.error(f"File upload failed - Error: {result}")
                return create_response(result, 400 if "size" in result or "type" in result else 500)
        except Exception as e:
            logger.exception("Unexpected error during file upload", exc_info=True)
            return create_response(f"Upload failed: {str(e)}", 500)


@api_file.route('signed_url/<path:folder>/<string:filename>', methods=['GET'])
class SignedUrl(Resource):
    @cache.cached(timeout=3300, key_prefix=lambda: f"{g.tenant_id}:signed_url:{request.path}")
    def get(self, folder, filename):
        try:
            signed_url = storage.get_signed_url(folder, filename)
            return create_response("Signed URL generated", data={"signed_url": signed_url})
        except Exception as e:
            logger.exception("Unexpected error generating signed URL", exc_info=True)
            return create_response(f"Failed to generate signed URL: {str(e)}", 500)


@api_file.route('update/<string:client_id>/<string:entity_type>/<int:index>', methods=['PUT'])
class UpdateFile(Resource):
    def put(self, client_id, entity_type, index):
        """generate the file the move to destination"""
        # set container name to xapabackenddev
        client_id = g.db_session.query(Client).filter_by(id=client_id).first()
        if not client_id:
            return create_response("Client not found", status=404)

        tenant_id = client_id.id_key
        storage.container_name = tenant_id

        size = 50
        offset = (index - 1) * size

        db_session = get_db_session(tenant_id)
        obj_class = getattr(clientmodels, entity_type)
        count = db_session.query(obj_class).count()
        batch = []
        try:
            # Use SELECT FOR UPDATE to lock specific rows
            # Note: Transaction is auto-begun by SQLAlchemy when first query is executed

            objs = db_session.query(obj_class).filter(
                obj_class.id.in_(
                    db_session.query(obj_class.id)
                    .order_by(obj_class.id.asc())
                    .offset(offset)
                    .limit(size)
                    .scalar_subquery()
                )
            ).with_for_update(nowait=False).all()

            for obj in objs:
                entity_type_lower = entity_type.lower()
                cache_prefix = f'file_process:{entity_type_lower}:{obj.id}'
                needs_update = False
                
                # Process 'image' attribute
                needs_update |= self._process_file_attribute(
                    obj, 'image', entity_type_lower, tenant_id, cache_prefix
                )
                
                # Process 'image_webapp' attribute
                needs_update |= self._process_file_attribute(
                    obj, 'image_webapp', entity_type_lower, tenant_id, cache_prefix
                )
                
                # Process 'file_path' attribute (only for download type)
                needs_update |= self._process_file_attribute(
                    obj, 'file_path', entity_type_lower, tenant_id, cache_prefix,
                    condition_check=lambda o: getattr(o, 'file_type', None) == 'download'
                )
                
                # if needs_update:
                #     batch.append(obj)

            if batch:
                try:
                    db_session.bulk_save_objects(batch)
                    db_session.commit()
                except Exception as e:
                    db_session.rollback()
                    logger.error(f"Error updating file in batch: {str(e)}")
                    return create_response(f"Error updating file in batch: {str(e)}", status=500)
            else:
                # No changes to commit, just end the transaction cleanly
                db_session.rollback()
            
            return create_response("File updated successfully", status=200, data={"has_next": count > index * size, "count": count})
        except Exception as e:
            db_session.rollback()
            logger.error(f"Error updating file: {str(e)}")
            return create_response(f"Error updating file: {str(e)}", status=500)
        finally:
            db_session.close()

    def _process_file_attribute(self, obj, attr_name, entity_type_lower, tenant_id, cache_prefix, condition_check=None):
        """
        Helper method to process a file attribute with caching
        
        Args:
            obj: The database object being processed
            attr_name: The attribute name to process ('image', 'image_webapp', 'file_path')
            entity_type_lower: Lowercase entity type
            tenant_id: Tenant ID
            cache_prefix: Base cache key prefix
            condition_check: Optional function to check if processing should proceed
            
        Returns:
            bool: True if the object was updated, False otherwise
        """
        # Skip if condition check fails
        if condition_check and not condition_check(obj):
            return False
            
        # Get original path and skip if None
        original_path = getattr(obj, attr_name, None)
        if not original_path:
            return False
            
        # Create cache keys
        attr_suffix = attr_name.replace('_', '_')
        original_key = f'{cache_prefix}:original_{attr_suffix}'
        new_key = f'{cache_prefix}:new_{attr_suffix}'
        needs_update = False
        
        if tenant_id == 'global':
            # For global tenant, always process and cache the result
            cache.set(key=original_key, value=original_path, timeout=3600)
            success, new_path = FileService.process_model_image(original_path, entity_type_lower, obj.id)
            if success and new_path != original_path:
                setattr(obj, attr_name, new_path)
                cache.set(key=new_key, value=new_path, timeout=3600)
                needs_update = True
        else:
            # For non-global tenants, check cache first
            cached_original = cache.get(original_key) if cache.has(original_key) else None
            
            if cached_original and cached_original != original_path:
                # Process if original has changed from cached version
                success, new_path = FileService.process_model_image(original_path, entity_type_lower, obj.id)
                if success and new_path != original_path:
                    setattr(obj, attr_name, new_path)
                    needs_update = True
            elif cache.has(new_key):
                # Use cached new path if available
                cached_new_path = cache.get(new_key)
                new_path = FileService.copy_to_destination('global', cached_new_path, tenant_id, cached_new_path)
                if new_path and new_path != original_path:
                    setattr(obj, attr_name, new_path)
                    needs_update = True
                    
        return needs_update
