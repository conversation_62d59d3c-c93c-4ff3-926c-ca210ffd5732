import os

from flask import g
from flask_restx import Namespace, Resource
from sqlalchemy import func

from api.common.helper import create_response
from api.common.users_utils import get_user_entitlement_groups
from clientmodels import MASTER_TENANT, Character, Node, Program, Category, ProgramQuestAssociation, Quest, ProgramCategoryAssociation, \
    User, UserProgram, UserSpending, UserStats
from clientmodels import ProgramDivider, Chest
from clientmodels import EntitlementGroupProgramAssignment
from clientmodels import PublishedProgram, Client, ClientPackageAssociation, PackageProgramAssociation
from clientmodels import PublishedQuest

app_program_api = Namespace('api_app_program', description='Program related API')

programs_parser = app_program_api.parser()
programs_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
programs_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
programs_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')

@app_program_api.doc(security='bearer')
@app_program_api.route('/list')
class ProgramsList(Resource):
    @app_program_api.expect(programs_parser)
    def get(self):
        args = programs_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        offset = (page - 1) * limit

        query = get_program_query(search=search, user_id=g.user_id)
    
        total, programs = paginate_query(query, page, limit)

        if not programs:
            return create_response("No programs found", status=404)
        
        data = [transform_program_data(program, published_program) for program, published_program in programs]

        return create_response("Programs List", data=data, page=page, limit=limit, total=total)
        


@app_program_api.doc(security='bearer')
@app_program_api.route('/list/category')
class ProgramsListByCategory(Resource):
    def get(self):
        data = []
        categories = g.client_db_session.query(Category).all()
        ## sory by order
        categories.sort(key=lambda c: c.order)

        for category in categories:
            category_dict = category.to_dict()
            category_dict["program_list"] = []

            category_id = category.id
            ## get programs by category
            programs = get_program_query(category_id=category_id, user_id=g.user_id)

            ## get programs by category
            category_dict["program_list"] = [
                {**transform_program_data(program, published_program, g.user_id), "order": order}
                for program, published_program, order in programs
            ]

            ## if no programs in category, skip this category
            if not category_dict["program_list"]:
                continue

            ## sort programs by order
            category_dict["program_list"].sort(key=lambda p: (p['order'] is None, p['order']))

            data.append(category_dict)

        return create_response("Programs List by Category", data=data)
    

@app_program_api.doc(security='bearer')
@app_program_api.route('/<string:program_id>')
class ProgramDetail(Resource):
    def get(self, program_id):
        program = g.client_db_session.query(Program).filter_by(id=program_id, status="Published").first()
        if not program:
            return create_response("Program not found", status=404)
        
        ## check if user has access to xperience
        user_id = g.user_id
        if user_id:
            user = g.db_session.query(User).filter(User.id == user_id).first()
            if user:
                entitlement_groups = get_user_entitlement_groups(user)
                if entitlement_groups:
                    assigned_entitlement_group_ids = entitlement_groups
                    excluded_program_ids = g.client_db_session.query(EntitlementGroupProgramAssignment.program_id).filter(
                        ~EntitlementGroupProgramAssignment.entitlement_group_id.in_(assigned_entitlement_group_ids)
                    ).distinct().all()
                    excluded_program_ids = [program_id[0] for program_id in excluded_program_ids]
                    if program.id in excluded_program_ids:
                        return create_response("Program not available", status=403)

        program_dict = program.to_dict()
        program_dict['category_list'] = [category.to_dict() for category in program.categories]
        program_dict['tag_list'] = [tag.to_dict() for tag in program.tags]
        program_dict['facet_list'] = [facet.to_dict() for facet in program.facets]

        program_dict['dividers'] = []
        dividers = g.client_db_session.query(ProgramDivider).filter_by(program_id=program.id).all()
        for divider in dividers:
            divider_dict = divider.to_dict()
            quests = get_program_divider_quests(divider, user_id=g.user_id)
            divider_dict['quests'] = quests
            program_dict['dividers'].append(divider_dict)

        ## sort dividers by order
        program_dict['dividers'].sort(key=lambda d: (d['order'] is None, d['order']))

        chest_id = program.chest_id
        if chest_id:
            chest = g.client_db_session.query(Chest).filter(Chest.id == chest_id).first()
        else:
            chest = None

        if chest:
            program_dict['xp'] = chest.xp
        else:
            program_dict['xp'] = 0

        ## go through program dividers and add total xp from quests inside the dividers
        total_xp = program_dict['xp']
        for divider in program_dict['dividers']:
            for quest in divider['quests']:
                total_xp += quest['xp']
        program_dict['xp'] = total_xp

        ## get cast
        cast = get_program_cast(program_dict)
        program_dict['cast'] = cast

        return create_response("Program Detail", data=program_dict)
    


@app_program_api.doc(security='bearer')
@app_program_api.route('/<string:program_id>/cast')
class XperienceCast(Resource):
    def get(self, program_id):
        program = g.client_db_session.query(Program).filter_by(id=program_id, status="Published").first()
        if not program:
            return create_response("Program not found", status=404)

        program_dict = program.to_dict()
        program_dict['dividers'] = []
        dividers = g.client_db_session.query(ProgramDivider).filter_by(program_id=program.id).all()
        for divider in dividers:
            divider_dict = divider.to_dict()
            quests = get_program_divider_quests(divider, user_id=g.user_id)
            divider_dict['quests'] = quests
            program_dict['dividers'].append(divider_dict)

        cast = get_program_cast(program_dict)
        return create_response("Xperience cast retrieved successfully", data=cast)


def get_program_divider_quests(divider, user_id):
    # Get all ProgramQuestAssociations for this divider, ordered
    associations = g.db_session.query(ProgramQuestAssociation).filter_by(
        program_divider_id=divider.id
    ).order_by(ProgramQuestAssociation.order).all()
    quest_ids = [assoc.quest_id for assoc in associations]

    # Fetch all PublishedQuests in one query
    published_quests = g.db_session.query(PublishedQuest).filter(
        PublishedQuest.quest_id.in_(quest_ids)
    ).all()
    published_quest_map = {q.quest_id: q for q in published_quests}

    # Fetch all chests in one query
    chest_ids = [q.chest_id for q in published_quests if q.chest_id]
    chests = g.client_db_session.query(Chest).filter(Chest.id.in_(chest_ids)).all()
    chest_map = {c.id: c for c in chests}

    quests = []
    for order, quest_id in enumerate(quest_ids, start=1):
        quest = published_quest_map.get(quest_id)
        if not quest:
            continue
        quest_dict = quest.to_dict(["id", "name", "description", "image", "level"])
        quest_dict['order'] = quest_ids.index(quest_id) + 1
        
        chest_id = quest.chest_id
        if chest_id:
            chest = g.client_db_session.query(Chest).filter(Chest.id == chest_id).first()
        else:
            chest = None

        if chest:
            quest_dict['xp'] = chest.xp
        else:
            quest_dict['xp'] = 0

        quests.append(quest_dict)

    return quests



def get_program_cast(program_data):
    quest_ids = []
    for divider in program_data['dividers']:
        for quest in divider['quests']:
            quest_ids.append(quest['id'])

    # Get all nodes and transcripts in one query using joins
    nodes_with_transcripts = (
        g.client_db_session.query(Node)
        .filter(Node.quest_id.in_(quest_ids))
        .all()
    )

    # Extract unique character IDs
    character_ids = set()
    for node in nodes_with_transcripts:
        if node.transcripts:  # Check if transcripts exist
            for transcript in node.transcripts:
                if transcript and transcript.character_id:  # Add null checks
                    character_ids.add(transcript.character_id)

    # Get all characters in one query
    characters = (
        g.db_session.query(Character, User)
        .join(User, Character.user_id == User.id)
        .filter(Character.key.in_(character_ids))
        .all()
    )

    # Build cast list
    cast = []
    for character, user in characters:
        if user:
            user_dict = user.to_dict(["id", "first_name", "last_name", "preferred_name", "image", "phone_number", "email", "company", "title", "bio"])
            user_dict['character'] = character.to_dict()
            cast.append(user_dict)

    return cast



## unlock program
@app_program_api.doc(security='bearer')
@app_program_api.route('/unlock/<string:program_id>')
class UnlockProgram(Resource):
    def post(self, program_id):
        program = g.client_db_session.query(Program).filter_by(id=program_id).first()
        if not program:
            return create_response("Program not found", status=404)

        user_id = g.user_id
        program_id = program.id

        program_unlocked = g.client_db_session.query(Program).filter_by(id=program_id, status="Published").first()
        if not program_unlocked:
            return create_response("Program not found", status=404)

        user = g.db_session.query(User).filter_by(id=user_id).first()
        if not user:
            return create_response("User not found", status=404)

        keys_required = program.keys_required

        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)

        user_stats.keys_count = user_stats.keys_count if user_stats.keys_count else 0
        if user_stats.keys_count < keys_required:
            return create_response("Not enough keys to unlock this program", status=403)
        
        user_stats.keys_count -= keys_required
        g.db_session.commit()

        user_programs = g.db_session.query(UserProgram).filter_by(user_id=user_id, program_id=program_id).first()
        if not user_programs:
            user_programs = UserProgram(user_id=user_id, program_id=program_id)
            g.db_session.add(user_programs)
            g.db_session.commit()
        else:
            if user_programs.is_unlocked:
                return create_response("Program already unlocked", status=403)

        user_programs.is_unlocked = True
        g.db_session.commit()

        ## save keys used in user spending table
        user_spending = UserSpending(user_id=user_id, keys=keys_required, source="program", source_id=program_id)
        g.db_session.add(user_spending)
        g.db_session.commit()
        
        return create_response("Program unlocked successfully", data=program_unlocked.to_dict())



def get_program_query(category_id=None, search=None, user_id=None):
    """
    Returns a query for (Program, PublishedProgram[, order]) tuples,
    filtered by client, search, user entitlement, and optionally category.
    If category_id is provided, includes ProgramCategoryAssociation.order in the result.
    If g.tenant_id == MASTER_TENANT, returns all published programs without client filtering.
    """
    if g.tenant_id == MASTER_TENANT:
        # No client filtering, return all published programs
        if category_id:
            query = (
                g.db_session.query(Program, PublishedProgram, ProgramCategoryAssociation.order)
                .join(ProgramCategoryAssociation, Program.id == ProgramCategoryAssociation.program_id)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(
                    ProgramCategoryAssociation.category_id == category_id,
                    PublishedProgram.is_latest == True
                )
            )
        else:
            query = (
                g.db_session.query(Program, PublishedProgram)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(PublishedProgram.is_latest == True)
            )
    else:
        client = g.db_session.query(Client).filter(Client.id_key == g.tenant_id).first()
        if not client:
            return []

        # Get all package ids associated with the client
        client_package_ids = g.db_session.query(ClientPackageAssociation.package_id).filter(
            ClientPackageAssociation.client_id == client.id
        ).subquery()

        # Get all program ids associated with those packages
        package_program_ids = g.db_session.query(PackageProgramAssociation.program_id).filter(
            PackageProgramAssociation.package_id.in_(client_package_ids.select())
        ).subquery()

        if category_id:
            query = (
                g.db_session.query(Program, PublishedProgram, ProgramCategoryAssociation.order)
                .join(ProgramCategoryAssociation, Program.id == ProgramCategoryAssociation.program_id)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(
                    ProgramCategoryAssociation.category_id == category_id,
                    PublishedProgram.is_latest == True,
                    Program.id.in_(package_program_ids.select())
                )
            )
        else:
            query = (
                g.db_session.query(Program, PublishedProgram)
                .join(PublishedProgram, Program.id == PublishedProgram.program_id)
                .filter(
                    PublishedProgram.is_latest == True,
                    PublishedProgram.program_id.in_(package_program_ids.select()),
                )
            )

    # Entitlement group filtering
    if user_id:
        user = g.db_session.query(User).filter(User.id == user_id).first()
        if user:
            entitlement_group_ids = get_user_entitlement_groups(user)
            if entitlement_group_ids:
                restricted_program_subquery = (
                    g.client_db_session.query(EntitlementGroupProgramAssignment.program_id)
                    .filter(~EntitlementGroupProgramAssignment.entitlement_group_id.in_(entitlement_group_ids))
                    .group_by(EntitlementGroupProgramAssignment.program_id)
                    .having(func.count(EntitlementGroupProgramAssignment.entitlement_group_id) > 0)
                    .subquery()
                )
                query = query.filter(~Program.id.in_(restricted_program_subquery.select()))

    # Search filtering
    if search:
        query = query.filter(Program.name.ilike(f'%{search}%'))

    # Ordering
    if category_id:
        query = query.order_by(ProgramCategoryAssociation.order)

    return query



def paginate_query(query, page, limit):
    ## add exception if query is empty
    if not query:
        return 0, []
    ## add exception if page or limit is not valid
    if page < 1 or limit < 1:
        return 0, []
    ## add exception if page or limit is not valid
    if page > 1000 or limit > 1000:
        return 0, []
    
    offset = (page - 1) * limit
    total = query.count()
    items = query.offset(offset).limit(limit).all()
    return total, items



def transform_program_data(program, published_program, user_id=None):
    if not program or not published_program:
        return {}

    program_dict = published_program.to_dict()
    program_dict['id'] = program.id
    program_dict['category_list'] = [category.to_dict() for category in program.categories]
    program_dict['tag_list'] = [tag.to_dict() for tag in program.tags]
    program_dict['facet_list'] = [facet.to_dict() for facet in program.facets]

    # Fetch all dividers at once
    dividers = g.db_session.query(ProgramDivider).filter_by(program_id=program.id).all()
    program_dict['dividers'] = []
    for divider in sorted(dividers, key=lambda d: (d.order is None, d.order)):
        divider_dict = divider.to_dict()
        divider_dict['quests'] = get_program_divider_quests(divider, user_id=user_id)
        program_dict['dividers'].append(divider_dict)

    # Fetch chest once
    chest = g.db_session.query(Chest).filter(Chest.id == program.chest_id).first() if program.chest_id else None
    program_dict['xp'] = chest.xp if chest else 0

    # Add up all quest XP
    total_xp = program_dict['xp']
    for divider in program_dict['dividers']:
        for quest in divider['quests']:
            total_xp += quest['xp']
    program_dict['xp'] = total_xp

    return program_dict