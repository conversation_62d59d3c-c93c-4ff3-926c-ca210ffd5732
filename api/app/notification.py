import datetime

from flask import g
from flask_restx import Namespace, Resource, reqparse

from api.common.helper import create_response
from api.common.helper import format_datetime_with_timezone
from clientmodels import UserNotification, Notification

app_notification_api = Namespace('api_app_notifications', description='App notification operations')

# Parser for list endpoint
list_parser = reqparse.RequestParser()
list_parser.add_argument('page', type=int, help='Page Number', default=1)
list_parser.add_argument('limit', type=int, help='Limit per page', default=20)

@app_notification_api.route('/list')
class NotificationList(Resource):
    @app_notification_api.expect(list_parser)
    def get(self):
        """Get user's notifications"""
        try:
            args = list_parser.parse_args()
            page = args.get('page', 1)
            limit = args.get('limit', 20)

            # Calculate offset
            offset = (page - 1) * limit
            
            # Build query
            query = g.db_session.query(
                UserNotification,
                Notification
            ).join(
                Notification,
                Notification.id == UserNotification.notification_id
            ).filter(
                UserNotification.user_id == g.user_id,
                UserNotification.status == 'sent',
                UserNotification.is_deleted == False
            ).order_by(
                UserNotification.date_created.desc()
            )
            
            # Get total count
            total = query.count()
            
            # Get paginated results
            notifications = query.offset(offset).limit(limit).all()
            
            # Format response
            data = []
            for user_notification, notification in notifications:
                data.append({
                    'id': user_notification.notification_id,
                    'title': notification.title,
                    'body': notification.body,
                    'data': notification.data,
                    'date_created': format_datetime_with_timezone(notification.date_created),
                    'update_badge': notification.update_badge,
                    'enable_notification_push': notification.enable_notification_push,
                    'is_read': user_notification.is_read
                })
            
            return create_response("Notification list", data=data, total=total, page=page, limit=limit)
            
        except Exception as e:
            return create_response(str(e), status=500)

@app_notification_api.route('/<notification_id>/read')
class NotificationRead(Resource):
    def put(self, notification_id: str):
        """Mark a notification as read"""
        try:
            # Find the user notification
            user_notifications = g.db_session.query(UserNotification).filter(
                UserNotification.notification_id == notification_id,
                UserNotification.user_id == g.user_id,
                UserNotification.status == 'sent',
                UserNotification.is_read == False,
                UserNotification.is_deleted == False
            )
            
            if user_notifications.count() == 0:
                return create_response("Unread notification not found", status=404)
            
             # Filter notifications that have update_badge set to True
            # badge_count = user_notifications.join(UserNotification.notification).filter(
            #     Notification.update_badge == True,
            #     Notification.is_deleted == False
            # ).count()

            # Mark as read
            user_notifications.update({
                'is_read': True,
                'date_updated': datetime.datetime.utcnow()
            })
            g.db_session.commit()

            # Update badge count
            # if badge_count > 0:
            #     nf = NotificationFunction()
            #     nf.update_badge_count(g.user_id)
            
            return create_response("Notification marked as read")
            
        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

@app_notification_api.route('/read-all')
class NotificationReadAll(Resource):
    def put(self):
        """Mark all notifications as read"""
        try:
            # Update all unread notifications
            user_notifications = g.db_session.query(UserNotification).filter(
                UserNotification.user_id == g.user_id,
                UserNotification.status == 'sent',
                UserNotification.is_read == False,
                UserNotification.is_deleted == False
            )

            if user_notifications.count() == 0:
                return create_response("Unread notifications not found", status=200)

            # Filter notifications that have update_badge set to True
            # badge_count = user_notifications.join(UserNotification.notification).filter(
            #     Notification.update_badge == True,
            #     Notification.is_deleted == False
            # ).count()

            user_notifications.update({
                'is_read': True,
                'date_updated': datetime.datetime.utcnow()
            })
            
            g.db_session.commit()

            # Update badge count
            # if badge_count > 0:
            #     nf = NotificationFunction()
            #     nf.update_badge_count(g.user_id)

            return create_response("All notifications marked as read")
            
        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

@app_notification_api.route('/delete-all')
class NotificationDeleteAll(Resource):
    def delete(self):
        """Delete all notifications"""
        try:
            # Update all unread notifications
            user_notifications = g.db_session.query(UserNotification).filter(
                UserNotification.user_id == g.user_id,
                UserNotification.is_deleted == False
            )

            user_notifications.update({
                'is_deleted': True
            })
            
            g.db_session.commit()

            return create_response("All notifications deleted")
        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

@app_notification_api.route('/badge-read-all')
class NotificationBadgeReadAll(Resource):
    def put(self):
        """Mark all notifications as badge read"""
        try:
            # Update all unread notifications for the user
            g.db_session.query(UserNotification).filter(
                UserNotification.user_id == g.user_id,
                UserNotification.status == 'sent',
                UserNotification.is_badge_read == False
            ).update({
                'is_badge_read': True
            })

            g.db_session.commit()

            # Update badge count
            # nf = NotificationFunction()
            # nf.update_badge_count(g.user_id)

            return create_response('All notifications marked as badge read')
        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

@app_notification_api.route('/<string:notification_id>')
class NotificationDelete(Resource):
    def delete(self, notification_id: str):
        """Delete a specific notification"""
        try:
            # Find the user notification
            user_notification = g.db_session.query(UserNotification).filter(
                UserNotification.notification_id == notification_id,
                UserNotification.user_id == g.user_id,
                UserNotification.is_deleted == False
            ).first()
            
            if not user_notification:
                return create_response("Notification not found", status=404)
            
            # Delete the user notification
            user_notification.delete()
            g.db_session.commit()

            # Update badge count
            # nf = NotificationFunction()
            # nf.update_badge_count(g.user_id)

            return create_response("Notification deleted successfully")
        
        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

@app_notification_api.route('/<notification_id>/unread')
class NotificationUnread(Resource):
    def put(self, notification_id: str):
        """Mark a notification as unread"""
        try:
            # Find the user notification
            user_notification = g.db_session.query(UserNotification).filter(
                UserNotification.notification_id == notification_id,
                UserNotification.user_id == g.user_id,
                UserNotification.status == 'sent',
                UserNotification.is_read == True,
                UserNotification.is_deleted == False
            ).first()
            
            if not user_notification:
                return create_response("Read notification not found", status=404)
            
            # Mark as unread
            user_notification.is_read = False
            g.db_session.commit()
            
            return create_response("Notification marked as unread")
            
        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

@app_notification_api.route('/unread-count')
class NotificationUnreadCount(Resource):
    def get(self):
        """Get the count of unread notifications"""
        try:
            # Count unread notifications
            unread_count = g.db_session.query(UserNotification).filter(
                UserNotification.user_id == g.user_id,
                UserNotification.status == 'sent',
                UserNotification.is_read == False,
                UserNotification.is_deleted == False
            ).count()

            return create_response("Unread notification count", data={'unread_count': unread_count})
        except Exception as e:
            return create_response(str(e), status=500)
