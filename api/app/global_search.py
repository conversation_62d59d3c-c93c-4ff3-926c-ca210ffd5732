from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from clientmodels import Program, Tag, Xperience, Quest, User, Xircle

app_global_search_api = Namespace('api_app_global_search', description='Global Search related API')

search_model = app_global_search_api.model('Search', {
    'search': fields.String(required=True, description='Search term'),
})

@app_global_search_api.route('/search')
class Search(Resource):
    @app_global_search_api.expect(search_model)
    def post(self):
        search = request.json.get('search', '')
        if not search:
            return create_response("Search term is required", status=400)

        data = []

        from sqlalchemy import func
        
        # xperiences = g.client_db_session.query(Xperience).filter(Xperience.status == 'Published').filter(Xperience.name.ilike(f'%{search}%')).all()
        if not search:
            xperiences = g.client_db_session.query(Xperience).filter(Xperience.status == 'Published').all()
        else:
            xperiences = g.client_db_session.query(Xperience).filter(
                Xperience.status == 'Published',
                (Xperience.name.ilike(f'%{search}%') | Xperience.tags.any(Tag.name.ilike(f'%{search}%')))
            ).all()
        
        if xperiences:
            result = {
                'title': 'Xperience',
                'type': 'xperience',
            }
            items = []
            for xperience in xperiences:
                xperience_dict = xperience.to_dict(['id', 'name', 'image'])
                xperience_dict['action'] = f'/xperiences/xperience/{xperience.id}'
                items.append(xperience_dict)

            result['items'] = items

            data.append(result)

        if not search:
            programs = g.client_db_session.query(Program).filter(Program.status == 'Published').all()
        else:
            programs = g.client_db_session.query(Program).filter(
                Program.status == 'Published',
                (Program.name.ilike(f'%{search}%') | Program.tags.any(Tag.name.ilike(f'%{search}%')))
            ).all()

        if programs:
            result = {
                'title': 'Program',
                'type': 'program',
            }
            items = []
            for program in programs:
                program_dict = program.to_dict(['id', 'name', 'image'])
                program_dict['action'] = f'/xperiences/program/{program.id}'
                items.append(program_dict)

            result['items'] = items

            data.append(result)

        if not search:
            quests = g.client_db_session.query(Quest).filter(Quest.status == 'Ready').all()
        else:
            quests = g.client_db_session.query(Quest).filter(
                Quest.status == 'Ready',
                (Quest.name.ilike(f'%{search}%') | Quest.tags.any(Tag.name.ilike(f'%{search}%')))
            ).all()

        if quests:
            result = {
                'title': 'Quest',
                'type': 'quest',
            }
            items = []
            
            for quest in quests:
                quest_dict = quest.to_dict(['id', 'name', 'image'])
                quest_dict['action'] = f'/xperiences/quest/{quest.id}'
                ## get the quest first xperience
                xperiences = quest.xperiences
                if xperiences:
                    xperience = xperiences[0]
                    quest_dict['action'] = f'/xperiences/xperience/{xperience.id}?quest_id={quest.id}'
                    items.append(quest_dict)
                else:
                    continue

            result['items'] = items

            data.append(result)

        users = g.client_db_session.query(User).filter(
            User.is_deleted == False,
            User.is_active == True,
            func.concat(User.first_name, ' ', User.last_name).ilike(f"%{search}%") | 
            User.email.ilike(f"%{search}%") | 
            User.preferred_name.ilike(f"%{search}%")
        ).all()
        if users:
            result = {
                'title': 'People',
                'type': 'user',
            }
            items = []
            for user in users:
                name = f"{user.first_name} {user.last_name}"
                preferred_name = user.preferred_name
                user_dict = {
                    'id': user.id,
                    'name': name if search.lower() in name.lower() else preferred_name,
                    'image': user.image
                }
                user_dict['action'] = f'/people/{user.id}'
                items.append(user_dict)

            result['items'] = items

            data.append(result)

        xircles = g.client_db_session.query(Xircle).filter(Xircle.is_public == True, Xircle.is_deleted == False).filter(Xircle.name.ilike(f'%{search}%')).all()
        if xircles:
            result = {
                'title': 'Xircle',
                'type': 'xircle',
            }
            items = []
            for xircle in xircles:
                xircle_dict = xircle.to_dict(['id', 'name', 'image'])
                xircle_dict['action'] = f'/community?xircle_id={xircle.id}#feed'
                items.append(xircle_dict)

            result['items'] = items

            data.append(result)
        
        return create_response("Search results retrieved successfully", data=data)