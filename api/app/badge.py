import json

from flask import g
from flask_restx import Namespace
from sqlalchemy import String

from clientmodels import Badge, UserCoins, UserGems, UserKeys, UserStats, UserXP, XperienceBadgeAssociation, \
    ProgramBadgeAssociation, UserBadge

app_badge_api = Namespace('api_app_badge', description='Badge related operations')


def check_badge(badge_type, condition, source=None, source_id=None):
    badges = []
    if badge_type == '':
        query = g.db_session.query(Badge).filter(
            Badge.achievement_conditions.cast(String).ilike(f'%{condition}%')
        )
        milestone_badges = query.all()
        for milestone_badge in milestone_badges:
            badge_condition = json.loads(milestone_badge.achievement_conditions) if milestone_badge.achievement_conditions else {}
            if badge_condition.get('condition') == condition:
                badges.append(milestone_badge)
                break
          
    elif badge_type == 'xperience':
        query = g.client_db_session.query(XperienceBadgeAssociation).filter_by(xperience_id=source_id).all()
        for item in query:
            badge = g.client_db_session.query(Badge).filter_by(id=item.badge_id).first()
            badges.append(badge)
            
    elif badge_type == 'program':
        query = g.client_db_session.query(ProgramBadgeAssociation).filter_by(program_id=source_id).all()
        for item in query:
            badge = g.client_db_session.query(Badge).filter_by(id=item.badge_id).first()
            badges.append(badge)

    elif badge_type == 'streak':
        query = g.db_session.query(Badge).filter(Badge.achievement_type == 'streak')
        streak_badges = query.all()
        for streak_badge in streak_badges:
            badge_condition = json.loads(streak_badge.achievement_conditions) if streak_badge.achievement_conditions else {}
            if badge_condition.get('days') <= condition:
                badges.append(streak_badge)
                
    
    data = []
    for badge in badges:
        ## Check if user has already earned the badge
        user_badge = g.db_session.query(UserBadge).filter_by(user_id=g.user_id, badge_id=badge.id).first()
        if user_badge:
            return None
        
        ## create user badge record
        user_badge = UserBadge()
        user_badge.user_id = g.user_id
        user_badge.badge_id = badge.id
        
        g.db_session.add(user_badge)
        g.db_session.commit()

        data.append(badge.to_dict())

        ## if badge has reward, add reward to user
        rewards = json.loads(badge.rewards) if badge.rewards else {}
        if rewards:
            user_id = g.user_id
            user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
            if not user_stats:
                user_stats = UserStats(user_id=user_id)
                g.db_session.add(user_stats)
                g.db_session.commit()

            for key, value in rewards.items():
                if key == 'gems':
                    user_stats.gems_count = user_stats.gems_count if user_stats.gems_count else 0
                    user_stats.gems_count += value

                    user_gems = UserGems(user_id=user_id, value=value, source='badge', source_id=badge.id)
                    g.db_session.add(user_gems)

                elif key == 'coins':
                    user_stats.coins_count = user_stats.coins_count if user_stats.coins_count else 0
                    user_stats.coins_count += value

                    user_coins = UserCoins(user_id=user_id, value=value, source='badge', source_id=badge.id)
                    g.db_session.add(user_coins)

                elif key == 'xp':
                    user_stats.xp_count = user_stats.xp_count if user_stats.xp_count else 0
                    user_stats.xp_count += value

                    user_xp = UserXP(user_id=user_id, value=value, source='badge', source_id=badge.id)
                    g.db_session.add(user_xp)

                elif key == 'keys':
                    user_stats.keys_count = user_stats.keys_count if user_stats.keys_count else 0
                    user_stats.keys_count += value

                    user_keys = UserKeys(user_id=user_id, value=value, source='badge', source_id=badge.id)
                    g.db_session.add(user_keys)

            g.db_session.commit()
            
    
    if len(data) == 1:
        return data[0]
    else:
        return data

