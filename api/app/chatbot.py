from flask import g, json, request, session
from flask_restx import Namespace, Resource, fields
import requests
from app import app

from api.common.helper import create_response

app_chatbot_api = Namespace('api_app_chatbot', description='Chatbot related operations')


chatbot_model = app_chatbot_api.model('ChatbotConfig', {
    'chatInput': fields.String(required=True, description='Input for the chatbot'),
})

CHATBOT_WEBHOOK_URL = app.config.get("CHATBOT_WEBHOOK_URL", "")

@app_chatbot_api.doc(security='bearer')
@app_chatbot_api.route('/')
class Chatbot(Resource):
    @app_chatbot_api.expect(chatbot_model)
    def post(self):
        """
        Update the chatbot configuration.
        """
        data = request.get_json()
        if not data:
            return create_response({'error': 'No data provided'}, status=400)

        chat_input = data.get('chatInput')
        if not chat_input:
            return create_response({'error': 'chatInput is required'}, status=400)
        
        # Send data to n8n webhook
        n8n_webhook_url = CHATBOT_WEBHOOK_URL  
        ## create a unique session ID for the session
        chat_session_id = session.get('chat_session_id', None)
        if not chat_session_id:
            ## create a new session ID of random characters
            import random
            import string
            session_id = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
            session['chat_session_id'] = session_id
            chat_session_id = session_id

        try:
            headers = {
                "Content-Type": "application/json"
            }
            n8n_response = requests.post(
                n8n_webhook_url,
                json={
                    "chatInput": chat_input,
                    "sessionId": chat_session_id
                },
                timeout=60,
                headers=headers
            )
            ## get the response from n8n
            n8n_data = n8n_response.json()
            print(f'n8n response: {n8n_data}')
            output = n8n_data.get('output', "")

            ## after we get the output, 
            # check the links inside the output 
            # if it matches "https://gostage.xapa.ai/#/xperiences/xperience/{xperience_id}" 
            # and g.user_login_type is app, the replace the link with "xapa://page/xperiences/xperience/{xperience_id}"
            if output:
                if g.user_login_type == 'app':
                    import re
                    pattern = r'https://gostage\.xapa\.ai/#/xperiences/xperience/([a-zA-Z0-9\-]+)'
                    output = re.sub(pattern, r'/xperiences/xperience/\1', output)

        except requests.RequestException as e:
            print(f'Error calling n8n webhook: {str(e)}')
            data = {
                'output': "Error processing request, please try again later."
            }
            return create_response('Error processing chatbot request', status=500)
        
        data = {
            'output': output
        }
        return create_response("Chatbot response processed successfully", data=data)
    


