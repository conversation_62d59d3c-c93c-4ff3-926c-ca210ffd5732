import os

from flask import g, json, request
from flask_restx import Namespace, Resource
from sqlalchemy import func

from api.common.helper import create_response
from api.common.users_utils import get_user_entitlement_groups
from clientmodels import Category, Character, Node, Tag, User, UserSpending, UserStats, UserXperience, Xperience, Chest, XperienceCategoryAssociation, \
    XperienceQuestAssociation, EntitlementGroupXperienceAssignment
from clientmodels import PublishedQuest, PublishedXperience, PublishedNode, PublishedNodeTranscript
from clientmodels import Client, ClientPackageAssociation, PackageXperienceAssociation
from clientmodels import MASTER_TENANT

app_xperience_api = Namespace('api_app_xperience', description='Xperience related API')

xperience_list_parser = app_xperience_api.parser()
xperience_list_parser.add_argument('page', type=int, required=False, default=1, help='Page number')
xperience_list_parser.add_argument('limit', type=int, required=False, default=10, help='Results per page')
xperience_list_parser.add_argument('search', type=str, required=False, help='Search term')

@app_xperience_api.doc(security='bearer')
@app_xperience_api.route('/list')
class XperienceList(Resource):
    @app_xperience_api.expect(xperience_list_parser)
    def get(self):
        args = xperience_list_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        search = args.get('search', '')
        offset = (page - 1) * limit

        query = get_xperience_query(search=search, user_id=g.user_id)
        if not query:
            return create_response("No xperience found", status=404)
        
        total, xperiences = paginate_query(query, page, limit)

        if not xperiences:
            return create_response("No xperience found", status=404)

        data = [transform_xperience(xperience, published_xperience, g.user_id) for xperience, published_xperience in xperiences]
        
        return create_response("Xperience list retrieved successfully", data=data, total=total, page=page,limit=limit)
    


@app_xperience_api.doc(security='bearer')
@app_xperience_api.route('/list/category')
class XperienceByCategory(Resource):
    @app_xperience_api.expect(xperience_list_parser)
    def get(self):
        data = []

        search = request.args.get('search', '')

        categories = g.client_db_session.query(Category).all()
        ## sory by order
        categories.sort(key=lambda c: c.order)

        for category in categories:
            xperiences = get_xperience_query(search=search, user_id=g.user_id, category_id=category.id)

            category_dict = category.to_dict()
            category_dict["xperience_list"] = [
                {**transform_xperience(xperience, published_xperience, g.user_id), "order": order}
                for xperience, published_xperience, order in xperiences
            ]

            ## if no xperience found in category, skip this category
            if not category_dict["xperience_list"]:
                continue

            ## sort xperience list by order
            category_dict["xperience_list"].sort(key=lambda x: x['order'])
            
            data.append(category_dict)
            
        
        return create_response("Xperience list retrieved successfully", data=data)
    


## get recommended xperience list 
@app_xperience_api.doc(security='bearer')
@app_xperience_api.route('/list/recommend')
class XperienceRecommend(Resource):
    def get(self):
        data = []
        user_id = g.user_id
        if not user_id:
            return create_response("User not found", status=404)
        
        # Get base query for published xperiences
        query = get_xperience_query(user_id=user_id)
        
        # Find all categories related to user's xperiences
        user_xperiences = g.db_session.query(UserXperience).filter(UserXperience.user_id == user_id).all()
        user_categories = set()
        user_finished_category_ids = set()
        for user_xperience in user_xperiences:
            if user_xperience.process_percentage < 100:
                xperience = g.db_session.query(Xperience).filter(Xperience.id == user_xperience.xperience_id).first()
                if xperience:
                    for category in xperience.categories:
                        user_categories.add(category.id)
            else:
                user_finished_category_ids.add(user_xperience.xperience_id)

        # Filter out xperiences that user has already finished
        if user_finished_category_ids:
            query = query.filter(~Xperience.id.in_(user_finished_category_ids))
        
        # Filter xperiences that are in the same categories as user's xperiences
        if user_categories:
            query = query.filter(Xperience.categories.any(Category.id.in_(user_categories)))

        # Order by the most recently updated
        query = query.order_by(Xperience.date_updated.desc())

        # Paginate the query
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 50, type=int)
        total, xperiences = paginate_query(query, page, limit)

        if not xperiences:
            return create_response("No recommended xperience found", status=404)
        
        # Transform the xperiences into the desired format
        data = [
            transform_xperience(xperience, published_xperience, user_id)
            for xperience, published_xperience in xperiences
        ]
        
        return create_response("Recommended xperience list retrieved successfully", data=data)



@app_xperience_api.doc(security='bearer')
@app_xperience_api.route('/<string:xperience_id>')
class XperienceDetail(Resource):
    def get(self, xperience_id):
        xperience = g.db_session.query(Xperience).filter(Xperience.id == xperience_id).first()
        if not xperience:
            return create_response("Xperience not found", status=404)
        
        # if xperience.status != 'Published':
        #     return create_response("Xperience not published", status=403)
        
        ## check if user has access to xperience
        user_id = g.user_id
        if user_id:
            user = g.db_session.query(User).filter(User.id == user_id).first()
            if user:
                entitlement_groups = get_user_entitlement_groups(user)
                if entitlement_groups:
                    assigned_entitlement_group_ids = entitlement_groups
                    excluded_xperience_ids = g.client_db_session.query(EntitlementGroupXperienceAssignment.xperience_id).filter(
                        ~EntitlementGroupXperienceAssignment.entitlement_group_id.in_(assigned_entitlement_group_ids)
                    ).distinct().all()
                    excluded_xperience_ids = [xperience_id[0] for xperience_id in excluded_xperience_ids]
                    if xperience.id in excluded_xperience_ids:
                        return create_response("Xperience not available", status=403)
                    
        # Transform the xperience into the desired format
        ## if current tenant is MASTER_TENANT, get the latest revision published xperience
        if g.tenant_id == MASTER_TENANT:
            latest_revision_subq = (
                g.db_session.query(
                    PublishedXperience.xperience_id,
                    func.max(PublishedXperience.revision).label("max_revision")
                )
                .group_by(PublishedXperience.xperience_id)
                .subquery()
            )

            published_xperience = (
                g.db_session.query(PublishedXperience)
                .join(
                    latest_revision_subq,
                    (PublishedXperience.xperience_id == latest_revision_subq.c.xperience_id) &
                    (PublishedXperience.revision == latest_revision_subq.c.max_revision)
                )
                .filter(PublishedXperience.xperience_id == xperience.id)
                .first()
            )

        else:
            published_xperience = g.db_session.query(PublishedXperience).filter(
                PublishedXperience.xperience_id == xperience.id,
                PublishedXperience.is_latest == True
            ).first()

        if not published_xperience:
            return create_response("Published xperience not found", status=404)
        
        print(f"Published xperience ID: {published_xperience.id}")

        xperience_dict = transform_xperience(xperience, published_xperience, user_id)

        ## get xperience cast
        xperience_dict['cast'] = get_xperience_cast(xperience)

        return create_response("Xperience retrieved successfully", data=xperience_dict)
    


@app_xperience_api.doc(security='bearer')
@app_xperience_api.route('/<string:xperience_id>/cast')
class XperienceCast(Resource):
    def get(self, xperience_id):
        xperience = g.db_session.query(Xperience).filter(Xperience.id == xperience_id).first()
        if not xperience:
            return create_response("Xperience not found", status=404)

        cast = get_xperience_cast(xperience)
        
        return create_response("Xperience cast retrieved successfully", data=cast)



## unlock xperience
@app_xperience_api.doc(security='bearer')
@app_xperience_api.route('/unlock/<string:xperience_id>')
class XperienceUnlock(Resource):
    def post(self, xperience_id):
        xperience = g.db_session.query(Xperience).filter(Xperience.id == xperience_id).first()
        if not xperience:
            return create_response("Xperience not found", status=404)
        
        if xperience.status != 'Published':
            return create_response("Xperience not published", status=403)
        
        user_id = g.user_id
        if not user_id:
            return create_response("User not found", status=404)

        user = g.db_session.query(User).filter(User.id == user_id).first()
        if not user:
            return create_response("User not found", status=404)

        keys_required = xperience.keys_required

        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)

        if user_stats.keys_count < keys_required:
            return create_response("Not enough keys to unlock this xperience", status=403)
        
        user_stats.keys_count -= keys_required
        g.db_session.commit()

        ## save unlock status to user xperience
        user_xperience = g.db_session.query(UserXperience).filter_by(user_id=user_id, xperience_id=xperience_id).first()
        if not user_xperience:
            user_xperience = UserXperience(user_id=user_id, xperience_id=xperience_id)
            g.db_session.add(user_xperience)
            g.db_session
        else:
            if user_xperience.is_unlocked:
                return create_response("Xperience already unlocked")

        user_xperience.is_unlocked = True
        g.db_session.commit()

        ## save keys used in user spending table
        user_spending = UserSpending(user_id=user_id, keys=keys_required, source="xperience", source_id=xperience_id)
        g.db_session.add(user_spending)
        g.db_session.commit()

        return create_response("Xperience unlocked successfully")
    




def get_xperience_quests(xperience, published_xperience, user_id):
    quests = []

    if not xperience or not published_xperience:
        return quests

    published_quest_ids = json.loads(published_xperience.quest_ids) if published_xperience.quest_ids else []

    # Step 1: Find the correct PublishedQuest for each quest_id
    if not published_quest_ids:
        return quests

    if g.tenant_id == MASTER_TENANT:
        # Get the latest revision for each quest_id
        latest_revision_subq = (
            g.db_session.query(
                PublishedQuest.quest_id,
                func.max(PublishedQuest.revision).label("max_revision")
            )
            .filter(PublishedQuest.quest_id.in_(published_quest_ids))
            .group_by(PublishedQuest.quest_id)
            .subquery()
        )
        published_quests = (
            g.db_session.query(PublishedQuest)
            .join(latest_revision_subq, 
                  (PublishedQuest.quest_id == latest_revision_subq.c.quest_id) &
                  (PublishedQuest.revision == latest_revision_subq.c.max_revision))
            .all()
        )
    else:
        published_quests = (
            g.db_session.query(PublishedQuest)
            .filter(
                PublishedQuest.quest_id.in_(published_quest_ids),
                PublishedQuest.is_latest == True
            )
            .all()
        )

    published_quest_map = {pq.quest_id: pq for pq in published_quests}

    # Step 2: Get associations and chests
    associations = (
        g.db_session.query(XperienceQuestAssociation)
        .filter(
            XperienceQuestAssociation.xperience_id == xperience.id,
            XperienceQuestAssociation.quest_id.in_(published_quest_ids)
        )
        .order_by(XperienceQuestAssociation.order)
        .all()
    )
    order_map = {assoc.quest_id: assoc.order for assoc in associations}

    chest_ids = [pq.chest_id for pq in published_quests if pq.chest_id]
    chests = g.db_session.query(Chest).filter(Chest.id.in_(chest_ids)).all()
    chest_map = {c.id: c for c in chests}

    # Step 3: Build quest list
    for quest_id in published_quest_ids:
        published_quest = published_quest_map.get(quest_id)
        if not published_quest:
            continue

        quest_dict = published_quest.to_dict(["id", "name", "description", "image", "level"])
        quest_dict['id'] = published_quest.quest_id
        quest_dict['order'] = order_map.get(quest_id)
        chest = chest_map.get(published_quest.chest_id)
        quest_dict['xp'] = chest.xp if chest else 0

        quests.append(quest_dict)

    quests.sort(key=lambda q: (q['order'] is None, q['order']))

    return quests


def get_xperience_cast(xperience):
    cast = []
    character_ids = []

    # Step 1: Get all quest_ids for this xperience
    quest_ids = [
        assoc.quest_id
        for assoc in g.db_session.query(XperienceQuestAssociation)
        .filter(XperienceQuestAssociation.xperience_id == xperience.id)
        .all()
    ]
    if not quest_ids:
        return cast

    # Use a single query to fetch published nodes, transcripts, and characters
    results = (
        g.db_session.query(
            PublishedNodeTranscript.character_id,
            Character,
            User
        )
        .join(
            PublishedNode, PublishedNode.id == PublishedNodeTranscript.published_node_id
        )
        .join(
            PublishedQuest, PublishedQuest.quest_id == PublishedNode.quest_id
        )
        .outerjoin(
            Character, Character.key == PublishedNodeTranscript.character_id
        )
        .outerjoin(
            User, User.id == Character.user_id
        )
        .filter(PublishedQuest.quest_id.in_(quest_ids))
        .filter(PublishedNodeTranscript.character_id.isnot(None))
        .all()
    )

    # Transform the results into the desired format
    for character_id, character, user in results:
        if character_id not in character_ids:
            character_ids.append(character_id)
            if user:
                user_dict = user.to_dict([
                    "id", "first_name", "last_name", "preferred_name", "image",
                    "phone_number", "email", "company", "title", "bio"
                ])
                user_dict['character'] = character.to_dict() if character else None
                cast.append(user_dict)

    return cast



def get_xperience_query(search=None, user_id=None, category_id=None):
    """
    Returns a query that always yields (xperience, published_xperience[, order]) tuples,
    filtered by client, search, user entitlement, and optionally category.
    If g.tenant_id == MASTER_TENANT, returns all published xperiences without client filtering.
    """
    if g.tenant_id == MASTER_TENANT:
        # No client filtering, return all published xperiences
        latest_revision_subq = (
            g.db_session.query(
                PublishedXperience.xperience_id,
                func.max(PublishedXperience.revision).label("max_revision")
            )
            .group_by(PublishedXperience.xperience_id)
            .subquery()
        )

        if category_id:
            base_query = (
                g.db_session.query(Xperience, PublishedXperience, XperienceCategoryAssociation.order)
                .join(PublishedXperience, (PublishedXperience.xperience_id == Xperience.id))
                .join(latest_revision_subq, 
                      (PublishedXperience.xperience_id == latest_revision_subq.c.xperience_id) &
                      (PublishedXperience.revision == latest_revision_subq.c.max_revision))
                .join(XperienceCategoryAssociation, PublishedXperience.xperience_id == XperienceCategoryAssociation.xperience_id)
                .filter(XperienceCategoryAssociation.category_id == category_id)
                .order_by(XperienceCategoryAssociation.order)
            )
        else:
            base_query = (
                g.db_session.query(Xperience, PublishedXperience)
                .join(PublishedXperience, (PublishedXperience.xperience_id == Xperience.id))
                .join(latest_revision_subq, 
                      (PublishedXperience.xperience_id == latest_revision_subq.c.xperience_id) &
                      (PublishedXperience.revision == latest_revision_subq.c.max_revision))
            )
    else:
        client_id = g.db_session.query(Client).filter(Client.id_key == g.tenant_id).first()
        if not client_id:
            return []

        client_package_ids = g.db_session.query(ClientPackageAssociation.package_id).filter(
            ClientPackageAssociation.client_id == client_id.id
        ).subquery()

        client_xperience_ids = g.db_session.query(PackageXperienceAssociation.xperience_id).filter(
            PackageXperienceAssociation.package_id.in_(client_package_ids.select())
        ).subquery()

        if category_id:
            base_query = (
                g.db_session.query(Xperience, PublishedXperience, XperienceCategoryAssociation.order)
                .join(PublishedXperience, PublishedXperience.xperience_id == Xperience.id)
                .join(XperienceCategoryAssociation, PublishedXperience.xperience_id == XperienceCategoryAssociation.xperience_id)
                .filter(
                    PublishedXperience.is_latest == True,
                    PublishedXperience.xperience_id.in_(client_xperience_ids.select()),
                    XperienceCategoryAssociation.category_id == category_id
                )
                .order_by(XperienceCategoryAssociation.order)
            )
        else:
            base_query = (
                g.db_session.query(Xperience, PublishedXperience)
                .join(PublishedXperience, PublishedXperience.xperience_id == Xperience.id)
                .filter(
                    PublishedXperience.is_latest == True,
                    PublishedXperience.xperience_id.in_(client_xperience_ids.select())
                )
            )

    if search:
        base_query = base_query.filter(
            PublishedXperience.name.ilike(f'%{search}%') |
            PublishedXperience.tags.any(Tag.name.ilike(f'%{search}%'))
        )

    if user_id:
        user = g.db_session.query(User).filter(User.id == user_id).first()
        if user:
            entitlement_group_ids = get_user_entitlement_groups(user)
            if entitlement_group_ids:
                if category_id:
                    excluded_xperience_ids = g.client_db_session.query(
                        EntitlementGroupXperienceAssignment.xperience_id
                    ).filter(
                        ~EntitlementGroupXperienceAssignment.entitlement_group_id.in_(entitlement_group_ids)
                    ).distinct().all()
                    excluded_xperience_ids = [x[0] for x in excluded_xperience_ids]
                    base_query = base_query.filter(~PublishedXperience.xperience_id.in_(excluded_xperience_ids))
                else:
                    restricted_xperience_subquery = (
                        g.client_db_session.query(EntitlementGroupXperienceAssignment.xperience_id)
                        .filter(~EntitlementGroupXperienceAssignment.entitlement_group_id.in_(entitlement_group_ids))
                        .group_by(EntitlementGroupXperienceAssignment.xperience_id)
                        .having(func.count(EntitlementGroupXperienceAssignment.entitlement_group_id) > 0)
                        .subquery()
                    )
                    base_query = base_query.filter(~Xperience.id.in_(restricted_xperience_subquery.select()))

    return base_query


def paginate_query(query, page, limit):
    offset = (page - 1) * limit
    total = query.count()
    results = query.offset(offset).limit(limit).all()
    return total, results


def transform_xperience(xperience, published_xperience, user_id=None):
    if not xperience or not published_xperience:
        return {}

    xperience_dict = published_xperience.to_dict()
    ## update the xperience id to original xperience id
    xperience_dict['id'] = xperience.id
    xperience_dict['category_list'] = [category.to_dict() for category in xperience.categories]
    xperience_dict['tag_list'] = [tag.to_dict() for tag in xperience.tags]
    xperience_dict['facet_list'] = [facet.to_dict() for facet in xperience.facets]

    xperience_dict['quest_list'] = get_xperience_quests(xperience, published_xperience, user_id)

    # Get xperience chest
    chest_id = published_xperience.chest_id
    chest = g.db_session.query(Chest).filter(Chest.id == chest_id).first() if chest_id else None
    xperience_dict['xp'] = chest.xp if chest else 0
    xperience_dict['chest_id'] = chest_id

    # Add all quest XP to xperience XP
    for quest in xperience_dict['quest_list']:
        xperience_dict['xp'] += quest['xp']

    return xperience_dict



def paginate_query(query, page, limit):
    ## add exception if query is empty
    if not query:
        return 0, []
    ## add exception if page or limit is not valid
    if page < 1 or limit < 1:
        return 0, []
    ## add exception if page or limit is not valid
    if page > 1000 or limit > 1000:
        return 0, []

    offset = (page - 1) * limit
    total = query.count()
    results = query.offset(offset).limit(limit).all()
    return total, results