from flask import g
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from clientmodels import Banner
from app import cache

app_home_api = Namespace('api_app_home', description='Home related API')

@app_home_api.doc(security='bearer')
@app_home_api.route('/banners')
class Banners(Resource):
    @cache.cached(timeout=3600, key_prefix=lambda: f'{g.tenant_id}:home:banners')
    def get(self):
        banners = g.client_db_session.query(Banner).order_by(Banner.order).all()
        data = [banner.to_dict() for banner in banners]

        ## if banner is empty, then use the banners from master 
        if not data:
            banners = g.db_session.query(Banner).order_by(Banner.order).all()
            data = [banner.to_dict() for banner in banners]

        return create_response("Banners retrieved successfully", data=data)
