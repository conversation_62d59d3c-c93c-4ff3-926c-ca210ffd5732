import datetime
import json

from flask import g
from flask import request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from api.common.helper import format_datetime_with_timezone
from clientmodels import Asset, Chest, Program, UserAsset, UserBoost, UserPowerUp, UserChest, UserCoins, UserGems, UserKeys, \
    UserSpending, UserStats, UserXP, UserXPFacet, Xperience

from api.app.badge import check_badge

app_chest_api = Namespace('api_app_user_chest', description='Chest related operations')



def get_chest(chest_id, source_id, source_type):
    # This function is used to open the chest
    # The chest is opened by the user and the user gets a reward

    chest_dict = {}
    chest = g.client_db_session.query(Chest).filter_by(id=chest_id).first()
    user_id = g.user_id

    chest_type = ""
    if source_type == "program":
        chest_type = "mega"
        date_openable = datetime.datetime.utcnow() + datetime.timedelta(hours=12)
        is_opened = False
    elif source_type == "xperience":
        chest_type = "prime"
        date_openable = datetime.datetime.utcnow() + datetime.timedelta(hours=8)
        is_opened = False
    else:
        chest_type = "standard"
        date_openable = datetime.datetime.utcnow()
        is_opened = True

    ## check if user has a boost that is live now by checking its start and end
    ## if the boost is live, the date_openable is reduced by 50%
    if not is_opened:
        user_boosts = g.db_session.query(UserBoost).filter(
            UserBoost.user_id == user_id,
            UserBoost.start <= datetime.datetime.utcnow(),
            UserBoost.end >= datetime.datetime.utcnow()
        ).all()

        for boost in user_boosts:
            print("user got a boost")
            date_openable = date_openable - (date_openable - datetime.datetime.utcnow()) / 2


    if chest:
        user_chest = g.db_session.query(UserChest).filter_by(user_id=user_id, chest_id=chest.id).first()
        if not user_chest:
            print("user got a chest")
            date_received = datetime.datetime.utcnow()

            user_chest = UserChest(user_id=user_id, chest_id=chest.id)
            user_chest.date_received = date_received
            user_chest.date_openable = date_openable
            user_chest.is_opened = is_opened
            user_chest.source = source_type
            user_chest.source_id = source_id
            user_chest.chest_type = chest_type

            if source_type == "program" and source_id:
                user_chest.program_id = source_id
            elif source_type == "xperience" and source_id:
                user_chest.xperience_id = source_id
            elif source_type == "quest" and source_id:
                user_chest.quest_id = source_id

            g.db_session.add(user_chest)
            g.db_session.commit()

            if is_opened:
                open_chest(chest, user_chest, source_id, source_type)

            chest_dict = get_chest_dict(chest, user_chest)

    return chest_dict


## function to open a chest and get chest rewards
def open_chest(chest, user_chest, source_id, source_type):
    user_id = g.user_id

    user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
    if not user_stats:
        user_stats = UserStats(user_id=user_id)
        g.db_session.add(user_stats)

    chest_xp, chest_coins, chest_gems, chest_keys = get_chest_rewards(chest, user_chest)

    user_stats.xp_count = user_stats.xp_count + chest_xp
    user_stats.coins_count = user_stats.coins_count + chest_coins
    user_stats.gems_count = user_stats.gems_count + chest_gems
    user_stats.keys_count = user_stats.keys_count + chest_keys
    g.db_session.add(user_stats)

    # Update user XP
    if chest_xp > 0:
        new_user_xp = UserXP(user_id=user_id, value=chest_xp, source=source_type, source_id=source_id)
        g.db_session.add(new_user_xp)
        g.db_session.commit()

        ## update chest xp per facet
        try:
            chest_xp_facet = json.loads(chest.facet_xp) if chest.facet_xp else []
            for facet in chest_xp_facet:
                facet_id = facet['id']
                facet_xp = facet['xp']
                ## create a new user xp facet
                new_user_xp_facet = UserXPFacet(user_id=user_id
                                                , value=facet_xp
                                                , facet_id=facet_id
                                                , user_xp_id=new_user_xp.id)
                g.db_session.add(new_user_xp_facet)
        except:
            chest_xp_facet = []

    # Update user coins
    if chest.coins > 0:
        new_user_coins = UserCoins(user_id=user_id, value=chest_coins, source=source_type, source_id=source_id)
        g.db_session.add(new_user_coins)

    # Update user gems
    if chest.gems > 0:
        new_user_gems = UserGems(user_id=user_id, value=chest_gems, source=source_type, source_id=source_id)
        g.db_session.add(new_user_gems)

    # Update user keys
    if chest.keys > 0:
        new_user_keys = UserKeys(user_id=user_id, value=chest_keys, source=source_type, source_id=source_id)
        g.db_session.add(new_user_keys)

    assets = chest.assets
    # Add assets to user assets
    for asset in assets:
        if not g.db_session.query(UserAsset).filter_by(user_id=user_id, asset_id=asset.id).count():
            ## check if the asset exist in client db and global db
            global_asset = g.db_session.query(Asset).filter(Asset.id == asset.id, Asset.is_deleted == False).first()
            asset = g.client_db_session.query(Asset).filter(Asset.id == asset.id, Asset.is_deleted == False).first()
            if asset and global_asset:
                user_asset = UserAsset(user_id=user_id, asset_id=asset.id)
                g.db_session.add(user_asset)

    user_chest.is_opened = True

    g.db_session.commit()
    return chest


def get_chest_rewards(chest, user_chest):
    ## calculate the chest rewards
    user_id = g.user_id
    chest_xp = chest.xp if chest.xp else 0
    chest_coins = chest.coins if chest.coins else 0
    chest_gems = chest.gems if chest.gems else 0
    chest_keys = chest.keys if chest.keys else 0

    ## check if user has a powerup that is live now by checking its start and end
    ## if the powerup is live, then we add the powerup value to the chest value
    date_openable = user_chest.date_openable
    date_received = user_chest.date_received
        
    user_powerups = g.db_session.query(UserPowerUp).filter(
        UserPowerUp.user_id == user_id,
        UserPowerUp.start <= date_received,
        UserPowerUp.end >= date_received
    ).all()
    for powerup in user_powerups:
        print("user double the chest rewards")
        chest_coins *= 2
        chest_gems *= 2
        # chest_keys *= 2

    return chest_xp, chest_coins, chest_gems, chest_keys



def get_chest_dict(chest, user_chest):
    assets = chest.assets
    chest_xp, chest_coins, chest_gems, chest_keys = get_chest_rewards(chest, user_chest)

    chest_type = user_chest.chest_type

    ## get assets
    asset_list = []
    for asset in assets:
        ## check if the asset exist in client db and global db
        global_asset = g.db_session.query(Asset).filter(Asset.id == asset.id, Asset.is_deleted == False).first()
        asset = g.client_db_session.query(Asset).filter(Asset.id == asset.id, Asset.is_deleted == False).first()
        if asset and global_asset:
            asset_list.append(asset.to_dict(["id", "name", "file_type", "file_path"]))
    
    chest_dict = {
        'chest_id': chest.id,
        'xp': chest_xp,
        'coins': chest_coins,
        'gems': chest_gems,
        'keys': chest_keys,
        'name': chest.name if chest.name else 'YOUR REWARDS',
        'button_label': chest.button_label if chest.button_label else 'Claim Rewards',
        'assets': asset_list,
        'date_received': format_datetime_with_timezone(user_chest.date_received),
        'date_openable': format_datetime_with_timezone(user_chest.date_openable),
        'is_opened': user_chest.is_opened,
        'chest_type': chest_type
    }

    return chest_dict


open_chest_model = app_chest_api.model('OpenChest', {
    'open_immidiately': fields.Boolean(required=True, description='Open the chest immidiately', default=False)
})
@app_chest_api.doc(security='bearer')
@app_chest_api.route('/open/<string:chest_id>')
class OpenChest(Resource):
    @app_chest_api.expect(open_chest_model)
    def post(self, chest_id):
        ## get user id from g
        user_id = g.user_id
        
        ## get the user chest
        user_chest = g.db_session.query(UserChest).filter_by(user_id=user_id, chest_id=chest_id, is_opened=False).first()
        if not user_chest:
            return create_response("Chest not found or already opened", status=200)
        
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)

        ## get the chest details
        chest = g.client_db_session.query(Chest).filter_by(id=chest_id).first()
        if not chest:
            return create_response("Chest details not found", status=200)

        chest_type = user_chest.chest_type if user_chest.chest_type else ""
        source_id = user_chest.source_id if user_chest.source_id else ""
        source = user_chest.source if user_chest.source else ""

        if not source or not source_id:
            ## get chest type from xperience and program table to see if it is a program chest or xperience chest
            program = g.client_db_session.query(Program).filter_by(chest_id=chest_id).first()
            if program:
                source = "program"
                source_id = program.id

            xperience = g.client_db_session.query(Xperience).filter_by(chest_id=chest_id).first()
            if xperience:
                source = "xperience"
                source_id = xperience.id

        if source == "program":
            price = 50
            chest_type = chest_type if chest_type else "mega"
        elif source == "xperience":
            price = 25
            chest_type = chest_type if chest_type else "prime"
        
        ## check chest openable time, if not openable, return error
        if user_chest.date_openable and user_chest.date_openable > datetime.datetime.utcnow():
            ## check if the user wants to open the chest immidiately
            data = request.json
            open_immidiately = data.get('open_immidiately', False)
            if not open_immidiately:
                return create_response("Chest is not openable yet", status=200)
            else:
                ## check if the user has enough gems to open the chest immidiately
                gems_count = user_stats.gems_count if user_stats.gems_count else 0
                if gems_count < price:
                    return create_response("Not enough Xrystals to open the chest", status=200)

                ## deduct the gems from the user
                user_stats.gems_count = gems_count - price

                g.db_session.add(user_stats)
                g.db_session.commit()

                ## record the spending of gems in user spending table
                user_spending = UserSpending(user_id=user_id, gems=price, source="open_chest", date_created=datetime.datetime.utcnow())
                g.db_session.add(user_spending)
                g.db_session.commit()

        ## open the chest
        chest = open_chest(chest, user_chest, source_id, source)
        chest_dict = get_chest_dict(chest, user_chest)

        rewards = {
            'chests': [],
            'badges': []
        }

        ## check if user has earned any badge
        if chest_type == "mega":
            mega_chest_open_badge = check_badge("", "first_mega_chest_open")
            if mega_chest_open_badge:
                rewards['badges'].append(mega_chest_open_badge)

        if chest_type == "prime":
            prime_chest_open_badge = check_badge("", "first_prime_chest_open")
            if prime_chest_open_badge:
                rewards['badges'].append(prime_chest_open_badge)


        rewards['chests'].append(chest_dict)
        
        return create_response("Chest opened successfully", data=chest.to_dict(), rewards=rewards)



@app_chest_api.doc(security='bearer')
@app_chest_api.route('/pending')
class PendingChests(Resource):
    def get(self):
        ## get user id from g
        user_id = g.user_id
        
        ## get unopened chests for the user
        unopened_chests = g.db_session.query(UserChest).filter_by(user_id=user_id, is_opened=False).all()
        
        chests_data = []
        for user_chest in unopened_chests:
            chest = g.client_db_session.query(Chest).filter_by(id=user_chest.chest_id).first()
            if chest:
                chest_dict = get_chest_dict(chest, user_chest)
                
                chest_type = user_chest.chest_type if user_chest.chest_type else ""
                source_id = user_chest.source_id if user_chest.source_id else ""
                source = user_chest.source if user_chest.source else ""
                price = {
                    'value': 0,
                    'type': "gems"
                }

                if not source or not source_id:
                    ## get chest type from xperience and program table to see if it is a program chest or xperience chest
                    program = g.client_db_session.query(Program).filter_by(chest_id=chest.id).first()
                    if program:
                        source = "program"
                        source_id = program.id

                    xperience = g.client_db_session.query(Xperience).filter_by(chest_id=chest.id).first()
                    if xperience:
                        source = "xperience"
                        source_id = xperience.id

                if source == "program":
                    price = {
                        'value': 50,
                        'type': "gems"
                    }
                    chest_type = chest_type if chest_type else "mega"

                elif source == "xperience":
                    price = {
                        'value': 25,
                        'type': "gems"
                    }
                    chest_type = chest_type if chest_type else "prime"
                    
                chest_dict['chest_type'] = chest_type
                chest_dict['price'] = price
                chests_data.append(chest_dict)

        ## sort the chest data by date_received
        chests_data = sorted(chests_data, key=lambda x: x['date_received'])
        
        return create_response("Get unopened chests", data=chests_data)


