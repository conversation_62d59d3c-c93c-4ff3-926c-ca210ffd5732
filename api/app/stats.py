import datetime
import json

from flask import g
from flask import request
from flask_restx import Namespace, Resource, fields
from sqlalchemy import func

from api.app.badge import check_badge
from api.app.xperience import get_xperience_quests
from api.common.helper import create_response
from api.common.helper import format_datetime_with_timezone, get_local_today
from clientmodels import Asset, Badge, Chest, Node, Program, PublishedProgram, PublishedXperience, Quest, User, UserAsset, UserBadge, UserBoost, UserKeys, UserNode, \
    UserPowerUp, UserQuest, UserStore, UserStreak, UserStats, UserCheckin, Xperience
from clientmodels import UserXP, UserGems, UserSpending, UserXperience, UserProgram, UserXAPADay, Store
from app import storage

app_stats_api = Namespace('api_app_user_stats', description='App User Stats related operations')

@app_stats_api.doc(security='bearer')
@app_stats_api.route('/stats')
class MyStats(Resource):
    def get(self):
        ## get current user profile
        user = g.db_session.query(User).filter_by(id=g.user_id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user.id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user.id)
            g.db_session.add(user_stats)
            g.db_session.commit()
        
        data = user_stats.to_dict()

        ## get user last checkin date
        data['last_checkin'] = ""
        data['last_mode'] = ""
        last_checkin = g.db_session.query(UserCheckin).filter_by(user_id=user.id).order_by(UserCheckin.date_created.desc()).first()
        if last_checkin:
            data['last_checkin'] = format_datetime_with_timezone(last_checkin.date_created) if last_checkin.date_created else ""
            data['last_mode'] = last_checkin.notes

        ## get user last streak time
        data['last_streak'] = ""
        last_streak = g.db_session.query(UserStreak).filter_by(user_id=user.id).order_by(UserStreak.date_created.desc()).first()
        if last_streak:
            data['last_streak'] = format_datetime_with_timezone(last_streak.date_created) if last_streak.date_created else ""

        ## get user last xapa day time
        data['last_xapa_day_checkin'] = ""
        last_xapa_day = g.db_session.query(UserXAPADay).filter_by(user_id=user.id).order_by(UserXAPADay.date_created.desc()).first()
        if last_xapa_day:
            data['last_xapa_day_checkin'] = format_datetime_with_timezone(last_xapa_day.date_created) if last_xapa_day.date_created else ""

        ## get ongoing powerups
        data['addons'] = {
            'powerups': [],
            'boosts': []
        }
        user_powerups = g.db_session.query(UserPowerUp).filter_by(user_id=user.id).filter(
            UserPowerUp.end >= datetime.datetime.utcnow()).all()

        for user_powerup in user_powerups:
            data['addons']['powerups'].append(user_powerup.to_dict())

        user_boosts = g.db_session.query(UserBoost).filter_by(user_id=user.id).filter(
            UserBoost.end >= datetime.datetime.utcnow()).all()
        
        for user_boost in user_boosts:
            data['addons']['boosts'].append(user_boost.to_dict())

        ## get the last daily powerup
        data['latest_daily_powerup'] = ""
        latest_daily_powerup = g.db_session.query(UserPowerUp).filter_by(user_id=user.id, source='daily').order_by(UserPowerUp.date_created.desc()).first()
        if latest_daily_powerup:
            data['latest_daily_powerup'] = format_datetime_with_timezone(latest_daily_powerup.date_created) if latest_daily_powerup.date_created else ""
        
        return create_response("Get user stats", data=data)


checkin_model = app_stats_api.model('Checkin', {
    'mode': fields.String(required=True, description='Checkin mode')
})

@app_stats_api.doc(security='bearer')
@app_stats_api.route('/checkin')
class Checkin(Resource):
    @app_stats_api.expect(checkin_model)
    def post(self):
        ## get current user profile
        user = g.db_session.query(User).filter_by(id=g.user_id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        # Check if user already checked in today
        today = get_local_today()
        checkin_today = g.db_session.query(UserCheckin).filter_by(user_id=user.id, checkin_date=today).first()
        
        if checkin_today:
            return create_response("User already checked in today")
        
        mode = request.json.get('mode', '')
        
        # Save user checkin in UserCheckin table
        checkin = UserCheckin(user_id=user.id, checkin_date=today, date_created=datetime.datetime.utcnow())
        if mode:
            checkin.notes = mode

        g.db_session.add(checkin)

        # add 1 gem to user stats
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user.id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user.id)
            g.db_session.add(user_stats)

        user_stats.gems_count += 1

        # Add 1 gem record in UserGems table
        user_gem = UserGems(user_id=user.id, value=1, source="checkin", date_created=datetime.datetime.utcnow())
        g.db_session.add(user_gem)

        g.db_session.commit()
    
        return create_response("Checkin successful")


@app_stats_api.doc(security='bearer')
@app_stats_api.route('/streak')
class Streak(Resource):
    def post(self):
        ## get user id from g
        user_id = g.user_id
        
        # Calculate the current streak count
        today = get_local_today()
        yesterday = today - datetime.timedelta(days=1)

        # Check the last streak date
        last_streak_date = None
        last_streak = g.db_session.query(UserStreak).filter_by(user_id=user_id).order_by(UserStreak.date_created.desc()).first()
        if last_streak:
            if last_streak.date:
                last_streak_date = last_streak.date
            else:
                last_streak_date = last_streak.date_created.date()

            if 2 <= (today - last_streak_date).days <= 4:
                return create_response("Streak broken, cannot continue")
            
        # Check if user already has a streak today
        if last_streak_date and last_streak_date == today:
            return create_response("User already has a streak today")
            
        streak = UserStreak(user_id=user_id, source="streak", date=today)
        g.db_session.add(streak)
        
        # Get the user's streaks
        streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).order_by(UserStreak.date_created.desc()).all()
        
        streak_count = 1
        cur_date = today
        for streak in streaks:
            if streak.date:
                streak_day = streak.date
            else:
                streak_day = streak.date_created.date()

            if streak_day == today:
                continue
            elif streak_day == yesterday:
                cur_date = yesterday
                streak_count += 1
                yesterday -= datetime.timedelta(days=1)
            else:
                ## remove duplicate streaks
                if streak_day == cur_date:
                    streak.source = "deleted"
                    g.db_session.add(streak)
                    # g.db_session.delete(streak)
                    continue
                else:
                    break
        
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id, streaks_count=streak_count)
            g.db_session.add(user_stats)
        else:
            user_stats.streaks_count = streak_count

        # Add 8 XP to the user's stats
        user_stats.xp_count = user_stats.xp_count if user_stats.xp_count else 0
        user_stats.xp_count += 8

        # Add 8 XP record in UserXP table
        user_xp = UserXP(user_id=user_id, value=8, source="streak", date_created=datetime.datetime.utcnow())
        g.db_session.add(user_xp)

        g.db_session.commit()

        ## check user streak badges
        rewards = {
            "badges": []
        }
        streak_badge = check_badge('streak', user_stats.streaks_count)
        if streak_badge:
            if isinstance(streak_badge, list):
                rewards["badges"].extend(streak_badge)
            else:
                rewards["badges"].append(streak_badge)
    
        return create_response("Streak added successfully", rewards=rewards)



@app_stats_api.doc(security='bearer')
@app_stats_api.route('/streak/repair')
class RepairStreak(Resource):
    def post(self):
        ## get user id from g
        user_id = g.user_id
        
        # Get the user's streaks
        streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).order_by(UserStreak.date_created.desc()).all()
        
        if not streaks:
            return create_response("No streaks found to repair", status=404)
        
        # Find the last streak date
        if streaks[0].date:
            last_streak_date = streaks[0].date
        else:
            last_streak_date = streaks[0].date_created.date()

        today = get_local_today()

        # Check if the missing days count is within 4 days
        missing_days = (today - last_streak_date).days - 1
        if missing_days > 4:
            return create_response("Cannot repair streak, too many days missed", status=200)
        
        # Check if user has more than 10 gems
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats or user_stats.gems_count < 10:
            return create_response("Not enough gems to repair streak", status=200)
        
        # Add missing streaks from the break day to today
        current_date = last_streak_date + datetime.timedelta(days=1)
        
        while current_date < today:
            streak = UserStreak(user_id=user_id, source="repair", date=current_date, date_created=current_date)
            g.db_session.add(streak)
            current_date += datetime.timedelta(days=1)
        
        # Add today's streak with source "checkin"
        streak = UserStreak(user_id=user_id, source="streak", date=today)
        g.db_session.add(streak)
        
        # Calculate the current streak count
        streak_count = 1
        yesterday = today - datetime.timedelta(days=1)
        
        streaks = g.db_session.query(UserStreak).filter_by(user_id=user_id).order_by(UserStreak.date_created.desc()).all()
        cur_date = today
        for streak in streaks:
            if streak.date:
                streak_day = streak.date
            else:
                streak_day = streak.date_created.date()

            if streak_day == today:
                continue
            elif streak_day == yesterday:
                streak_count += 1
                cur_date = yesterday
                yesterday -= datetime.timedelta(days=1)
            else:
                # Handle duplicate streaks
                if streak_day == cur_date:
                    streak.source = "deleted"
                    g.db_session.add(streak)
                    continue
                else:
                    break
        
        if not user_stats:
            user_stats = UserStats(user_id=user_id, streaks_count=streak_count)
            g.db_session.add(user_stats)
        else:
            user_stats.streaks_count = streak_count
        
        # Deduct 10 gems from user stats
        user_stats.gems_count -= 10

        # Add 10 gems spending in UserSpending table
        user_spending = UserSpending(user_id=user_id, gems=10, source="repair_streak", date_created=datetime.datetime.utcnow())
        g.db_session.add(user_spending)

        g.db_session.commit()
    
        return create_response("Streak repaired successfully")


@app_stats_api.doc(security='bearer')
@app_stats_api.route('/xapa_day')
class XapaDay(Resource):
    def get(self):
        ## get user id from g
        user_id = g.user_id
        
        
        versions = storage.list_files('global_settings/xapa_days/versions')
        
        if not versions:
            return create_response("No daily checkin versions found", status=404)
            
        # Get the latest version by sorting on last_modified
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        latest_version = versions[0]
        
        # Get the content of the latest version
        data = storage.download_file(f"global_settings/xapa_days/versions", latest_version['name'].split('/')[-1])
        if not data:
            return create_response("Failed to download daily checkin settings", status=404)

        # Parse JSON content
        try:
            import json
            data = json.loads(data.read().decode('utf-8'))
        except Exception as e:
            return create_response(f"Failed to parse settings: {str(e)}", status=500)

        ## get current day of the week and get the quote from the json file
        today = get_local_today()
        
        ## return day name
        day_name = today.strftime("%A")

        res_data = {}
        for day_data in data:
            if day_data['day'] == day_name:
                res_data = day_data
                break

        if not res_data:
            return create_response("Get Xapa Day", status=404)
    
        return create_response("Get Xapa Day", data=res_data)


@app_stats_api.doc(security='bearer')
@app_stats_api.route('/xapa_day/claim')
class ClaimXapaDay(Resource):
    def post(self):
        ## get user id from g
        user_id = g.user_id
        
        ## get today's date
        today = get_local_today()
        
        ## check if user has already claimed xapa day today
        user_xapa_day = g.db_session.query(UserXAPADay).filter_by(user_id=user_id, checkin_date=today).first()
        if user_xapa_day:
            return create_response("Xapa Day already claimed today")
        
        ## save user xapa day record
        user_xapa_day = UserXAPADay(user_id=user_id, checkin_date=today, date_created=datetime.datetime.utcnow())
        g.db_session.add(user_xapa_day)

        ## add 1 key to user stats
        user_stats = g.db_session.query(UserStats).filter_by(user_id=user_id).first()
        if not user_stats:
            user_stats = UserStats(user_id=user_id)
            g.db_session.add(user_stats)

        user_stats.keys_count += 1

        ## add 1 key record in UserKeys table
        user_key = UserKeys(user_id=user_id, value=1, source="xapa_day", date_created=datetime.datetime.utcnow())
        g.db_session.add(user_key)

        g.db_session.commit()
    
        return create_response("Xapa Day claimed successfully")



node_model = app_stats_api.model('Node', {
    'node_ids': fields.List(fields.String, required=True, description='List of node IDs')
})

@app_stats_api.doc(security='bearer')
@app_stats_api.route('/nodes')
class UserNodes(Resource):
    @app_stats_api.expect(node_model)
    def post(self):
        ## get user id from g
        user_id = g.user_id
        
        ## get node ids from post data
        node_ids = request.json.get('node_ids', [])
        
        if not node_ids:
            return create_response("No node ids provided", status=400)
        
        ## get nodes information and user answers
        nodes_data = []
        for node_id in node_ids:
            node = g.client_db_session.query(Node).filter_by(id=node_id).first()
            if node:
                node_dict = node.to_dict()
                
                ## get user answer from user_node table
                user_node = g.db_session.query(UserNode).filter_by(user_id=user_id, node_id=node_id).first()
                if user_node:
                    node_dict['status'] = 'completed'
                    ## get options from raw_data if it's not empty
                    if user_node.raw_data:
                        raw_data = json.loads(user_node.raw_data)
                        options = raw_data.get('options', [])
                        for option in options:
                            if option is None:
                                option = {}
                            else:
                                for option in options:
                                    for key, value in option.items():
                                        if value is None:
                                            option[key] = ""

                    node_dict['user_data'] = {
                        'node_type': user_node.node_type if user_node.node_type else "",
                        'value': user_node.value if user_node.value else "",
                        'points': user_node.points if user_node.points else 0,
                        'duration': user_node.duration if user_node.duration else 0,
                        'options': options if options else [],
                        'previous_node_id': user_node.previous_node_id if user_node.previous_node_id else "",
                        'next_node_id': user_node.next_node_id if user_node.next_node_id else "",
                    }
                else:
                    node_dict['status'] = 'incomplete'
                    node_dict['user_data'] = {}
                
                nodes_data.append(node_dict)
        
        return create_response("Get user nodes", data=nodes_data)




## API for getting user xperience, program, quest status
@app_stats_api.doc(security='bearer')
@app_stats_api.route('/contents')
@app_stats_api.doc(security='bearer')
@app_stats_api.route('/contents')
class UserContents(Resource):
    def get(self):
        user_id = g.user_id
        user = g.db_session.query(User).filter_by(id=user_id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        data = {}

        # --- Finished Quests Today ---
        today = get_local_today()
        user_completed_quests = g.db_session.query(UserQuest).filter_by(user_id=user.id)\
            .filter(UserQuest.status=='completed', UserQuest.date_completed >= today).all()
        quest_ids_today = [uq.quest_id for uq in user_completed_quests]
        quests_today = g.db_session.query(Quest).filter(Quest.id.in_(quest_ids_today)).all()
        quest_map_today = {q.id: q for q in quests_today}
        data['finished_quests'] = [
            quest_map_today[uq.quest_id].to_dict(["id", "name", "image", "description"])
            for uq in user_completed_quests if uq.quest_id in quest_map_today
        ]

        # --- Ongoing Quests ---
        user_quests = g.db_session.query(UserQuest).filter_by(user_id=user.id)\
            .order_by(UserQuest.date_updated.desc()).all()
        quest_ids = list({uq.quest_id for uq in user_quests})
        quests = g.db_session.query(Quest).filter(Quest.id.in_(quest_ids)).all()
        quest_map = {q.id: q for q in quests}
        chest_ids = [q.chest_id for q in quests if q.chest_id]
        chests = g.db_session.query(Chest).filter(Chest.id.in_(chest_ids)).all()
        chest_map = {c.id: c for c in chests}

        quest_progress = {}
        for uq in user_quests:
            process_percentage = uq.process_percentage or 0
            if uq.quest_id not in quest_map:
                continue
            if uq.quest_id not in quest_progress:
                if process_percentage > 0 or uq.status == 'completed':
                    quest = quest_map[uq.quest_id]
                    quest_dict = quest.to_dict(["id", "name", "image", "description"])
                    chest = chest_map.get(quest.chest_id)
                    quest_dict['xp'] = chest.xp if chest else 0
                    quest_dict['process_percentage'] = 100 if uq.status == 'completed' else process_percentage
                    quest_progress[uq.quest_id] = quest_dict
            else:
                # Update process percentage if completed or higher progress
                if uq.status == 'completed':
                    quest_progress[uq.quest_id]['process_percentage'] = 100
                elif process_percentage > quest_progress[uq.quest_id]['process_percentage']:
                    quest_progress[uq.quest_id]['process_percentage'] = process_percentage
        data['quests'] = list(quest_progress.values())

        # --- Ongoing Xperiences ---
        user_xperiences = g.db_session.query(UserXperience).filter_by(user_id=user.id)\
            .order_by(UserXperience.date_updated.desc()).all()
        xperience_ids = list({ux.xperience_id for ux in user_xperiences})
        xperiences = g.db_session.query(Xperience).filter(Xperience.id.in_(xperience_ids)).all()
        xperience_map = {x.id: x for x in xperiences}

        # Fetch all published xperiences in one query

        ## if current tenant is MASTER_TENANT, then filter by latest revision number, otherwise filter by is_latest
        if g.tenant_id == 'MASTER_TENANT':
            latest_revision_subq = (
                g.db_session.query(
                    PublishedXperience.xperience_id,
                    func.max(PublishedXperience.revision).label("max_revision")
                )
                .group_by(PublishedXperience.xperience_id)
                .subquery()
            )
            published_xperiences = g.db_session.query(PublishedXperience).join(
                latest_revision_subq,
                (PublishedXperience.xperience_id == latest_revision_subq.c.xperience_id) &
                (PublishedXperience.revision == latest_revision_subq.c.max_revision)
            ).filter(PublishedXperience.xperience_id.in_(xperience_ids)).all()

        else:
            published_xperiences = g.db_session.query(PublishedXperience).filter(
                PublishedXperience.xperience_id.in_(xperience_ids),
                PublishedXperience.is_latest == True
            ).all()

        published_xperience_map = {px.xperience_id: px for px in published_xperiences}

        chest_ids = [x.chest_id for x in xperiences if x.chest_id]
        chests = g.db_session.query(Chest).filter(Chest.id.in_(chest_ids)).all()
        chest_map = {c.id: c for c in chests}

        xperience_progress = {}
        for ux in user_xperiences:
            if ux.xperience_id not in xperience_map:
                continue
            process_percentage = ux.process_percentage or 0
            if ux.xperience_id not in xperience_progress:
                xperience = xperience_map[ux.xperience_id]
                published_xperience = published_xperience_map.get(ux.xperience_id)

                if not xperience or not published_xperience:
                    continue

                xperience_dict = published_xperience.to_dict()
                xperience_dict['id'] = xperience.id

                chest = chest_map.get(xperience.chest_id)
                xperience_dict['xp'] = chest.xp if chest else 0
                xperience_dict['process_percentage'] = 100 if ux.status == 'completed' else process_percentage
                # Pass both xperience and published_xperience to get_xperience_quests
                xperience_dict['quest_list'] = get_xperience_quests(xperience, published_xperience, g.user_id)
                for quest in xperience_dict['quest_list']:
                    xperience_dict['xp'] += quest['xp']
                xperience_progress[ux.xperience_id] = xperience_dict
            else:
                if ux.status == 'completed':
                    xperience_progress[ux.xperience_id]['process_percentage'] = 100
                elif process_percentage > xperience_progress[ux.xperience_id]['process_percentage']:
                    xperience_progress[ux.xperience_id]['process_percentage'] = process_percentage
        data['xperiences'] = list(xperience_progress.values())

        # --- Unlocked Xperiences ---
        unlocked_xperiences = g.db_session.query(UserXperience).filter_by(
            user_id=user.id, is_unlocked=True
        ).order_by(UserXperience.date_started.desc()).all()
        data['unlocked_xperiences'] = list({ux.xperience_id for ux in unlocked_xperiences})

        # --- Ongoing Programs ---
        user_programs = g.db_session.query(UserProgram).filter_by(user_id=user.id)\
            .order_by(UserProgram.date_started.desc()).all()
        program_ids = list({up.program_id for up in user_programs})
        programs = g.db_session.query(Program).filter(Program.id.in_(program_ids)).all()
        program_map = {p.id: p for p in programs}

        # Fetch all published programs in one query
        ## if current tenant is MASTER_TENANT, then filter by latest revision number, otherwise filter by is_latest
        if g.tenant_id == 'MASTER_TENANT':
            latest_revision_subq = (
                g.db_session.query(
                    PublishedProgram.program_id,
                    func.max(PublishedProgram.revision).label("max_revision")
                )
                .group_by(PublishedProgram.program_id)
                .subquery()
            )
            published_programs = g.db_session.query(PublishedProgram).join(
                latest_revision_subq,
                (PublishedProgram.program_id == latest_revision_subq.c.program_id) &
                (PublishedProgram.revision == latest_revision_subq.c.max_revision)
            ).filter(PublishedProgram.program_id.in_(program_ids)).all()
        else:
            published_programs = g.db_session.query(PublishedProgram).filter(
                PublishedProgram.program_id.in_(program_ids),
                PublishedProgram.is_latest == True
            ).all()

        published_program_map = {pp.program_id: pp for pp in published_programs}

        chest_ids = [p.chest_id for p in programs if p.chest_id]
        chests = g.db_session.query(Chest).filter(Chest.id.in_(chest_ids)).all()
        chest_map = {c.id: c for c in chests}

        program_progress = {}
        for up in user_programs:
            if up.program_id not in program_map:
                continue
            process_percentage = up.process_percentage or 0
            if up.program_id not in program_progress:
                program = program_map[up.program_id]
                published_program = published_program_map.get(up.program_id)

                if not program or not published_program:
                    continue

                # Use published_program.to_dict() for program info
                program_dict = published_program.to_dict()
                program_dict['id'] = program.id
                
                chest = chest_map.get(program.chest_id)
                program_dict['xp'] = chest.xp if chest else 0
                program_dict['process_percentage'] = 100 if up.status == 'completed' else process_percentage
                # program_dict['quest_list'] = program.get_quest_list() # Uncomment if needed
                program_progress[up.program_id] = program_dict
            else:
                if up.status == 'completed':
                    program_progress[up.program_id]['process_percentage'] = 100
                elif process_percentage > program_progress[up.program_id]['process_percentage']:
                    program_progress[up.program_id]['process_percentage'] = process_percentage
        data['programs'] = list(program_progress.values())

        # --- Unlocked Programs ---
        unlocked_programs = g.db_session.query(UserProgram).filter_by(
            user_id=user.id, is_unlocked=True
        ).order_by(UserProgram.date_started.desc()).all()
        data['unlocked_programs'] = list({up.program_id for up in unlocked_programs})

        return create_response("Get user contents", data=data)



@app_stats_api.doc(security='bearer')
@app_stats_api.route('/store')
class UserStoreList(Resource):
    def get(self):
        ## get user id from g
        user_id = g.user_id
        
        ## get user purchased items from user store table
        user_store_items = g.db_session.query(UserStore).filter_by(user_id=user_id).all()
        store_items_ids = [user_store_item.store_item_id for user_store_item in user_store_items]

        ## get user boost and powerups that source are from store and it's not expired
        user_boosts = g.db_session.query(UserBoost).filter_by(user_id=user_id).filter(
            UserBoost.end >= datetime.datetime.utcnow(), UserBoost.source=="store").all()
        for user_boost in user_boosts:
            store_items_ids.append(user_boost.source_id)

        user_powerups = g.db_session.query(UserPowerUp).filter_by(user_id=user_id).filter(
            UserPowerUp.end >= datetime.datetime.utcnow(), UserPowerUp.source=="store").all()
        for user_powerup in user_powerups:
            store_items_ids.append(user_powerup.source_id)

        ## get store items
        store_items = g.db_session.query(Store).filter(Store.id.in_(store_items_ids)).all()
        data = [store_item.to_dict() for store_item in store_items]

        ## in store items, check if the item is a desktop, if so, make sure user has access to both mobile and desktop version asset
        for store_item in data:
            if store_item['item_type'] == "desktop":
                user_login_type = g.user_login_type if g.user_login_type else "app"
                if user_login_type == "app":
                    name = store_item['name'] + " (Mobile)"
                else:
                    name = store_item['name'] + " (Desktop)"
                asset = g.db_session.query(Asset).filter_by(name=name).first()
                if asset:
                    ## add asset id to user asset table
                    user_asset = g.db_session.query(UserAsset).filter_by(user_id=user_id, asset_id=asset.id).first()
                    if not user_asset:
                        user_asset = UserAsset(user_id=user_id, asset_id=asset.id)
                        g.db_session.add(user_asset)
                        g.db_session.commit()
                    store_item['asset_id'] = asset.id

        return create_response("Get user store items", data=data)
    


@app_stats_api.doc(security='bearer')
@app_stats_api.route('/badges')
class UserBadges(Resource):
    def get(self):
        ## get user id from g
        user_id = g.user_id
        data = []
        
        ## get user badges
        user_badges = g.db_session.query(UserBadge).filter_by(user_id=user_id).all()
        for user_badge in user_badges:
            badge = g.db_session.query(Badge).filter_by(id=user_badge.badge_id).first()
            if badge:
                data.append(badge.to_dict())
        
        return create_response("Get user badges", data=data)