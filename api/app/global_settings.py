from flask_restx import Namespace, Resource

from api.common.helper import create_response
from clientmodels import Setting, get_db_session
from app import cache, storage

app_global_settings_api = Namespace('api_app_global_settings', description='Global settings operations for app')

@app_global_settings_api.doc(security='bearer')
@app_global_settings_api.route('/city/current')
class CurrentCitySettings(Resource):
    @cache.cached(timeout=3600, key_prefix='global_setting:city')
    def get(self):
        """Get current city global settings for app"""
        versions = storage.list_files('global_settings/city/versions')
        
        if not versions:
            return create_response("No city versions found", status=404)
            
        # Get the latest version by sorting on last_modified
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        latest_version_filename = versions[0]['name'].split('/')[-1]
        latest_version_id = latest_version_filename.split('.')[0]
        
        # Get the content of the latest version
        data = storage.download_file(f"global_settings/city/versions", latest_version_filename)
        if not data:
            return create_response("Failed to download city settings", status=404)
            
        # Parse JSON content
        try:
            import json
            content = json.loads(data.read().decode('utf-8'))
           
            return create_response("City settings retrieved successfully", data={
                'content': content,
                'version': latest_version_id
            })
        except Exception as e:
            return create_response(f"Failed to parse city settings: {str(e)}", status=500)


@app_global_settings_api.doc(security='bearer')
@app_global_settings_api.route('/school/current')
class CurrentSchoolSettings(Resource):
    @cache.cached(timeout=3600, key_prefix='global_setting:school')
    def get(self):
        """Get current school global settings for app"""
        
        versions = storage.list_files('global_settings/school/versions')
        
        if not versions:
            return create_response("No school versions found", status=404)
            
        # Get the latest version by sorting on last_modified
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        latest_version_filename = versions[0]['name'].split('/')[-1]
        latest_version_id = latest_version_filename.split('.')[0]
        
        # Get the content of the latest version
        data = storage.download_file(f"global_settings/school/versions", latest_version_filename)
        if not data:
            return create_response("Failed to download school settings", status=404)
            
        # Parse JSON content
        try:
            import json
            content = json.loads(data.read().decode('utf-8'))
            
            return create_response("School settings retrieved successfully", data={
                'content': content,
                'version': latest_version_id
            })
        except Exception as e:
            return create_response(f"Failed to parse school settings: {str(e)}", status=500)


@app_global_settings_api.doc(security='bearer')
@app_global_settings_api.route('/rewards/daily/current')
class DailyRewardsSettings(Resource):
    @cache.cached(timeout=3600, key_prefix='global_setting:daily_rewards')
    def get(self):
        """Get daily rewards global settings for app"""
        
        versions = storage.list_files('global_settings/rewards/daily/versions')
        
        if not versions:
            return create_response("No daily rewards versions found", status=404)
            
        # Get the latest version by sorting on last_modified
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        latest_version = versions[0]
        
        # Get the content of the latest version
        data = storage.download_file(f"global_settings/rewards/daily/versions", latest_version['name'].split('/')[-1])
        if not data:
            return create_response("Failed to download daily rewards settings", status=404)
            
        # Parse JSON content
        try:
            import json
            content = json.loads(data.read().decode('utf-8'))
        except Exception as e:
            return create_response(f"Failed to parse daily rewards settings: {str(e)}", status=500)
        
        # Only return the content without version information for app endpoint
        return create_response("Daily rewards settings retrieved successfully", data=content)
    

@app_global_settings_api.doc(security='bearer')
@app_global_settings_api.route('/daily_checkin/current')
class DailyCheckinSettings(Resource):
    @cache.cached(timeout=3600, key_prefix='global_setting:daily_checkin')
    def get(self):
        """Get daily checkin global settings for app"""
        
        versions = storage.list_files('global_settings/daily_checkin/versions')
        
        if not versions:
            return create_response("No daily checkin versions found", status=404)
            
        # Get the latest version by sorting on last_modified
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        latest_version = versions[0]
        
        # Get the content of the latest version
        data = storage.download_file(f"global_settings/daily_checkin/versions", latest_version['name'].split('/')[-1])
        if not data:
            return create_response("Failed to download daily checkin settings", status=404)
            
        # Parse JSON content
        try:
            import json
            content = json.loads(data.read().decode('utf-8'))
        except Exception as e:
            return create_response(f"Failed to parse daily checkin settings: {str(e)}", status=500)
        
        # Only return the content without version information for app endpoint
        return create_response("Daily checkin settings retrieved successfully", data=content)
    

# @app_global_settings_api.doc(security='bearer')
@app_global_settings_api.route('/extra_navigation/current')
class ExtraNavigationSettings(Resource):
    @cache.cached(timeout=3600, key_prefix='global_setting:extra_navigation')
    def get(self):
        """Get extra navigation global settings for app"""
        
        versions = storage.list_files('global_settings/extra_navigation/versions')
        
        if not versions:
            return create_response("No extra navigation versions found", status=404)
            
        # Get the latest version by sorting on last_modified
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        latest_version = versions[0]
        
        # Get the content of the latest version
        data = storage.download_file(f"global_settings/extra_navigation/versions", latest_version['name'].split('/')[-1])
        if not data:
            return create_response("Failed to download extra navigation settings", status=404)
            
        # Parse JSON content
        try:
            import json
            content = json.loads(data.read().decode('utf-8'))
        except Exception as e:
            return create_response(f"Failed to parse extra navigation settings: {str(e)}", status=500)
        
        # Only return the content without version information for app endpoint
        return create_response("Extra navigation settings retrieved successfully", data=content)


@app_global_settings_api.doc(security='bearer')
@app_global_settings_api.route('/store/versions')
class StoreVersions(Resource):
    @cache.cached(timeout=3600, key_prefix='global_setting:store_versions')
    def get(self):
        """Get store versions for app"""
        
        versions = storage.list_files('global_settings/store/versions')
        
        if not versions:
            return create_response("No store versions found", status=404)
        
         # Get the latest version by sorting on last_modified
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        latest_version = versions[0]

        # Only return the content without version information for app endpoint
        data = storage.download_file(f"global_settings/store/versions", latest_version['name'].split('/')[-1])
        if not data:
            return create_response("Failed to download store versions", status=404)
        
        # Parse JSON content
        try:
            import json
            content = json.loads(data.read().decode('utf-8'))
        except Exception as e:
            return create_response(f"Failed to parse store versions: {str(e)}", status=500)

            
        return create_response("Store versions retrieved successfully", data=content)


@app_global_settings_api.doc(security='bearer')
@app_global_settings_api.route('/')
class Settings(Resource):
    @cache.cached(timeout=3600, key_prefix='global_setting:settings')
    def get(self):
        """Get settings"""
        try:
            db_session = get_db_session()

            settings = db_session.query(Setting).filter(
                Setting.key.in_([
                    'minimum_app_version',
                    'current_app_version',
                    'company_xircle_enabled',
                    'current_city_version',
                    'current_school_version'
                ]),
                Setting.is_deleted == False
            ).all()
            
            result = {
                'app_version': {
                    'minimum': "",
                    'current': ""
                },
                'company_xircle': {
                    'enabled': False  # Default to False if not set
                },
                'city_version': {
                    'current': ""
                },
                'school_version': {
                    'current': ""
                }
            }
            
            for setting in settings:
                if setting.key == 'minimum_app_version':
                    result['app_version']['minimum'] = setting.value
                elif setting.key == 'current_app_version':
                    result['app_version']['current'] = setting.value
                elif setting.key == 'company_xircle_enabled':
                    result['company_xircle']['enabled'] = setting.value.lower() == 'true'
                elif setting.key == 'current_city_version':
                    result['city_version']['current'] = setting.value
                elif setting.key == 'current_school_version':
                    result['school_version']['current'] = setting.value
            return create_response("Settings retrieved successfully", data=result)
            
        except Exception as e:
            return create_response(f"Failed to retrieve settings: {str(e)}", status=500)