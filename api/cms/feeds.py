import datetime

from flask import g, request
from flask_restx import Namespace, Resource, fields, reqparse

from api.common.decorator import check_user_permission
from api.common.file import FileService
from api.common.helper import create_response, parse_bool_or_none
from api.common.publish_utils import publish_xapa_feed, publish_seniority_feed
from api.common.task import TaskFunction
from clientmodels import Feed, FeedComment, FeedLike, FeedFlag, Xircle, User, Task, FeedSeniority

cms_feeds_api = Namespace('api_cms_feed', description='Feed management related operations')


## feed model
feed_model = cms_feeds_api.model('FeedItem', {
    'message': fields.String(required=True, description='Feed Message'),
    'image': fields.String(required=False, description='Feed Image'),
    'media': fields.String(required=False, description='Feed Media, Video'),
    'xircle_id': fields.String(required=False, description='Xircle Id, optional'),
    'start_time': fields.String(required=False, description='Start Time, optional'),
    'end_time': fields.String(required=False, description='End Time, optional'),
    'link': fields.String(required=False, description='Feed Link, optional'),
    'link_type': fields.String(required=False, description='Feed Link Type, optional'),
    'label': fields.String(required=False, description='Feed Label, optional')
})

## create, edit, delete and get feed
@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/', methods=['POST'])
@cms_feeds_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class FeedObject(Resource):
    def get(self, id):
        """Get a feed"""
        from sqlalchemy import func, and_

        # Subqueries for counts and interactions
        likes_count = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('likes_count')
            )
            .filter(FeedLike.feed_id == id)
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        comments_count = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('comments_count')
            )
            .filter(FeedComment.feed_id == id, FeedComment.is_deleted == False)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        flags_count = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('flags_count')
            )
            .filter(FeedFlag.feed_id == id)
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        # Subquery for xircle creators
        xircle_creators = (
            g.client_db_session.query(
                Xircle.id.label('xircle_id'),
                Xircle.creator_id
            ).subquery()
        )

        # Main query combining all subqueries
        feed_query = (
            g.client_db_session.query(
                Feed,
                User,
                Xircle,
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count')
            )
            .join(User, Feed.user_id == User.id)
            .join(Xircle, Feed.xircle_id == Xircle.id)
            .outerjoin(likes_count, Feed.id == likes_count.c.feed_id)
            .outerjoin(comments_count, Feed.id == comments_count.c.feed_id)
            .outerjoin(flags_count, Feed.id == flags_count.c.feed_id)
            .outerjoin(xircle_creators, Feed.xircle_id == xircle_creators.c.xircle_id)
            .filter(Feed.id == id, Feed.is_deleted == False, Xircle.is_deleted == False)
            .first()
        )

        scheduled_feed_query = (
            g.db_session.query(
                Task
            ).filter(
                Task.is_deleted == False
            )
        )

        if feed_query is None:
            return create_response("Feed not found", status=404)
        
        feed, user, xircle, likes, comments, flags = feed_query
        
        data = feed.to_dict(['id', 'message', 'image', 'media', 'date_created', 'date_updated', 'status', 'link', 'link_type', 'label'])
        data['likes_count'] = int(likes)
        data['comments_count'] = int(comments)
        data['flags_count'] = int(flags)
        data['user'] = user.to_dict(['id', 'first_name', 'last_name', 'image', 'title'])
        data['xircle'] = xircle.to_dict(['id', 'name', 'image'])
        
        scheduled_tasks = scheduled_feed_query.filter(Task.data['feed_id'].astext == feed.id).all()
        data['scheduled_for'] = [
            scheduled_task.to_dict(['name', 'scheduled_for', 'status']) for scheduled_task in scheduled_tasks
        ] if scheduled_tasks else []

        return create_response("Get feed", data=data)

    @cms_feeds_api.expect(feed_model)
    # @check_user_permission('admin')
    def post(self):
        """Create a new feed"""
        data = request.json

        message = data.get('message').strip()
        image = data.get('image', '')
        media = data.get('media', '')
        xircle_id = data.get('xircle_id', '')
        start_time = data.get('start_time', None)
        end_time = data.get('end_time', None)
        link = data.get('link', '')
        link_type = data.get('link_type', '')
        label = data.get('label', '')

        start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%SZ') if start_time else None
        if start_time and start_time < datetime.datetime.utcnow():
            return create_response("Start time cannot be in the past", status=400)
        
        end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%SZ') if end_time else None
        if end_time and end_time < datetime.datetime.utcnow():
            return create_response("End time cannot be in the past", status=400)

        if start_time and end_time and start_time > end_time:
            return create_response("Start time cannot be greater than end time", status=400)
        
        user = g.client_db_session.query(User).filter_by(id='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.client_db_session.add(user)
            g.client_db_session.commit()

        # Get global Xapa xircle
        xircle = g.client_db_session.query(Xircle).filter_by(id=xircle_id).first()
        if not xircle:
            return create_response("Xapa xircle not found", status=404)

        feed = Feed()
        feed.message = message
        feed.xircle_id = xircle.id
        feed.link = link
        feed.link_type = link_type
        feed.label = label

        ## creator is the current user
        feed.user_id = user.id

        ## save image
        ## move the image from the temp folder to user's feed folder
        FileService.process_entity_image(feed, image, 'feed', feed.id)

        g.client_db_session.add(feed)
        g.client_db_session.flush()

        ## save media
        ## move the media from the temp folder to user's feed folder
        # TODO: save media

        tf = TaskFunction()
        task_data = {
            'tenant_id': g.tenant_id,
            'feed_id': feed.id,
        }

        if start_time:
            feed.status = 'scheduled'
            tf.create_task('start_time', 'post_feed', task_data, start_time)
        else:
            # Update xircle's date_updated if feed is posted to a xircle
            xircle.date_updated = datetime.datetime.utcnow()

        if end_time:
            tf.create_task('end_time', 'delete_feed', task_data, end_time)
        
        g.client_db_session.commit()

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==feed.xircle_id, Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if xircle:
            publish_xapa_feed(feed.id)

        data = feed.to_dict()
        return create_response("Feed created", data=data)

    @cms_feeds_api.expect(feed_model)
    # @check_user_permission('admin')
    def put(self, id):
        """Update a feed"""
        feed = g.client_db_session.query(Feed).filter_by(id=id).first()
        if feed is None:
            return create_response("Feed not found", status=404)
        
        data = request.json

        message = data.get('message').strip()
        image = data.get('image', '')
        media = data.get('media', '')
        start_time = data.get('start_time', None)
        end_time = data.get('end_time', None)
        link = data.get('link', '')
        link_type = data.get('link_type', '')
        label = data.get('label', '')

        # Parse start and end times
        start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%SZ') if start_time else None
        if start_time and start_time < datetime.datetime.utcnow() and feed.status == 'scheduled':
            return create_response("Start time cannot be in the past", status=400)
        
        end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%SZ') if end_time else None
        if end_time and end_time < datetime.datetime.utcnow() and feed.status == 'scheduled':
            return create_response("End time cannot be in the past", status=400)

        if start_time and end_time and start_time > end_time and feed.status == 'scheduled':
            return create_response("Start time cannot be greater than end time", status=400)

        # Update image if changed
        FileService.process_entity_image(feed, image, 'feed', feed.id)

        # Update message
        feed.message = message
        feed.link = link
        feed.link_type = link_type
        feed.label = label

        # Manage scheduled tasks
        tf = TaskFunction()
        task_data = {
            'tenant_id': g.tenant_id,
            'feed_id': feed.id,
        }
        
        if feed.status == 'scheduled':
            # Remove existing scheduled tasks for this feed
            existing_tasks = g.db_session.query(Task).filter(
                Task.data['feed_id'].astext == feed.id
            ).all()
            for task in existing_tasks:
                tf.delete_task(task.id)

            # Recreate scheduled tasks if start or end times are provided
            if start_time:
                tf.create_task('start_time', 'post_feed', task_data, start_time)
            
            if end_time:
                tf.create_task('end_time', 'delete_feed', task_data, end_time)

        g.client_db_session.commit()

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==feed.xircle_id, Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if xircle:
            publish_xapa_feed(feed.id)

        return create_response("Feed updated successfully")

    # @check_user_permission('admin')
    def delete(self, id):
        """Delete a feed"""
        feed = g.client_db_session.query(Feed).filter_by(id=id).first()
        if feed is None:
            return create_response("Feed not found", status=404)

        feed.delete()
        g.client_db_session.commit()

        xircle = g.client_db_session.query(Xircle).filter(Xircle.id==feed.xircle_id, Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if xircle:
            publish_xapa_feed(feed.id)

        return create_response("Feed deleted")

comment_parser = reqparse.RequestParser()
comment_parser.add_argument('page', type=int, help='Page Number', default=1)
comment_parser.add_argument('limit', type=int, help='Limit per page', default=20)

comment_model = cms_feeds_api.model('FeedCommentItem', {
    'message': fields.String(required=True, description='Feed Comment Message')
})

## create, edit, delete and get feed comment
@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/<string:feed_id>/comment/<string:id>', methods=['PUT', 'DELETE'])
@cms_feeds_api.route('/<string:feed_id>/comment/list', methods=['GET'])
class FeedCommentObject(Resource):
    @cms_feeds_api.expect(comment_parser)
    def get(self, feed_id):
        """Get list of comments for a feed"""
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        offset = (page - 1) * limit

        # Query comments with user information
        feed_comments_query = (
            g.client_db_session.query(FeedComment, User)
            .join(
                User, 
                FeedComment.user_id == User.id
            )
            .filter(
                FeedComment.feed_id == feed_id,
                FeedComment.is_deleted == False
            )
        )

        total = feed_comments_query.count()
        feed_comments = feed_comments_query.order_by(FeedComment.date_created.desc()).limit(limit).offset(offset).all()
        
        data = []
        for comment, user in feed_comments:
            comment_dict = comment.to_dict(['id', 'message', 'date_created', 'date_updated'])
            comment_dict['user'] = user.to_dict(['id', 'first_name', 'last_name', 'image', 'title'])
            data.append(comment_dict)

        return create_response("Feed Comment list", data=data, total=total, page=page, limit=limit)

    @cms_feeds_api.expect(comment_model)
    # @check_user_permission('admin')
    def put(self, feed_id, id):
        """Edit a feed comment"""
        feed = g.client_db_session.query(Feed).filter_by(id=feed_id).first()
        if feed is None:
            return create_response("Feed not found", status=404)

        feed_comment = g.client_db_session.query(FeedComment).filter_by(id=id).first()
        if feed_comment is None:
            return create_response("Feed Comment not found", status=404)
        
        data = request.json

        message = data.get('message').strip()
        feed_comment.message = message

        g.client_db_session.commit()

        return create_response("Feed Comment updated successfully")

    # @check_user_permission('admin')
    def delete(self, feed_id, id):
        """Delete a feed comment"""
        feed = g.client_db_session.query(Feed).filter_by(id=feed_id).first()
        if feed is None:
            return create_response("Feed not found", status=404)

        feed_comment = g.client_db_session.query(FeedComment).filter_by(id=id).first()
        if feed_comment is None:
            return create_response("Feed Comment not found", status=404)
        
        feed_comment.is_deleted = True
        g.client_db_session.commit()

        return create_response("Feed Comment deleted")

## feed flag list parser
flag_parser = reqparse.RequestParser()
flag_parser.add_argument('page', type=int, help='Page Number', default=1)
flag_parser.add_argument('limit', type=int, help='Limit per page', default=20)

## create, edit, delete and get feed flag
@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/<string:feed_id>/flag/<string:id>', methods=['DELETE'])
@cms_feeds_api.route('/<string:feed_id>/flag/list', methods=['GET'])
class FeedFlagObject(Resource):
    @cms_feeds_api.expect(flag_parser)
    def get(self, feed_id):
        """Get list of flags for a feed"""
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        offset = (page - 1) * limit

        # Query flags with user information
        query = g.client_db_session.query(FeedFlag).filter(FeedFlag.feed_id == feed_id)

        total = query.count()
        feed_flags = query.order_by(FeedFlag.date_created.desc()).limit(limit).offset(offset).all()
        
        data = []
        for flag in feed_flags:
            flag_dict = flag.to_dict(['id', 'message', 'date_created'])
            flag_dict['user'] = flag.user.to_dict(['id', 'first_name', 'last_name', 'image', 'title'])
            data.append(flag_dict)

        return create_response("Feed Flag list", data=data, total=total, page=page, limit=limit)

    # @check_user_permission('admin')
    def delete(self, feed_id, id):
        """Delete a feed flag"""
        feed_flag = g.client_db_session.query(FeedFlag).filter_by(id=id).first()
        if feed_flag is None:
            return create_response("Feed Flag not found", status=404)
        
        g.client_db_session.delete(feed_flag)
        g.client_db_session.commit()

        return create_response("Feed Flag deleted")

## get feed list
parser = reqparse.RequestParser()
parser.add_argument('page', type=int, help='Page Number', default=1)
parser.add_argument('limit', type=int, help='Limit per page', default=20)
parser.add_argument('search', type=str, help='Search query', default='')
parser.add_argument('xircle_id', type=str, help='Xircle Id, optional')
parser.add_argument('status', type=str, help='Status, optional (active, scheduled)', default=None)
parser.add_argument('schedule_type', type=str, help='Schedule type, optional (immediate, scheduled)', default=None)
parser.add_argument('has_image', type=parse_bool_or_none, help='Filter feeds with image', default=None)
parser.add_argument('has_comments', type=parse_bool_or_none, help='Filter feeds with comments', default=None)
parser.add_argument('flagged', type=parse_bool_or_none, help='Filter flagged feeds', default=None)

@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/list', methods=['GET'])
class FeedList(Resource):
    @cms_feeds_api.expect(parser)
    def get(self):
        """Get list of feeds"""
        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        offset = (page - 1) * limit
        search = args.get('search', '')
        xircle_id = args.get('xircle_id', None)
        status = args.get('status', None)
        schedule_type = args.get('schedule_type', None)
        has_image = args.get('has_image', None)
        has_comments = args.get('has_comments', None)
        flagged = args.get('flagged', None)

        from sqlalchemy import func, or_, and_, exists

        # Subquery for total likes count
        likes_count = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('likes_count')
            )
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        # Subquery for total comments count
        comments_count = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('comments_count')
            )
            .filter(FeedComment.is_deleted == False)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        # Subquery for total flags count
        flags_count = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('flags_count')
            )
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        # Subquery to get xircle creators
        xircle_creators = (
            g.client_db_session.query(
                Xircle.id.label('xircle_id'),
                Xircle.creator_id
            ).subquery()
        )

        # Main query with all joins
        query = (
            g.client_db_session.query(
                Feed,
                User,
                Xircle,
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count')
            )
            .join(User, Feed.user_id == User.id)
            .outerjoin(Xircle, Feed.xircle_id == Xircle.id)
            .outerjoin(likes_count, Feed.id == likes_count.c.feed_id)
            .outerjoin(comments_count, Feed.id == comments_count.c.feed_id)
            .outerjoin(flags_count, Feed.id == flags_count.c.feed_id)
            .outerjoin(xircle_creators, Feed.xircle_id == xircle_creators.c.xircle_id)
        )

        scheduled_feed_query = (
            g.client_db_session.query(
                Task
            ).filter(
                Task.is_deleted == False
            )
        )

        # Filter by xircle_id if provided, otherwise show feeds from xircles user has joined
        if xircle_id:
            query = query.filter(Feed.xircle_id == xircle_id)

        if status is not None:
            query = query.filter(Feed.status == status)

        # Apply schedule_type filter
        if schedule_type is not None:
            if schedule_type == 'scheduled':
                query = query.filter(exists().where(and_(
                    Task.data['feed_id'].astext == Feed.id,
                    Task.is_deleted == False
                )))
            elif schedule_type == 'immediate':
                query = query.filter(~exists().where(and_(
                    Task.data['feed_id'].astext == Feed.id,
                    Task.is_deleted == False
                )))

        # Apply search filter if provided
        if search:
            query = query.filter(Feed.message.ilike(f'%{search}%'))

        # Apply image filter
        if has_image is not None:
            if has_image:
                query = query.filter(Feed.image.isnot(None), Feed.image != '')
            else:
                query = query.filter(or_(Feed.image.is_(None), Feed.image == ''))

        # Apply comments filter
        if has_comments is not None:
            if has_comments:
                query = query.filter(comments_count.c.comments_count > 0)
            else:
                query = query.filter(or_(comments_count.c.comments_count.is_(None), comments_count.c.comments_count == 0))

        # Apply flagged filter
        if flagged is not None:
            if flagged:
                query = query.filter(flags_count.c.flags_count > 0)
            else:
                query = query.filter(or_(flags_count.c.flags_count.is_(None), flags_count.c.flags_count == 0))

        query = query.filter(Feed.is_deleted == False)

        total = query.count()
        results = query.order_by(Feed.date_created.desc()).limit(limit).offset(offset).all()
        
        data = []
        for feed, user, xircle, likes, comments, flags in results:
            feed_dict = feed.to_dict(['id', 'message', 'image', 'media', 'date_created', 'date_updated', 'status', 'link', 'link_type', 'label'])
            feed_dict['likes_count'] = int(likes)
            feed_dict['comments_count'] = int(comments)
            feed_dict['flags_count'] = int(flags)
            feed_dict['user'] = user.to_dict(['id', 'first_name', 'last_name', 'image', 'title'])
            feed_dict['xircle'] = xircle.to_dict(['id', 'name', 'image'])

            if schedule_type == 'scheduled':
                scheduled_tasks = scheduled_feed_query.filter(Task.data['feed_id'].astext == feed.id).all()
                feed_dict['scheduled_for'] = [
                    scheduled_task.to_dict(['name', 'scheduled_for', 'status']) for scheduled_task in scheduled_tasks
                ]

            data.append(feed_dict)
        
        return create_response("Feed list", data=data, total=total, page=page, limit=limit)


@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/xapa')
class XapaFeedObject(Resource):
    @cms_feeds_api.expect(feed_model)
    # @check_user_permission('super_admin')
    def post(self):
        """Create a new xapa feed"""
        data = request.json

        message = data.get('message').strip()
        image = data.get('image', '')
        media = data.get('media', '')
        start_time = data.get('start_time', None)
        end_time = data.get('end_time', None)
        link = data.get('link', '')
        link_type = data.get('link_type', '')
        label = data.get('label', '')

        start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%SZ') if start_time else None
        if start_time and start_time < datetime.datetime.utcnow():
            return create_response("Start time cannot be in the past", status=400)
        
        end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%SZ') if end_time else None
        if end_time and end_time < datetime.datetime.utcnow():
            return create_response("End time cannot be in the past", status=400)

        if start_time and end_time and start_time > end_time:
            return create_response("Start time cannot be greater than end time", status=400)

        user = g.db_session.query(User).filter_by(id='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.db_session.add(user)
            g.db_session.commit()

        # Get global Xapa xircle
        xircle = g.db_session.query(Xircle).filter(Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if not xircle:
            xircle = Xircle()
            xircle.name = 'Xapa'
            xircle.is_public = True
            xircle.include_leaderboard = True
            xircle.creator_id = user.id
            g.db_session.add(xircle)
            g.db_session.commit()

        feed = Feed()
        feed.message = message
        feed.link = link
        feed.link_type = link_type
        feed.label = label
        feed.user_id = user.id
        feed.xircle_id = xircle.id

        # save image
        # move the image from the temp folder to user's feed folder
        FileService.process_entity_image(feed, image, 'feed', feed.id)

        g.db_session.add(feed)
        g.db_session.commit()

        ## save media
        ## move the media from the temp folder to user's feed folder
        # TODO: save media

        tf = TaskFunction()
        task_data = {
            'tenant_id': g.tenant_id,
            'feed_id': feed.id,
        }

        if start_time:
            feed.status = 'scheduled'
            tf.create_task('start_time', 'post_feed', task_data, start_time)
        else:
            xircle.date_updated = datetime.datetime.utcnow()
            publish_xapa_feed(feed.id)

        if end_time:
            tf.create_task('end_time', 'delete_feed', task_data, end_time)
        
        g.db_session.commit()

        data = feed.to_dict()
        return create_response("Feed created", data=data)

@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/company')
class CompanyFeedObject(Resource):
    @cms_feeds_api.expect(feed_model)
    # @check_user_permission('admin')
    def post(self):
        """Create a new company feed"""
        data = request.json

        message = data.get('message').strip()
        image = data.get('image', '')
        media = data.get('media', '')
        start_time = data.get('start_time', None)
        end_time = data.get('end_time', None)
        link = data.get('link', '')
        link_type = data.get('link_type', '')
        label = data.get('label', '')

        start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%SZ') if start_time else None
        if start_time and start_time < datetime.datetime.utcnow():
            return create_response("Start time cannot be in the past", status=400)
        
        end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%SZ') if end_time else None
        if end_time and end_time < datetime.datetime.utcnow():
            return create_response("End time cannot be in the past", status=400)

        if start_time and end_time and start_time > end_time:
            return create_response("Start time cannot be greater than end time", status=400)

        user = g.client_db_session.query(User).filter_by(id='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.client_db_session.add(user)
            g.client_db_session.commit()

        # Get global Xapa xircle
        xircle = g.client_db_session.query(Xircle).filter_by(id='00000000-0000-0000-0000-000000000000').first()
        if not xircle:
            xircle = Xircle()
            xircle.name = 'Company Xircle'
            xircle.is_public = True
            xircle.include_leaderboard = True
            xircle.creator_id = user.id
            g.client_db_session.add(xircle)
            g.client_db_session.commit()

        feed = Feed()
        feed.message = message
        feed.link = link
        feed.link_type = link_type
        feed.label = label
        feed.user_id = user.id
        feed.xircle_id = xircle.id

        # save image
        # move the image from the temp folder to user's feed folder
        FileService.process_entity_image(feed, image, 'feed', feed.id)

        g.client_db_session.add(feed)
        g.client_db_session.flush()

        # save media
        # move the media from the temp folder to user's feed folder
        # TODO: save media

        tf = TaskFunction()
        task_data = {
            'tenant_id': g.tenant_id,
            'feed_id': feed.id,
        }
        if start_time:
            feed.status = 'scheduled'
            tf.create_task('start_time', 'post_feed', task_data, start_time)
        else:
            xircle.date_updated = datetime.datetime.utcnow()

        if end_time:
            tf.create_task('end_time', 'delete_feed', task_data, end_time)
        
        g.client_db_session.commit()

        data = feed.to_dict()
        return create_response("Feed created", data=data)


# Add new model for seniority template
seniority_model = cms_feeds_api.model('SeniorityTemplateItem', {
    'days': fields.Integer(required=True, description='Days milestone'),
    'message': fields.String(required=True, description='Template message'),
    'image': fields.String(required=False, description='Template image'),
    'media': fields.String(required=False, description='Template media'),
})

@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/seniority', methods=['POST'])
@cms_feeds_api.route('/seniority/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class FeedSeniorityObject(Resource):
    # @check_user_permission('super_admin')
    def get(self, id):
        """Get all seniority templates"""
        from sqlalchemy import func

        # Subqueries for counts
        likes_count = (
            g.db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('likes_count')
            )
            .filter(FeedLike.feed_id == id)
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        comments_count = (
            g.db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('comments_count')
            )
            .filter(FeedComment.feed_id == id, FeedComment.is_deleted == False)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        flags_count = (
            g.db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('flags_count')
            )
            .filter(FeedFlag.feed_id == id)
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        feed_query = (
            g.db_session.query(
                FeedSeniority,
                User,
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count')
            )
            .join(User, User.id == FeedSeniority.user_id)
            .outerjoin(likes_count, FeedSeniority.id == likes_count.c.feed_id)
            .outerjoin(flags_count, FeedSeniority.id == flags_count.c.feed_id)
            .outerjoin(comments_count, FeedSeniority.id == comments_count.c.feed_id)
            .filter(
                FeedSeniority.id == id,
                FeedSeniority.is_deleted == False
            ).first()
        )

        if not feed_query:
            return create_response("Seniority not found", status=404)

        feed, user, likes, flags, comments = feed_query

        data = feed.to_dict(['id', 'days', 'message', 'image', 'media', 'date_created', 'date_updated', 'link', 'link_type', 'label'])
        data['user'] = user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title'])
        data['likes_count'] = int(likes)
        data['flags_count'] = int(flags)
        data['comments_count'] = int(comments)
        
        return create_response("Seniority templates retrieved", data=data)

    @cms_feeds_api.expect(seniority_model)
    # @check_user_permission('super_admin')
    def post(self):
        """Create new seniority template"""
        data = request.json

        message = data.get('message', None)
        days = data.get('days', None)
        image = data.get('image', '')
        media = data.get('media', '')
        link = data.get('link', '')
        link_type = data.get('link_type', '')
        label = data.get('label', '')

        user = g.db_session.query(User).filter_by(id='00000000-0000-0000-0000-000000000000').first()
        if not user:
            user = User()
            user.id = '00000000-0000-0000-0000-000000000000'
            user.first_name = 'Xapa'
            user.email = 'admin@xapa'
            user.is_active = False
            user.is_deleted = False
            g.db_session.add(user)
            g.db_session.commit()

        xircle = g.db_session.query(Xircle).filter(Xircle.name.ilike('xapa'), Xircle.is_deleted==False).first()
        if not xircle:
            return create_response("Xircle not found", status=404)

        feed = FeedSeniority()
        feed.message = message
        feed.days = days
        feed.media = media
        feed.link = link
        feed.link_type = link_type
        feed.label = label
        feed.user_id = user.id
        feed.xircle_id = xircle.id
        
        FileService.process_entity_image(feed, image, 'feedseniority', feed.id)

        g.db_session.add(feed)
        g.db_session.commit()

        # Publish to client databases
        success, message = publish_seniority_feed(feed.id)

        if not success:
            return create_response(message, status=500)

        return create_response("Seniority created", data=feed.to_dict())

    @cms_feeds_api.expect(seniority_model)
    # @check_user_permission('super_admin')
    def put(self, id):
        """Update seniority feed"""
        feed = g.db_session.query(FeedSeniority).filter_by(id=id).first()
        if not feed:
            return create_response("Seniority not found", status=404)

        data = request.json

        days = data.get('days', None)
        message = data.get('message', None)
        media = data.get('media', None)
        image = data.get('image', None)
        link = data.get('link', None)
        link_type = data.get('link_type', None)
        label = data.get('label', None)

        FileService.process_entity_image(feed, image, 'feedseniority', feed.id)
        
        feed.days = days
        feed.message = message
        feed.link = link
        feed.link_type = link_type
        feed.label = label

        g.db_session.commit()

        # Publish to client databases
        success, message = publish_seniority_feed(feed.id)

        if not success:
            return create_response(message, status=500)

        return create_response("Seniority updated", data=feed.to_dict())

    # @check_user_permission('admin')
    def delete(self, id):
        """Delete seniority feed"""
        feed = g.db_session.query(FeedSeniority).filter_by(id=id).first()

        if not feed:
            return create_response("Seniority not found", status=404)

        feed.is_deleted = True
        g.db_session.commit()

        # Publish deletion to client databases
        success, message = publish_seniority_feed(feed.id)

        if not success:
            return create_response(message, status=500)

        return create_response("Seniority deleted")

seniority_parser = reqparse.RequestParser()
seniority_parser.add_argument('page', type=int, help='Page Number', default=1)
seniority_parser.add_argument('limit', type=int, help='Limit per page', default=20)
seniority_parser.add_argument('search', type=str, help='Search query', default='')

@cms_feeds_api.doc(security='bearer')
@cms_feeds_api.route('/seniority/list', methods=['GET'])
class SeniorityFeedList(Resource):
    @cms_feeds_api.expect(seniority_parser)
    def get(self):
        """Get list of seniority feeds"""

        args = seniority_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        offset = (page - 1) * limit
        search = args.get('search', '')

        from sqlalchemy import func

        # Subqueries for counts and interactions
        likes_count = (
            g.client_db_session.query(
                FeedLike.feed_id,
                func.count(FeedLike.id).label('likes_count')
            )
            .filter(FeedLike.feed_id == FeedSeniority.id)
            .group_by(FeedLike.feed_id)
            .subquery()
        )

        comments_count = (
            g.client_db_session.query(
                FeedComment.feed_id,
                func.count(FeedComment.id).label('comments_count')
            )
            .filter(FeedComment.feed_id == FeedSeniority.id, FeedComment.is_deleted == False)
            .group_by(FeedComment.feed_id)
            .subquery()
        )

        flags_count = (
            g.client_db_session.query(
                FeedFlag.feed_id,
                func.count(FeedFlag.id).label('flags_count')
            )
            .filter(FeedFlag.feed_id == FeedSeniority.id)
            .group_by(FeedFlag.feed_id)
            .subquery()
        )

        query = (
            g.client_db_session.query(
                FeedSeniority,
                User,
                func.coalesce(likes_count.c.likes_count, 0).label('likes_count'),
                func.coalesce(flags_count.c.flags_count, 0).label('flags_count'),
                func.coalesce(comments_count.c.comments_count, 0).label('comments_count'),
            )
            .join(User, User.id == FeedSeniority.user_id)
            .outerjoin(likes_count, FeedSeniority.id == likes_count.c.feed_id)
            .outerjoin(flags_count, FeedSeniority.id == flags_count.c.feed_id)
            .outerjoin(comments_count, FeedSeniority.id == comments_count.c.feed_id)
            .filter(FeedSeniority.is_deleted == False)
            .order_by(
                FeedSeniority.days, 
                FeedSeniority.date_created
            )
        )

        if search:
            query = query.filter(FeedSeniority.message.ilike(f'%{search}%'))

        total = query.count()
        feeds = query.limit(limit).offset(offset).all()

        data = []
        for feed, user, likes, flags, comments in feeds:
            feed_dict = feed.to_dict(['id', 'days', 'message', 'image', 'media', 'date_created', 'date_updated', 'link', 'link_type', 'label'])
            feed_dict['user'] = user.to_dict(['id', 'first_name', 'last_name', 'preferred_name', 'image', 'title'])
            feed_dict['likes_count'] = int(likes)
            feed_dict['flags_count'] = int(flags)
            feed_dict['comments_count'] = int(comments)
            data.append(feed_dict)

        return create_response("Seniority list", data=data, total=total, page=page, limit=limit)
