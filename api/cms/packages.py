import datetime
from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.file import FileService
from api.common.helper import create_response
from clientmodels import ContentPublishLog, Package, Quest, Xperience, Program, User

cms_packages_api = Namespace('api_cms_packages', description='CMS package operations')



# Parser for list endpoint
list_parser = cms_packages_api.parser()
list_parser.add_argument('page', type=int, help='Page Number', default=1)
list_parser.add_argument('limit', type=int, help='Limit per page', default=20)
list_parser.add_argument('search', type=str, help='Search term', default='')
list_parser.add_argument('status', type=str, help='Filter by status')
list_parser.add_argument('sort', type=str, help='Sort by field')

@cms_packages_api.doc(security='bearer')
@cms_packages_api.route('/list', methods=['GET'])
class PackageList(Resource):
    @cms_packages_api.expect(list_parser)
    def get(self):
        """List all packages"""
        try:
            args = list_parser.parse_args()
            page = args.get('page', 1)
            limit = args.get('limit', 20)
            search = args.get('search', '')
            status = args.get('status', '')
            sort = args.get('sort', None)

            # Calculate offset
            offset = (page - 1) * limit

            from sqlalchemy.orm import aliased
            from sqlalchemy import or_
            create_user = aliased(User)
            update_user = aliased(User)

            # Build query
            query = g.db_session.query(
                Package,
                create_user,
                update_user
            ).outerjoin(
                create_user,
                create_user.id == Package.create_user
            ).outerjoin(
                update_user,
                update_user.id == Package.update_user
            ).filter(
                Package.is_deleted == False
            )

            # Apply filters
            if search:
                query = query.filter(
                    or_(
                        Package.name.ilike(f'%{search}%'),
                        Package.description.ilike(f'%{search}%')
                    )
                )
            
            if status:
                query = query.filter(Package.status == status)

            if sort:
                if sort.startswith('-'):
                    query = query.order_by(getattr(Package, sort[1:]).desc())
                else:
                    query = query.order_by(getattr(Package, sort))
            else:
                query = query.order_by(Package.date_created.desc())

            # Get total count
            total = query.count()

            # Get paginated results
            packages = query.offset(offset).limit(limit).all()

            # Format response
            data = []
            for package, create_user, update_user in packages:
                package_dict = package.to_dict()
                package_dict['create_user'] = create_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if create_user else None
                package_dict['update_user'] = update_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if update_user else None
                package_dict['xperience_count'] = len(package.xperiences)
                package_dict['program_count'] = len(package.programs)
                package_dict['quest_count'] = len(package.quests)
                data.append(package_dict)

            return create_response("get package list", data=data, total=total, page=page, limit=limit)

        except Exception as e:
            return create_response(str(e), status=500)


# Model for create/update package
package_model = cms_packages_api.model('Package', {
    'name': fields.String(required=True, description='Package name'),
    'description': fields.String(required=False, description='Package description'),
    'image': fields.String(required=False, description='Package image URL'),
    'status': fields.String(required=False, description='Package status'),
    'xperience_ids': fields.List(fields.String, required=False, description='List of xperience IDs'),
    'program_ids': fields.List(fields.String, required=False, description='List of program IDs'),
})

@cms_packages_api.doc(security='bearer')
@cms_packages_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
@cms_packages_api.route('/', methods=['POST'])
class PackageObject(Resource):
    def get(self, id):
        """Get package details"""
        try:
            package = g.db_session.query(Package).filter(
                Package.id == id,
                Package.is_deleted == False
            ).first()

            if not package:
                return create_response('Package not found', status=404)

            data = package.to_dict()
            data['xperiences'] = [x.to_dict(['id', 'name', 'description']) for x in package.xperiences]
            data['programs'] = [p.to_dict(['id', 'name', 'description']) for p in package.programs]

            return create_response("get package details", data=data)

        except Exception as e:
            return create_response(str(e), status=500)

    @cms_packages_api.expect(package_model)
    def post(self):
        """Create a new package"""
        try:
            data = request.json

            package = Package()
            package.name = data.get('name')
            package.description = data.get('description')

            image = data.get('image')
            FileService.process_entity_image(package, image, 'package', package.id)

            # Update xperiences if provided
            if data.get('xperience_ids', None) is not None:
                xperiences = g.db_session.query(Xperience).filter(
                    Xperience.id.in_(data['xperience_ids']),
                    Xperience.is_deleted == False,
                    Xperience.status == 'Published'
                ).all()
                package.xperiences = xperiences

            # Update programs if provided
            if data.get('program_ids', None) is not None:
                programs = g.db_session.query(Program).filter(
                    Program.id.in_(data['program_ids']),
                    Program.is_deleted == False
                ).all()
                package.programs = programs

            g.db_session.add(package)
            g.db_session.commit()

            ## save publish log of the package
            log = ContentPublishLog()
            log.content_id = package.id
            log.content_type = 'Package'
            log.user_id = g.user_id
            g.db_session.add(log)
            g.db_session.commit()


            return create_response("add package", data=package.to_dict())

        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

    @cms_packages_api.expect(package_model)
    def put(self, id):
        """Update package details and manage xperiences, programs, and quests"""
        try:
            data = request.json

            package = g.db_session.query(Package).filter(
                Package.id == id,
                Package.is_deleted == False
            ).first()

            if not package:
                return create_response('Package not found', status=404)

            # Update basic package info
            package.name = data.get('name')
            package.description = data.get('description')

            image = data.get('image', '').strip()
            FileService.process_entity_image(package, image, 'package', package.id)

            # Update xperiences if provided
            if data.get('xperience_ids', None) is not None:
                xperiences = g.db_session.query(Xperience).filter(
                    Xperience.id.in_(data['xperience_ids']),
                    Xperience.is_deleted == False,
                    Xperience.status == 'Published'
                ).all()
                package.xperiences = xperiences

            # Update programs if provided
            if data.get('program_ids', None) is not None:
                programs = g.db_session.query(Program).filter(
                    Program.id.in_(data['program_ids']),
                    Program.is_deleted == False
                ).all()
                package.programs = programs

            g.db_session.commit()

            # Return updated package with all relationships
            data = package.to_dict()
            data['xperiences'] = [x.to_dict(['id', 'name', 'description']) for x in package.xperiences]
            data['programs'] = [p.to_dict(['id', 'name', 'description']) for p in package.programs]

            return create_response("update package", data=data)

        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)

    def delete(self, id):
        """Delete a package"""
        try:
            package = g.db_session.query(Package).filter(
                Package.id == id,
                Package.is_deleted == False
            ).first()

            if not package:
                return create_response('Package not found', status=404)

            package.is_deleted = True
            g.db_session.commit()

            return create_response('Package deleted successfully')

        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)



# Model for publish package
publish_parser = cms_packages_api.parser()
publish_parser.add_argument('force', type=bool, help='Force publish even if no changes', default=False)

@cms_packages_api.doc(security='bearer')
@cms_packages_api.route('/<string:id>/publish', methods=['PUT'])
class PackagePublish(Resource):
    @cms_packages_api.expect(publish_parser)
    def put(self, id):
        """Publish package to clients"""
        try:
            package = g.db_session.query(Package).filter(
                Package.id == id,
                Package.is_deleted == False
            ).first()

            if not package:
                return create_response('Package not found', status=404)

            ## publish xperiences to clients
            from api.common.publish_utils import release_package_to_clients
            from sqlalchemy.orm import joinedload

            clients = package.clients

            ## get the last publish log
            last_publish_log = g.db_session.query(ContentPublishLog).filter(
                ContentPublishLog.content_id == package.id,
                ContentPublishLog.content_type == 'Package'
            ).order_by(ContentPublishLog.date_published.desc()).first()
            last_publish_date = last_publish_log.date_published if last_publish_log else datetime.datetime(2025, 1, 1) 

            force = request.args.get('force', 'false').lower() == 'true'
            if not force:
                release_package_to_clients(package, clients, last_publish_date)
            else:
                release_package_to_clients(package, clients)

            ## save publish log of the package
            log = ContentPublishLog()
            log.content_id = package.id
            log.content_type = 'Package'
            log.user_id = g.user_id
            g.db_session.add(log)
            g.db_session.commit()

            return create_response('Package published successfully')

        except Exception as e:
            g.db_session.rollback()
            return create_response(str(e), status=500)