import datetime
import secrets

from flask import g, json, request
from flask_restx import Namespace, Resource, fields

from api.common.decorator import check_user_permission
from api.common.file import FileService
from api.common.helper import create_response, format_datetime_with_timezone
from api.common.email import EmailService
from clientmodels import Client, MasterUser, User, Admin, Character, UserAssignment, UserStats, UserStreak, get_db_session, MASTER_TENANT
from clientmodels import EntitlementGroupUserAssignment

cms_users_api = Namespace('api_cms_users', description='User and Admin management related operations')


users_parser = cms_users_api.parser()
users_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
users_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
users_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')
users_parser.add_argument('sort', type=str, help='sort query', location='args', required=False, default='')
users_parser.add_argument('filter_type', type=str, help='filter type', location='args', required=False, default='')
users_parser.add_argument('filter_value', type=str, help='filter value', location='args', required=False, default='')


@cms_users_api.doc(security='bearer')
@cms_users_api.route('/list', methods=['GET'])
class UsersList(Resource):
    @cms_users_api.expect(users_parser)
    def get(self):
        args = users_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        sort = args.get('sort', '')
        filter_type = args.get('filter_type', '')
        filter_value = args.get('filter_value', '')
        
        from sqlalchemy import func

        query = g.client_db_session.query(User).filter(User.is_deleted == False)

        ## exclude any user that has related data in character table
        if g.tenant_id != MASTER_TENANT:
            character_users = g.client_db_session.query(Character.user_id).distinct().subquery()
            query = query.filter(~User.id.in_(character_users.select()))

        ## exclude admin user account
        query = query.filter(User.id != "********-0000-0000-0000-********0000")

        if search:
            ## search by first name, last name, email
            query = query.filter(
                func.concat(User.first_name, ' ', User.last_name).ilike(f"%{search}%") | 
                User.email.ilike(f"%{search}%") | 
                User.preferred_name.ilike(f"%{search}%")
            )

        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(User, sort[1:]).desc())
            else:
                query = query.order_by(getattr(User, sort))
        else:
            users = query.order_by(User.first_name)

        if filter_type and filter_value:
            if filter_type == 'role':
                query = query.join(Admin, User.id == Admin.user_id).filter(Admin.role == filter_value)
            elif filter_type == 'status':
                if filter_value == 'assigned':
                    query = query.join(UserAssignment, User.id == UserAssignment.user_id)
                elif filter_value == 'unassigned':
                    subquery = g.db_session.query(UserAssignment.user_id).distinct().subquery()
                    query = query.filter(~User.id.in_(subquery.select()))
                    query = query.filter(User.status != "lead")
                elif filter_value == "active":
                    query = query.filter(User.is_active == True)    
                elif filter_value == "inactive":
                    query = query.filter(User.is_active == False)
                elif filter_value == "lead":
                    ## lead users are users that are not assigned to any client and company is not empty
                    query = query.filter(User.company != None).filter(User.company != "")
                    subquery = g.db_session.query(UserAssignment.user_id).distinct().subquery()
                    query = query.filter(~User.id.in_(subquery.select()))
                elif filter_value == "deleted":
                    query = g.client_db_session.query(User).filter(User.is_deleted == True)
                else:
                    query = query.filter(User.status == filter_value)
            else:
                query = query.filter(getattr(User, filter_type) == filter_value)

        total = query.count()
        users = query.offset((page - 1) * limit).limit(limit).all()
        
        data = []
        for user in users:
            item = user.to_dict(["id", "first_name", "last_name", "email", "image", "company", "is_active", "date_updated", "status", "last_login", "sso_type"])
            ## get user admin info
            user_admin = g.client_db_session.query(Admin).filter_by(user_id=user.id).first()
            if user_admin:
                item['is_admin'] = True
                item['role'] = user_admin.role if user_admin.role else ""
                # item['permissions'] = user_admin.permissions if user_admin.permissions else []
            else:
                item['is_admin'] = False
                item['role'] = ""
                # item['permissions'] = []

            ## get user last streak
            user_streak = g.db_session.query(UserStreak).filter_by(user_id=user.id).order_by(UserStreak.date_created.desc()).first()
            if user_streak:
                item['last_active'] = format_datetime_with_timezone(user_streak.date_created)
            else:
                item['last_active'] = ""

            if item["sso_type"] == 'msal':
                item["sso_type"] = "Microsoft"

            data.append(item)

        return create_response("Users List", data=data, total=total, page=page, limit=limit)



user_model = cms_users_api.model('UserItem', {
    'first_name': fields.String(required=True, description='First Name'),
    'last_name': fields.String(required=True, description='Last Name'),
    'title': fields.String(required=False, description='Title'),
    'company': fields.String(required=False, description='Company'),
    'email': fields.String(required=True, description='Email'),
    'phone_number': fields.String(required=False, description='Phone'),
    'date_of_birth': fields.String(required=False, description='Birthday'),
    'image': fields.String(required=False, description='Profile Image'),
    'preferred_name': fields.String(required=False, description='Preferred Name'),
    'bio': fields.String(required=False, description='Bio'),
    'pronouns': fields.String(required=False, description='Pronouns'),
    'location': fields.String(required=False, description='Location'),
    'city': fields.String(required=False, description='City'),
    'state': fields.String(required=False, description='State'),
    'country': fields.String(required=False, description='Country'),
    'schools': fields.List(fields.Nested(cms_users_api.model('School', {
        'name': fields.String(required=True, description='School Name'),
        'country': fields.String(required=True, description='Country')
    })), required=False, description='List of Schools'),
    'children': fields.List(fields.String, required=False, description='List of Children'),
    'pets': fields.List(fields.String, required=False, description='List of Pets'),
    'diet': fields.List(fields.String, required=False, description='List of Dietary Preferences'),
    'activities': fields.List(fields.String, required=False, description='List of Activities'),
    'interests': fields.List(fields.String, required=False, description='List of Interests'),
    'entitlement_group_ids': fields.List(fields.String, required=False, description='List of Entitlement Group IDs'),
})


delete_parser = cms_users_api.parser()
delete_parser.add_argument('force', type=bool, help='force delete', location='args', required=False, default=False)

@cms_users_api.doc(security='bearer')
@cms_users_api.route('/', methods=['POST'])
@cms_users_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class UserObject(Resource):
    @cms_users_api.expect(user_model)
    def post(self):
        data = request.json
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        title = data.get('title', '').strip()
        company = data.get('company', '').strip()
        email = data.get('email', '').strip()
        phone_number = data.get('phone_number', '').strip()
        date_of_birth = data.get('date_of_birth', '').strip()
        image = data.get('image', '').strip()
        preferred_name = data.get('preferred_name', '').strip()
        bio = data.get('bio', '').strip()
        pronouns = data.get('pronouns', '').strip()
        location = data.get('location', '').strip()
        city = data.get('city', '').strip()
        state = data.get('state', '').strip()
        country = data.get('country', '').strip()
        schools = data.get('schools', [])
        children = data.get('children', [])
        pets = data.get('pets', [])
        diet = data.get('diet', [])
        activities = data.get('activities', [])
        interests = data.get('interests', [])

        if not first_name or not last_name or not email:
            return create_response("First Name, Last Name and Email are required", status=404)

        ## check if email exists
        user = g.client_db_session.query(User).filter_by(email=email).first()
        if user is not None:
            return create_response("Email already exists", status=404)

        user = User()
        user.first_name = first_name
        user.last_name = last_name
        user.preferred_name = preferred_name
        user.title = title
        user.company = company
        user.email = email
        user.phone_number = phone_number
        user.date_of_birth = datetime.datetime.strptime(date_of_birth, "%Y-%m-%d") if date_of_birth else None
        user.bio = bio
        user.pronouns = pronouns
        user.location = location
        user.city = city
        user.state = state
        user.country = country
        user.schools = schools
        user.children = children
        user.pets = pets
        user.diet = diet
        user.activities = activities
        user.interests = interests

        ## save image
        FileService.process_entity_image(user, image, 'user', user.id)

        user.is_active = False
        user.status = "unassigned"

        ## if user provided their company info, then update their status to lead
        if user.company:
            user.status = "lead"

        g.db_session.add(user)
        g.db_session.commit()

        ## sync user entitlment groups
        entitlement_group_ids = data.get('entitlement_group_ids', [])
        if entitlement_group_ids:
            for group_id in entitlement_group_ids:
                try:
                    assignment = EntitlementGroupUserAssignment()
                    assignment.user_id = user.id
                    assignment.entitlement_group_id = group_id
                    g.client_db_session.add(assignment)
                except Exception as e:
                    g.client_db_session.rollback()
                    print(f"Failed to assign entitlement group {group_id} to user {user.id}: {str(e)}")
                
            g.client_db_session.commit()

        return create_response("User created", data=user.to_dict())

    def get(self, id=None):
        from api.common.users_utils import get_user_entitlement_groups

        if id is None:
            return create_response("User ID is required", status=400)
            
        user = g.client_db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        data = user.to_dict()
        
        # Get user role
        user_admin = g.client_db_session.query(Admin).filter_by(user_id=user.id).first()
        if user_admin:
            data['role'] = user_admin.role if user_admin.role else ''
        else:
            data['role'] = ''

        ## get user entitlment group ids from entitlment group table
        entitlement_group_ids = []
        entitlement_group_ids = get_user_entitlement_groups(user)
        data['entitlement_group_ids'] = entitlement_group_ids
        return create_response("User details", data=data)
    
    @cms_users_api.expect(user_model)
    def put(self, id):
        data = request.json
        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)

        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        title = data.get('title', '').strip()
        company = data.get('company', '').strip()
        phone_number = data.get('phone_number', '').strip()
        date_of_birth = data.get('date_of_birth', '').strip()
        image = data.get('image', '').strip()
        preferred_name = data.get('preferred_name', '').strip()
        bio = data.get('bio', '').strip()
        pronouns = data.get('pronouns', '').strip()
        location = data.get('location', '').strip()
        city = data.get('city', '').strip()
        state = data.get('state', '').strip()
        country = data.get('country', '').strip()
        schools = data.get('schools', [])
        children = data.get('children', [])
        pets = data.get('pets', [])
        diet = data.get('diet', [])
        activities = data.get('activities', [])
        interests = data.get('interests', [])

        email = data.get('email', '').strip().lower()
        if email and email != user.email:
            existing_user = g.db_session.query(User).filter_by(email=email).first()
            if existing_user:
                return create_response("Email already exists", status=404)
        
        user.email = email

        user.first_name = first_name
        user.last_name = last_name
        user.title = title
        user.company = company
        user.phone_number = phone_number
        user.date_of_birth = datetime.datetime.strptime(date_of_birth, "%Y-%m-%d") if date_of_birth else None
        user.preferred_name = preferred_name
        user.bio = bio if bio else user.bio
        user.pronouns = pronouns if pronouns else user.pronouns
        user.location = location if location else user.location
        user.city = city if city else user.city
        user.state = state if state else user.state
        user.country = country if country else user.country
        user.schools = schools if schools else user.schools
        user.children = children if children else user.children
        user.pets = pets if pets else user.pets
        user.diet = diet if diet else user.diet
        user.activities = activities if activities else user.activities
        user.interests = interests if interests else user.interests

        ## save image
        FileService.process_entity_image(user, image, 'user', user.id)

        ## add user entitlement group or remove entitlement group
        entitlement_group_ids = data.get('entitlement_group_ids', [])
        if entitlement_group_ids:
            ## remove all entitlement groups for the user
            g.client_db_session.query(EntitlementGroupUserAssignment).filter_by(user_id=user.id).delete()
            for group_id in entitlement_group_ids:
                try:
                    assignment = EntitlementGroupUserAssignment()
                    assignment.user_id = user.id
                    assignment.entitlement_group_id = group_id
                    g.client_db_session.add(assignment)
                except Exception as e:
                    g.client_db_session.rollback()
                    print(f"Failed to assign entitlement group {group_id} to user {user.id}: {str(e)}")
                
        else:
            g.client_db_session.query(EntitlementGroupUserAssignment).filter_by(user_id=user.id).delete()
            g.client_db_session.commit()

        g.db_session.commit()

        return create_response("User updated", data=user.to_dict())
    
    @cms_users_api.expect(delete_parser)
    def delete(self, id):
        from api.common.users_utils import delete_user
        from clientmodels import Feed, FeedComment, FeedLike, FeedFlag, XircleMember
        
        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)

        args = delete_parser.parse_args()
        force = args.get('force', False)

        if force:
            delete_user(user)

            return create_response("User deleted")
        else:
            user.is_deleted = True
            user.is_active = False
            user.status = 'deleted'
            user.client_id = None

            client_assignments = g.db_session.query(UserAssignment).filter_by(user_id=user.id).all()
            for assignment in client_assignments:
                g.db_session.delete(assignment)

                ## remove user data from client db
                client = g.db_session.query(Client).filter_by(id=assignment.client_id).first()
                if not client:
                    continue
                tenant_id_key = client.id_key
                client_db_session = get_db_session(tenant_id_key)
                client_user = client_db_session.query(User).filter_by(id=user.id).first()
                if client_user:
                    ## remove user data from client tenant
                    ## Clear User Feed Comments
                    user_feed_comments = client_db_session.query(FeedComment).filter_by(user_id=user.id).all()
                    for user_feed_comment in user_feed_comments:
                        client_db_session.delete(user_feed_comment)

                    ## Clear User Feeds
                    user_feeds = client_db_session.query(Feed).filter_by(user_id=user.id).all()
                    for user_feed in user_feeds:
                        client_db_session.delete(user_feed)

                    ## Clear User Feed Likes
                    user_feed_likes = client_db_session.query(FeedLike).filter_by(user_id=user.id).all()
                    for user_feed_like in user_feed_likes:
                        client_db_session.delete(user_feed_like)

                    ## Clear User Feed Flags
                    user_feed_flags = client_db_session.query(FeedFlag).filter_by(user_id=user.id).all()
                    for user_feed_flag in user_feed_flags:
                        client_db_session.delete(user_feed_flag)

                    ## Clear User Xircles
                    user_xircles = client_db_session.query(XircleMember).filter_by(user_id=user.id).all()
                    for user_xircle in user_xircles:
                        client_db_session.delete(user_xircle)

                    ## Clear User Entitlement Groups
                    entitlement_groups = client_db_session.query(EntitlementGroupUserAssignment).filter_by(user_id=user.id).all()
                    for entitlement_group in entitlement_groups:
                        client_db_session.delete(entitlement_group)

                    client_db_session.commit()

                    client_db_session.delete(client_user)
                    client_db_session.commit()
                
            g.db_session.commit()
            return create_response("User soft deleted")


@cms_users_api.doc(security='bearer')
@cms_users_api.route('/me', methods=['GET'])
class UserMe(Resource):
    def get(self):
        user_id = g.user_id
        user = g.db_session.query(User).filter_by(id=user_id, is_deleted=False).first()
        if user is None:
            return create_response("User not found", status=404)
        
        data = user.to_dict()
        
        # Get user role
        user_admin = g.db_session.query(Admin).filter_by(user_id=user.id).first()
        if user_admin:
            data['role'] = user_admin.role if user_admin.role else ''
        else:
            data['role'] = ''

        ## get current tenant client id
        tenant_id = g.tenant_id
        data['client_id'] = ""
        if tenant_id:
            master_db_session = get_db_session(MASTER_TENANT)
            client = master_db_session.query(Client).filter_by(id_key=tenant_id).first()
            if client:
                data['client_id'] = client.id
        return create_response("User details", data=data)


# Add admin assignment model
admin_model = cms_users_api.model('AdminAssignment', {
    'role': fields.String(required=True, description='Admin role (e.g., "admin", "super_admin")')
})


@cms_users_api.doc(security='bearer')
@cms_users_api.route('/<string:id>/admin', methods=['POST', 'DELETE'])
class UserAdminAssignment(Resource):
    @cms_users_api.expect(admin_model)
    @check_user_permission("super_admin")
    def post(self, id):
        """Assign admin role to a user"""
        user = g.client_db_session.query(User).filter_by(id=id, is_deleted=False).first()
        if user is None:
            return create_response("User not found", status=404)

        data = request.json
        role = data.get('role', '').strip().lower()

        # Validate role
        valid_roles = ['admin', 'super_admin', 'writer', 'user', 'communications']
        if role not in valid_roles:
            return create_response(f"Invalid role. Must be one of: {', '.join(valid_roles)}", status=400)

        try:
            # Check if user is already an admin
            admin = g.client_db_session.query(Admin).filter_by(user_id=user.id).first()
            if admin:
                admin.role = role
                g.client_db_session.add(admin)
            else:
                # Create new admin record
                admin = Admin()
                admin.user_id = user.id
                admin.role = role
                admin.is_active = True
                g.client_db_session.add(admin)

            g.client_db_session.commit()

            data = user.to_dict(["first_name", "last_name", "email"])
            data['role'] = role

            return create_response("User assigned as admin successfully", data=data)
        except Exception as e:
            g.client_db_session.rollback()
            return create_response(f"Failed to assign admin role: {str(e)}", status=500)

    @check_user_permission("super_admin")
    def delete(self, id):
        """Remove admin role from a user"""
        user = g.client_db_session.query(User).filter_by(id=id, is_deleted=False).first()
        if user is None:
            return create_response("User not found", status=404)

        user_admin = g.client_db_session.query(Admin).filter_by(user_id=user.id).first()
        if not user_admin:
            return create_response("User is not an admin", status=400)

        try:
            g.client_db_session.delete(user_admin)
            g.client_db_session.commit()
            return create_response("Admin role removed successfully")
        except Exception as e:
            g.client_db_session.rollback()
            return create_response(f"Failed to remove admin role: {str(e)}", status=500)



@cms_users_api.doc(security='bearer')
@cms_users_api.route('/characters', methods=['GET'])
class CharacterList(Resource):
    def get(self):
        characters = g.db_session.query(Character).all()
        data = [character.to_dict() for character in characters]
        return create_response("Characters List", data=data)


CharacterModel = cms_users_api.model('CharacterItem', {
    'name': fields.String(required=True, description='Character Name'),
    'key': fields.String(required=True, description='Character Key'),
    'animations': fields.List(fields.String, required=False, description='List of Animations'),
    'index': fields.Integer(required=True, description='Character Index'),
    'user_id': fields.String(required=False, description='User ID')
})

@cms_users_api.doc(security='bearer')
@cms_users_api.route('/character/<string:id>', methods=['GET', 'PUT', 'DELETE'])
@cms_users_api.route('/character', methods=['POST'])
class CharacterObject(Resource):
    def get(self, id):
        character = g.db_session.query(Character).filter_by(id=id).first()
        if character is None:
            return create_response("Character not found", status=404)

        data = character.to_dict()
        return create_response("Character details", data=data)

    @cms_users_api.expect(CharacterModel)
    def put(self, id):
        character = g.db_session.query(Character).filter_by(id=id).first()
        if character is None:
            return create_response("Character not found", status=404)

        data = request.json
        name = data.get('name', '').strip()
        key = data.get('key', '').strip()
        animations = data.get('animations', [])
        index = data.get('index', 0)
        user_id = data.get('user_id', None)

        character.name = name
        character.key = key
        character.animations = json.dumps(animations)
        character.index = index
        character.user_id = user_id

        g.db_session.commit()

        return create_response("Character updated", data=character.to_dict())

    def delete(self, id):
        character = g.db_session.query(Character).filter_by(id=id).first()
        if character is None:
            return create_response("Character not found", status=404)
        
        user_id = character.user_id
        if user_id:
            user = g.db_session.query(User).filter_by(id=user_id).first()
            if user:
                character.user_id = None
                g.db_session.commit()

                ## delete user
                g.db_session.delete(user)
                g.db_session.commit()

        g.db_session.delete(character)
        g.db_session.commit()

        return create_response("Character deleted")
    
    @cms_users_api.expect(CharacterModel)
    def post(self):
        data = request.json
        name = data.get('name', '').strip()
        key = data.get('key', '').strip()
        animations = data.get('animations', [])
        index = data.get('index', 0)
        user_id = data.get('user_id', None)

        character = g.db_session.query(Character).filter_by(key=key).first()
        if character is not None:
            return create_response("Character key already exists", status=404)
        
        character = Character()
        character.name = name
        character.key = key
        character.index = index

        if not user_id:
            email = name.lower().replace(" ", "_") + "@xapa.com"

            ## check if user exists
            user = g.db_session.query(User).filter_by(email=email).first()
            if user:
                user_id = user.id
            else:
                user = User()
                user.preferred_name = name
                user.email = email
                g.db_session.add(user)
                g.db_session.commit()
                user_id = user.id

        character.user_id = user_id
        character.animations = json.dumps(animations)

        g.db_session.add(character)
        g.db_session.commit()

        return create_response("Character created", data=character.to_dict())
    


assignment_model = cms_users_api.model('UserAssignment', {
    'client_ids': fields.List(fields.String, required=True, description='List of Client IDs'),
    'send_invite': fields.Boolean(required=False, description='Send invite email to user', default=False)
})

## user client assignments
@cms_users_api.doc(security='bearer')
@cms_users_api.route('/<string:id>/clients', methods=['GET', 'POST'])
class UserClientAssignment(Resource):
    @check_user_permission("super_admin")
    def get(self, id):
        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)

        data = []
        assginment = g.db_session.query(UserAssignment).filter_by(user_id=id).first()
        if assginment:  
            master_user_id = assginment.master_user_id
            all_assignments = g.db_session.query(UserAssignment).filter_by(master_user_id=master_user_id).all()
            
            for assignment in all_assignments:
                client = g.db_session.query(Client).filter_by(id=assignment.client_id).first()
                if client:
                    data.append(client.id)

        ## check user's email domain and if user is assigned to a client's domain, then assign the client to user
        # email = user.email
        # email_suffix = email.split('@')[-1]
        # clients = g.db_session.query(Client).filter(
        #     func.lower(Client.email_suffix).contains(func.lower(email_suffix))
        # ).all()
        # for client in clients:
        #     if client.id not in data:
        #         data.append(client.id)

        return create_response("User Clients List", data=data)

    @check_user_permission("super_admin")
    @cms_users_api.expect(assignment_model)
    def post(self, id):
        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)

        data = request.json
        client_ids = data.get('client_ids', [])

        ## get master user id
        assginment = g.db_session.query(UserAssignment).filter_by(user_id=id).first()
        if assginment is None:
            ## create a new user assignment and master user account
            master_user = MasterUser(
                username=secrets.token_hex(8),
                first_name=user.first_name,
                last_name=user.last_name
            )
            g.db_session.add(master_user)
            g.db_session.commit()
            master_user_id = master_user.id
        else:
            master_user_id = assginment.master_user_id
        
        master_user_id = master_user_id

        ## get all user assignments
        all_assignments = g.db_session.query(UserAssignment).filter_by(master_user_id=master_user_id).all()
        for assignment in all_assignments:
            if assignment.client_id not in client_ids:
                g.db_session.delete(assignment)

                ## get the client db and remove user record in that db
                client = g.db_session.query(Client).filter_by(id=assignment.client_id).first()
                if client:
                    client_db_session = get_db_session(client.id_key)
                    client_user = client_db_session.query(User).filter_by(id=id).first()
                    if client_user:
                        client_db_session.delete(client_user)
                        client_db_session.commit()

        for client_id in client_ids:
            assignment = g.db_session.query(UserAssignment).filter_by(master_user_id=master_user_id, client_id=client_id).first()
            if assignment is None:
                assignment = UserAssignment()
                assignment.master_user_id = master_user_id
                assignment.user_id = id
                assignment.client_id = client_id

                g.db_session.add(assignment)

        ## check user data, if assignment is created and user is inactive, then update their status to active 
        if client_ids:
            user.is_deleted = False
            user.is_active = True
            user.status = "active"
            send_invite = data.get('send_invite', False)
            if send_invite:
                email = EmailService()
                response = email.send_invitation_email(user)
                print(response)
        else:
            user.is_active = False
            user.status = "unassigned"

            ## if user provided their company info, then update their status to lead
            if user.company:
                user.status = "lead"


        g.db_session.commit()

        return create_response("Client assigned to user successfully")




# Add user currency model
user_currency_model = cms_users_api.model('UserCurrency', {
    'coins': fields.Integer(required=True, description='Coins'),
    'gems': fields.Integer(required=True, description='Gems'),
    'xp': fields.Integer(required=True, description='XP'),
    'keys': fields.Integer(required=True, description='Keys'),
})

## add client currency api
@cms_users_api.doc(security='bearer')
@cms_users_api.route('/<string:id>/currency', methods=['GET', 'POST'])

class UserCurrency(Resource):
    @check_user_permission("super_admin")
    def get(self, id):
        from clientmodels import UserXPFacet
        from sqlalchemy import func

        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)

        user_stats = g.db_session.query(UserStats).filter_by(user_id=id).first()
        if user_stats is None:
            ## return all 0 values
            data = {
                "coins": 0,
                "gems": 0,
                "xp": 0,
                "keys": 0
            }
        else:
            data = {
                "coins": user_stats.coins_count,
                "gems": user_stats.gems_count,
                "xp": user_stats.xp_count,
                "keys": user_stats.keys_count
            }

            user_xp_facets = g.db_session.query(UserXPFacet.facet_id, func.sum(UserXPFacet.value).label("total_xp")).filter_by(user_id=id).group_by(UserXPFacet.facet_id).all()
            xp_by_facet = [{"id": facet.facet_id, "xp": facet.total_xp} for facet in user_xp_facets]
            data["xp_by_facet"] = xp_by_facet

        return create_response("User Currency", data=data)

    @check_user_permission("super_admin")
    @cms_users_api.expect(user_currency_model)
    def post(self, id):
        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)

        data = request.json
        coins = data.get('coins', 0)
        gems = data.get('gems', 0)
        xp = data.get('xp', 0)
        keys = data.get('keys', 0)

        user_stats = g.db_session.query(UserStats).filter_by(user_id=id).first()
        if user_stats is None:
            user_stats = UserStats(user_id=id)
            g.db_session.add(user_stats)

        user_stats.coins_count = coins
        user_stats.gems_count = gems
        user_stats.xp_count = xp
        user_stats.keys_count = keys

        g.db_session.commit()

        return create_response("User Currency updated successfully")
    



## get users Statistics api
@cms_users_api.doc(security='bearer')
@cms_users_api.route('/statistics', methods=['GET'])
class UserStatistics(Resource):
    def get(self):
        from sqlalchemy import not_

        # Exclude users with IDs in the Character table and the super admin ID
        if g.tenant_id != MASTER_TENANT:
            character_users = g.client_db_session.query(Character.user_id).distinct().subquery()
            base_query = g.client_db_session.query(User).filter(
                User.is_deleted == False,
                not_(User.id.in_(character_users.select())),
                User.id != "********-0000-0000-0000-********0000"
            )
        else:
            base_query = g.client_db_session.query(User).filter(
                User.is_deleted == False,
                User.id != "********-0000-0000-0000-********0000"
            )

        total_users = base_query.count()
        total_super_admins = base_query.join(Admin, User.id == Admin.user_id).filter(Admin.role == 'super_admin').count()
        total_admins = base_query.join(Admin, User.id == Admin.user_id).filter(Admin.role == 'admin').count()
        total_writers = base_query.join(Admin, User.id == Admin.user_id).filter(Admin.role == 'writer').count()
        total_communications = base_query.join(Admin, User.id == Admin.user_id).filter(Admin.role == 'communications').count()

        data = {
            "total_users": total_users,
            "total_super_admins": total_super_admins,
            "total_admins": total_admins,
            "total_writers": total_writers,
            "total_communications": total_communications,
            
        }

        # get active users and un-active users
        total_users = g.db_session.query(User.id).join(
            UserAssignment, 
            User.id == UserAssignment.user_id
        ).join(
            Client, 
            UserAssignment.client_id == Client.id
        ).filter(
            User.is_active == True, 
            User.is_deleted == False, 
            (Client.id_key == g.tenant_id) if g.tenant_id != "global" else True
        ).distinct().count()
        
        # Count users with streaks
        active_users = g.db_session.query(User.id).join(
            UserAssignment, 
            User.id == UserAssignment.user_id
        ).join(
            Client, 
            UserAssignment.client_id == Client.id
        ).outerjoin(
            UserStreak,
            User.id == UserStreak.user_id
        ).filter(
            User.is_active == True, 
            User.is_deleted == False, 
            UserStreak.id != None,
            (Client.id_key == g.tenant_id) if g.tenant_id != "global" else True
        ).distinct().count() or 0
        
        # Users without streaks = total - users with streaks
        unactive_users = total_users - active_users

        data.update({
            "active_users": active_users,
            "unactive_users": unactive_users
        })

        # check with unassigned users, join the user assignment table to find users that are not assigned
        subquery = g.client_db_session.query(UserAssignment.user_id).distinct().subquery()
        unassigned_users = g.client_db_session.query(User).filter(~User.id.in_(subquery.select()), User.is_deleted == False, User.status != "lead").count()
        data.update({
            "unassigned_users": unassigned_users
        })

        # get lead users, lead users are users that are unassigned and company is not empty
        lead_users = g.client_db_session.query(User).filter(User.company != "", ~User.id.in_(subquery.select()), User.is_deleted == False).count()
        data.update({
            "lead_users": lead_users
        })

        # get deleted users count
        deleted_users = g.client_db_session.query(User).filter(User.is_deleted == True).count()
        data.update({
            "deleted_users": deleted_users
        })

        return create_response("User Statics", data=data)



# add send invitation api
@cms_users_api.doc(security='bearer')
@cms_users_api.route('/<string:id>/send_invite', methods=['POST'])
class SendInvite(Resource):
    @check_user_permission("super_admin")
    def post(self, id):
        user = g.db_session.query(User).filter_by(id=id).first()
        if user is None:
            return create_response("User not found", status=404)
        
        email = EmailService()
        response = email.send_invitation_email(user)
        print(response)

        return create_response("Invitation sent successfully")




## provide a user download function, it will share the same parameters as the user list api
@cms_users_api.doc(security='bearer')
@cms_users_api.route('/download', methods=['GET'])
class UserDownload(Resource):
    @cms_users_api.expect(users_parser)
    def get(self):
        import csv
        from io import StringIO
        from sqlalchemy import func
        from datetime import datetime
        from flask import make_response
        from flask import send_file
        from io import BytesIO
        from sqlalchemy.orm import joinedload
        from sqlalchemy.orm import contains_eager
        from sqlalchemy.orm import aliased

        args = users_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        sort = args.get('sort', '')
        filter_type = args.get('filter_type', '')
        filter_value = args.get('filter_value', '')

        # Join the Admin, UserStreak, UserStats, and UserAssignment tables, and also get the client name
        query = g.client_db_session.query(
            User,
            Admin,
            UserStats
        ).outerjoin(
            Admin, User.id == Admin.user_id
        ).outerjoin(
            UserStats, User.id == UserStats.user_id
        )

        query = query.filter(User.is_deleted == False)
        if search:
            ## search by first name, last name, email
            query = query.filter(
                func.concat(User.first_name, ' ', User.last_name).ilike(f"%{search}%") | 
                User.email.ilike(f"%{search}%") | 
                User.preferred_name.ilike(f"%{search}%")
            )

        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(User, sort[1:]).desc())
            else:
                query = query.order_by(getattr(User, sort))
        else:
            users = query.order_by(User.first_name)

        if filter_type and filter_value:
            if filter_type == 'role':
                query = query.join(Admin, User.id == Admin.user_id).filter(Admin.role == filter_value)
            elif filter_type == 'status':
                if filter_value == 'assigned':
                    query = query.join(UserAssignment, User.id == UserAssignment.user_id)
                elif filter_value == 'unassigned':
                    subquery = g.db_session.query(UserAssignment.user_id).distinct().subquery()
                    query = query.filter(~User.id.in_(subquery.select()))
                    query = query.filter(User.status != "lead")
                elif filter_value == "active":
                    query = query.filter(User.is_active == True)    
                elif filter_value == "inactive":
                    query = query.filter(User.is_active == False)
                elif filter_value == "lead":
                    ## lead users are users that are not assigned to any client and company is not empty
                    query = query.filter(User.company != None).filter(User.company != "")
                    subquery = g.db_session.query(UserAssignment.user_id).distinct().subquery()
                    query = query.filter(~User.id.in_(subquery.select()))
                elif filter_value == "deleted":
                    query = g.client_db_session.query(User).filter(User.is_deleted
                     == True)   
                else:
                    query = query.filter(User.status == filter_value)
            else:
                query = query.filter(getattr(User, filter_type) == filter_value)

        users = query.all()

        # print(str(query.statement))

        data = []
        for user, admin, user_stats in users:
            item = user.to_dict(["id", "first_name", "last_name", "email", "status", "date_updated"])

            # Get admin info
            if admin:
                item['role'] = admin.role.capitalize() if admin.role else "User"
                if admin.role == "super_admin":
                    item['role'] = "Super Admin"
            else:
                item['role'] = "User"

            # Get user stats
            if user_stats:
                item['xp'] = user_stats.xp_count
                item['coins'] = user_stats.coins_count
                item['gems'] = user_stats.gems_count
                item['keys'] = user_stats.keys_count
            else:
                item['xp'] = 0
                item['coins'] = 0
                item['gems'] = 0
                item['keys'] = 0

            # Get all user streaks
            streaks = g.db_session.query(UserStreak).filter_by(user_id=user.id).order_by(UserStreak.date_created.desc()).all()
            if streaks:
                last_streak = streaks[-1]
                item['last_active'] = format_datetime_with_timezone(last_streak.date_created, timezone="America/Los_Angeles")
            else:
                item['last_active'] = ""

            # Get all client names
            assignments = g.client_db_session.query(UserAssignment).filter_by(user_id=user.id).all()
            client_names = []
            for assignment in assignments:
                client = g.client_db_session.query(Client).filter_by(id=assignment.client_id).first()
                if client:
                    client_names.append(client.name)
            
            if item["role"] == "Super Admin":
                item['clients'] = "All Clients"
            else:
                item['clients'] = ", ".join(client_names)

            # Format date_updated
            if user.date_updated:
                item['date_updated'] = format_datetime_with_timezone(user.date_updated, timezone="America/Los_Angeles")
            else:
                item['date_updated'] = ""

            # Add the processed user data to the list
            data.append(item)

        si = StringIO()
        cw = csv.writer(si)
        headers = ["first_name", "last_name", "preferred_name", "bio", "phone_number", "title", "email", "company", "role", "clients", "coins", "xp", "gems", "keys", "status", "last_login", "last_active", "sso_type", "date_updated"]
        headers_label = ["First Name", "Last Name", "Preferred Name", "Bio", "Phone Number", "Title", "Email", "Company", "Role", "Clients", "Coins", "XP", "Crystals", "Keys", "Status", "Last Auth", "Last Active", "SSO Type", "Last Modified"]
        cw.writerow(headers_label)
        # cw.writerow(headers)
        for item in data:
            row = [item.get(header, "") for header in headers]
            cw.writerow(row)

        output = si.getvalue()
        si.close()

        # Convert StringIO to BytesIO for send_file
        output_bytes = BytesIO(output.encode('utf-8'))

        current_time = datetime.now().strftime("%Y%m%d_%H%M")
        return send_file(output_bytes, mimetype='text/csv', as_attachment=True, download_name=f'users_list_{current_time}.csv')
