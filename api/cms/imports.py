import os

from flask import g, json, request
from flask_restx import Namespace, Resource

from api.common.helper import create_response
from app import storage
from clientmodels import Chest, Quest, UserNode, UserNodeHistory, UserQuest, UserXperience, Xperience, Category, \
    XperienceCategoryAssociation, XperienceQuestAssociation
from clientmodels import Node, NodeTranscript, NodeBranch, NodeOption, NodeOptionMatching
from clientmodels import User, Character
from services.google_api import GoogleDocsService

cms_import_api = Namespace('api_cms_import', description='Import related operations')

NODE_TYPES = [
    "TEXT NODE",
    "TEXT SCREEN",
    "SINGLE-SELECT NODE",
    "SINGLE SELECT MULTIPLE CHOICE NODE",
    "CALCULATE SCORE",
    "BANTER",
    "JOURNAL NODE",
    "MULTI-SELECT NODE",
    "BUCKET NODE",
    "NUGGET NODE",
    "INTERACTIVE NODE",
    "TRADE NODE",
    "CRAFT NODE",
    "GATHER NODE",
    "PlaceItem",
    "INTERACTIVE SCREEN BUNDLE",
    "INTERACTIVE SINGLE-SELECT NODE",
    "MATCHING NODE",
    "MATCHING",
    "INTRO NODE",
    "Logline",
    "SLIDER BAR",
    "SLIDER NODE",
    "MC SINGLE SELECT",
    "MC MULTI SELECT",
    "RANKED ORDER NODE"
]

NODE_TYPE_MAPPING = {
    "TEXT NODE": "introduction",
    "SINGLE-SELECT NODE": "single_answer",
    "SINGLE SELECT MULTIPLE CHOICE NODE": "single_answer",
    "CALCULATE SCORE": "branch",
    "BANTER": "banter",
    "JOURNAL NODE": "journal",
    "MULTI-SELECT NODE": "multi_answer",
    "BUCKET NODE": "matching",
    "NUGGET NODE": "",
    "INTERACTIVE NODE": "",
    "INTERACTIVE SINGLE-SELECT NODE": "single_answer",
    "MATCHING NODE": "matching",
    "INTRO NODE": "intro_transition",
    "SLIDER NODE": "slider",
    "RANKED ORDER NODE": "sorting"
}

SKIP_LINES = [
    "TEXT NODE: Answer"
]


doc_parser = cms_import_api.parser()
doc_parser.add_argument('file', type=str, location='args', required=True)

@cms_import_api.doc(security='bearer')
@cms_import_api.route('/import_doc', methods=['GET'])
class ImportDoc(Resource):
    @cms_import_api.expect(doc_parser)
    def get(self):
        """Import a document from Google Docs"""
        file_id = request.args.get("file", "")
        try:
            google_docs_service = GoogleDocsService()
            ## example file ID: 1M_f992d8SmfR06hR4UB4xwI0B37kSVoerowRhCsTFng
            document = google_docs_service.get_document(file_id)

            if not document:
                return create_response("Document not found", 404)
            
            body = document["body"]

            title = ""
            description = ""
            categories = []
            quests = []
            
            sections = divide_paragraphs_by_heading(body)
            for key, item in sections.items():
                # section = {
                #     "heading": item["heading"],
                #     "type": item["type"],
                #     "length": len(item["paragraphs"])
                # }
                # print(section)

                heading = item["heading"]
                paragraphs = item["paragraphs"]
                heading_type = item["type"]

                if not heading:
                    continue

                if heading_type == "HEADING_1":
                    ## title is the first heading
                    title = heading
                    print(f"Title: {title}")

                    ## find quest description by searching "Short Description" in the paragraphs
                    description = ""
                    for paragraph in paragraphs:
                        if "Short Description" in paragraph:
                            description = paragraph.replace("Short Description:", "").strip()
                            break

                    print(f"Description: {description}")

                    ## find categories by searching "Topical Metadata" in the paragraphs
                    categories = []
                    for paragraph in paragraphs:
                        if "Topical Metadata" in paragraph:
                            categories = paragraph.replace("Topical Metadata:", "").strip().split(";")
                            categories = [category.strip() for category in categories]
                            break

                    print(f"Categories: {categories}")

                
                if heading_type == "HEADING_2":
                    ## find the sections by node types
                    section = divide_sections_by_node_type(item)
                    nodes = section["nodes"]
                    formatted_nodes = []

                    previous_node_id = "0"
                    for node in nodes:
                        result = handle_node(node, previous_node_id)

                        if isinstance(result, list):
                            for sub_node in result:
                                sub_result = handle_node(sub_node, previous_node_id)
                                if sub_result and isinstance(sub_result, dict):
                                    previous_node_id = sub_result.get("id", previous_node_id)

                                    ## calculate how many transcripts are there in the banter node
                                    transcripts = sub_result.get("transcripts", [])
                                    num_transcripts = len(transcripts)
                                    if num_transcripts > 1:
                                        previous_node_id = str(int(previous_node_id) + num_transcripts)

                                    formatted_nodes.append(sub_result)
                                    # print(f"Quest: {heading} Node {sub_result.get('type')}: {sub_result.get('name')} added")

                        elif isinstance(result, dict):
                            previous_node_id = result.get("id", previous_node_id)

                            ## if the node type is BANTER, then calculate the next node id
                            if result.get("type") == "BANTER":
                                ## calculate how many transcripts are there in the banter node
                                transcripts = result.get("transcripts", [])
                                num_transcripts = len(transcripts)
                                if num_transcripts > 1:
                                    previous_node_id = str(int(previous_node_id) + num_transcripts - 1)

                            formatted_nodes.append(result)
                            # print(f"Quest: {heading} Node {result.get('type')}: {result.get('name')} added")

                    quests.append({
                        "title": heading,
                        "nodes": formatted_nodes
                    })

            data = {
                "title": title,
                "description": description,
                "categories": categories,
                "quests": quests
            }

            # return create_response("import document", data=data)

            ## save the data to storage
            
            ## save the file with title as filename
            filename = title + ".json"
            folder = "quests"

            file_path = storage.upload_file(folder, filename, json.dumps(data), overwrite=True)

            return create_response("get document", file=filename)

        except Exception as e:
            print(e)
            return create_response(f"An error occurred: {e}", 500)
        

import_parser = cms_import_api.parser()
import_parser.add_argument('file', type=str, location='args', required=True)

@cms_import_api.doc(security='bearer')
@cms_import_api.route('/get_quest', methods=['GET'])
class GetQuest(Resource):
    @cms_import_api.expect(import_parser)
    def get(self):
        """Get a quest from storage"""
        try:
            
            folder = "quests"
            filename = request.args.get("file", "")

            print(f"Get Document: {filename}")
            
            file_content = storage.download_file(folder, filename)
            if not file_content:
                return create_response("Document not found", 404)
            
            # Read the content into a bytes object
            file_bytes = file_content.read()

            # Convert bytes to a string and parse as JSON
            data = json.loads(file_bytes.decode('utf-8'))

            ## save the xperience to database first
            xperience_name = data.get("title", "")
            if not xperience_name:
                return create_response("Xperience name not found", 404)
            
            # Check if an experience with the same name exists
            xperience = g.db_session.query(Xperience).filter(Xperience.name == xperience_name).first()
            if xperience:
                print(f"Xperience {xperience_name} already exists")
                # return create_response("Xperience already exists", data={"xperience": xperience.to_dict()})
            else:
                # Create a new experience with name and description
                xperience = Xperience(name=xperience_name, description=data.get("description", ""))
                g.db_session.add(xperience)
                g.db_session.commit()
                print(f"Xperience {xperience_name} added")

            # Check if categories exist and create associations
            categories = data.get("categories", [])
            for category_name in categories:
                category = g.db_session.query(Category).filter(Category.name == category_name).first()
                if category:
                    print(f"Category {category_name} exists")
                    association = XperienceCategoryAssociation(xperience_id=xperience.id, category_id=category.id)
                    g.db_session.add(association)

            g.db_session.commit()

            ## clear user xperience records
            g.db_session.query(UserQuest).filter(UserQuest.xperience_id == xperience.id).delete()
            g.db_session.query(UserXperience).filter(UserXperience.xperience_id == xperience.id).delete()

            ## update or create xperience chest, add 25 xp, 10 coins, 5 gems and 1 key into the chest
            xperience_chest_id = xperience.chest_id
            if not xperience_chest_id:
                print("Creating new chest")
                xperience_chest = Chest(name=f"{xperience_name} Chest")
                g.db_session.add(xperience_chest)
                g.db_session.commit()
            else:
                print("Updating existing chest")
                xperience_chest = g.db_session.query(Chest).filter(Chest.id == xperience_chest_id).first()
            
            xperience_chest.xp = 25
            xperience_chest.coins = 100
            xperience_chest.gems = 10
            xperience_chest.keys = 3
        
            xperience.chest_id = xperience_chest.id
            g.db_session.add(xperience_chest)
            g.db_session.add(xperience)

            g.db_session.commit()

            ## save the quests to database first
            quests = data.get("quests", [])
            for quest in quests:
                quest_title = quest.get("title", "")
                if not quest_title:
                    continue

                new_quest = g.db_session.query(Quest).filter(Quest.name == quest_title).first()
                if new_quest:
                    print(f"Quest {quest_title} already exists")
                    # continue

                    ## for now, delete all the nodes to create new ones
                    nodes = g.db_session.query(Node).filter(Node.quest_id == new_quest.id).all()
                    for node in nodes:
                        ## delete the transcripts
                        transcripts = g.db_session.query(NodeTranscript).filter(NodeTranscript.node_id == node.id).all()
                        for transcript in transcripts:
                            g.db_session.delete(transcript)
                        ## delete the branches
                        branches = g.db_session.query(NodeBranch).filter(NodeBranch.node_id == node.id).all()
                        for branch in branches:
                            g.db_session.delete(branch)
                        ## delete the options
                        options_matching = g.db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_id == node.id).all()
                        for option_matching in options_matching:
                            g.db_session.delete(option_matching)
                        ## delete the options
                        options = g.db_session.query(NodeOption).filter(NodeOption.node_id == node.id).all()
                        for option in options:
                            g.db_session.delete(option)
                        ## delete user node records
                        g.db_session.query(UserNodeHistory).filter(UserNodeHistory.node_id == node.id).delete()
                        g.db_session.query(UserNode).filter(UserNode.node_id == node.id).delete()
     
                        g.db_session.query(UserNodeHistory).filter(UserNodeHistory.next_node_id == node.id).update({"next_node_id": None})
                        g.db_session.query(UserNode).filter(UserNode.next_node_id == node.id).update({"next_node_id": None})
                        g.db_session.query(UserNodeHistory).filter(UserNodeHistory.previous_node_id == node.id).update({"previous_node_id": None})
                        g.db_session.query(UserNode).filter(UserNode.previous_node_id == node.id).update({"previous_node_id": None})

                        ## update all the next node ids
                        g.db_session.query(Node).filter(Node.next_node_id == node.id).update({"next_node_id": None})
                        g.db_session.query(NodeBranch).filter(NodeBranch.next_node_id == node.id).update({"next_node_id": None})
                        g.db_session.query(NodeOption).filter(NodeOption.next_node_id == node.id).update({"next_node_id": None})

                        ## delete the node
                        g.db_session.delete(node)

                else:
                    new_quest = Quest(name=quest_title)
                    g.db_session.add(new_quest)
                    print(f"Quest {quest_title} added")
                    g.db_session.commit()

                ## update or create the quest chest, add 10 xp into the chest
                chest_id = new_quest.chest_id
                if not chest_id:
                    chest = Chest(name=f"{quest_title} Chest")
                    g.db_session.add(chest)
                    g.db_session.commit()
                else:
                    chest = g.db_session.query(Chest).filter(Chest.id == chest_id).first()
                
                chest.xp = 10
                chest.coins = 25
                chest.gems = 2
                chest.keys = 0
                new_quest.chest_id = chest.id
                g.db_session.add(chest)

                new_quest.status = "Ready"
                g.db_session.add(new_quest)
                
                g.db_session.commit()
                
                ## check if the association exists, if not, then create it
                association = g.db_session.query(XperienceQuestAssociation).filter(
                    XperienceQuestAssociation.xperience_id == xperience.id,
                    XperienceQuestAssociation.quest_id == new_quest.id
                ).first()
                
                if not association:
                    association = XperienceQuestAssociation(xperience_id=xperience.id, quest_id=new_quest.id)
                    g.db_session.add(association)
                    g.db_session.commit()

                # Load the animation key mapping file
                animation_mapping = {}
                animation_mapping_file = os.path.join('static/files/animation_mapping.json')
                with open(animation_mapping_file, 'r') as f:
                    animation_mapping = json.load(f)

                ## create a mapping between node original id and new node id
                node_mapping = {}

                nodes = quest.get("nodes", [])
                previous_node_id = ""
                for node in nodes:
                    ## find the screen type from node type mapping
                    node_type = node.get("type", "")
                    screen_type = NODE_TYPE_MAPPING.get(node_type, "")
                    if not screen_type:
                        continue

                    node_name = node.get("name", "")
                    node_title = node.get("title", "")
                    node_sub_title = node.get("subtitle", "")
                    quest_character_id = node.get("quest_character_id", "")
                    quest_character_animation = node.get("quest_character_animation", "")
                    quest_character_audio = node.get("quest_character_audio", "")
                    quest_text = node.get("quest_text", "")
                    answer_placeholder = node.get("answer_placeholder", "")
                    points = node.get("points", 0)
                    is_quiz = node.get("is_quiz", False)
                    options = node.get("options", [])
                    transcripts = node.get("transcripts", [])
                    branches = node.get("branches", [])

                    if is_quiz:
                        quest_type = "quiz"
                    else:
                        quest_type = ""
                
                    ## mapping the quest_character_animation from the animation mapping file, if no key is found, use "Idle"
                    if quest_character_animation:
                        quest_character_animation = animation_mapping.get(quest_character_animation, "Idle")

                    new_node = Node(
                        quest_id=new_quest.id,
                        screen_type=screen_type,
                        name=node_name,
                        title=node_title,
                        sub_title=node_sub_title,
                        quest_character_id=quest_character_id,
                        quest_character_animation=quest_character_animation,
                        quest_character_audio=quest_character_audio,
                        quest_text=quest_text,
                        answer_placeholder=answer_placeholder,
                        points=points,
                        quest_type=quest_type
                    )

                    g.db_session.add(new_node)
                    g.db_session.commit()

                    if previous_node_id:
                        g.db_session.query(Node).filter(Node.id == previous_node_id).update({"next_node_id": new_node.id})

                    previous_node_id = new_node.id

                    ## save the transcripts
                    for transcript in transcripts:
                        character_id = transcript.get("character_id", "")
                        animation = transcript.get("animation", "")
                        show_icon = transcript.get("show_icon", False)
                        text = transcript.get("text", "")
                        audio_en = transcript.get("audio_en", "")
                        duration = transcript.get("duration", 0)

                        ## mapping the animation from the animation mapping file, if no key is found, use "Idle"
                        if animation:
                            animation = animation_mapping.get(animation, "Idle")

                        new_transcript = NodeTranscript(
                            node_id=new_node.id,
                            character_id=character_id,
                            animation=animation,
                            show_icon=show_icon,
                            text=text,
                            audio_en=audio_en,
                            duration=duration
                        )
                        g.db_session.add(new_transcript)

                    ## save the options
                    for option in options:
                        label = option.get("text", "")
                        points = option.get("points", 0)
                        is_correct = option.get("is_correct", False)
                        correct_feedback = option.get("correct_feedback", "")
                        incorrect_feedback = option.get("incorrect_feedback", "")
                        feedback = {
                            "correct": correct_feedback,
                            "incorrect": incorrect_feedback
                        }
                        position = option.get("position", "")
                        value = option.get("value", "")
                        index = option.get("index", 0)

                        new_option = NodeOption(
                            node_id=new_node.id,
                            label=label,
                            points=points,
                            is_correct=is_correct,
                            feedback=json.dumps(feedback),
                            position=position,
                            value=value,
                            index=index
                        )
                        g.db_session.add(new_option)

                    ## save the node id mapping
                    original_node_id = node.get("id", "")
                    if original_node_id:
                        node_mapping[original_node_id] = new_node.id

                    # print(f"Node {screen_type}: {node_name} added")

                g.db_session.commit()

                ## save the next node ids in node, single-select node and branch nodes
                ## save the matching options in matching node
                for node in nodes:
                    node_id = node.get("id", "")
                    next_node_id = node.get("next_node_id", "")

                    if node_id and next_node_id:
                        new_node_id = node_mapping.get(str(next_node_id), "")
                        if new_node_id:
                            g.db_session.query(Node).filter(Node.id == node_mapping[node_id]).update({"next_node_id": new_node_id})

                    ## save the next node ids in single-select node
                    if node.get("type") == "SINGLE-SELECT NODE" or node.get("type") == "SINGLE SELECT MULTIPLE CHOICE NODE":
                        options = node.get("options", [])
                        for option in options:
                            next_node_id = option.get("next_node_id", "")
                            if next_node_id:
                                label = option.get("text", "")
                                new_node_id = node_mapping.get(str(next_node_id), "")
                                if new_node_id:
                                    g.db_session.query(NodeOption).filter(NodeOption.node_id == node_mapping[node_id], NodeOption.label == label).update({"next_node_id": new_node_id})

                    ## save the matching options in matching node
                    if node.get("type") == "MATCHING NODE":
                        ## if current node id is not in the node_mapping, then skip
                        if node_id not in node_mapping:
                            continue
                        
                        options = node.get("options", [])
                        for option in options:
                            if "matching_label" in option:
                                matching_label = option.get("matching_label", "")
                                node_option = g.db_session.query(NodeOption).filter(NodeOption.node_id == node_mapping[node_id], NodeOption.label == option.get('text', '')).first()
                                if node_option:
                                    match_option = g.db_session.query(NodeOption).filter(NodeOption.node_id == node_mapping[node_id], NodeOption.label == matching_label).first()
                                    if match_option:
                                        ## check if the matching option is already added
                                        option_matching = g.db_session.query(NodeOptionMatching).filter(NodeOptionMatching.node_option_id == node_option.id, NodeOptionMatching.matching_node_option_id == match_option.id).first()
                                        if not option_matching:
                                            new_option_matching = NodeOptionMatching(
                                                node_id=node_mapping[node_id],
                                                node_option_id=node_option.id,
                                                matching_node_option_id=match_option.id
                                            )
                                            g.db_session.add(new_option_matching)
                                            g.db_session.commit()

                    ## save the branches_from id and next node id in calculate score node
                    if node.get("type") == "CALCULATE SCORE":
                        branches = node.get("branches", [])
                        for branch in branches:
                            next_node_id = branch.get("next_node_id", "")
                            if next_node_id:
                                new_node_id = node_mapping.get(str(next_node_id), "")
                                if new_node_id:
                                    # g.db_session.query(NodeBranch).filter(NodeBranch.node_id == node_mapping[node_id]).update({"next_node_id": new_node_id})
                                    min_points = branch.get("min_points", 0)
                                    max_points = branch.get("max_points", 0)

                                    condition = branch.get("condition", "between")
                                    condition_value = branch.get("value", "")
                                    
                                    if condition == "between":
                                        condition_value = f"{min_points},{max_points}"
                                    elif condition == "most":
                                        condition_value = condition_value
                                    else:
                                        condition_value = condition_value

                                    new_branch = NodeBranch(
                                        node_id=node_mapping[node_id],
                                        condition=condition,
                                        condition_value=condition_value,
                                        next_node_id=new_node_id
                                    )
                                    g.db_session.add(new_branch)

                        branches_from = node.get("branches_from", [])
                        new_branches_from = []
                        for branch_from in branches_from:
                            new_node_id = node_mapping.get(str(branch_from), "")
                            if new_node_id:
                                new_branches_from.append(new_node_id)

                        g.db_session.query(Node).filter(Node.id == node_mapping[node_id]).update({"branches_from_ids": json.dumps(new_branches_from)})
                
                g.db_session.commit()
                    
            return create_response("get quest")

        except Exception as e:
            print(e)
            return create_response(f"An error occurred: {e}", 500)



@cms_import_api.doc(security='bearer')
@cms_import_api.route('/import_characters', methods=['GET'])
class ImportCharacters(Resource):
    def get(self):
        '''Get all characters'''
        query = g.db_session.query(NodeTranscript.character_id).distinct()
        character_ids = query.all()
        
        data = []
        for character_id in character_ids:
            if character_id[0]:
                animations_query = g.db_session.query(NodeTranscript.animation).filter(NodeTranscript.character_id == character_id[0]).distinct()
                animations = [animation[0] for animation in animations_query.all() if animation[0]]
                if animations:
                    item = {
                        'character_id': character_id[0],
                        'animation_keys': animations,
                        'default_animation_key': 'Idle'
                    }

                    ## add character to Character table by unique key 
                    character_id = character_id[0]
                    character = g.db_session.query(Character).filter(Character.key == character_id).first()
                    if not character:
                        character = Character(name=character_id, key=character_id, animations=json.dumps(animations))
                        g.db_session.add(character)
                        g.db_session.commit()
                    else:
                        character.animations = json.dumps(animations)
                        g.db_session.add(character)
                        g.db_session.commit()

                    ## add character to user table and update user_id in character table
                    user = g.db_session.query(User).filter(User.preferred_name == character_id).first()
                    if not user:
                        user = User(preferred_name=character_id, email=f"{character_id.lower().replace(" ", "_")}@xapa.com", first_name="", last_name="")
                        g.db_session.add(user)
                        g.db_session.commit()
                        user_id = user.id
                    else:
                        user.email = f"{character_id.lower().replace(" ", "_")}@xapa.com"
                        user_id = user.id

                    character.user_id = user_id
                    g.db_session.add(character)

                    data.append(item)

        g.db_session.commit()
        
        return create_response("Characters List", data=data)




def divide_paragraphs_by_heading(body):
    sections = {}
    current_heading = None
    lists = {}

    for element in body["content"]:
        # Check only the paragraphs
        if "paragraph" in element:
            paragraph = element["paragraph"]
            # Check for HEADING_2 elements
            if paragraph["paragraphStyle"]['namedStyleType'] == "HEADING_2" or paragraph["paragraphStyle"]['namedStyleType'] == "HEADING_1":
                current_heading = element["paragraph"]["elements"][0]["textRun"]["content"].strip()
                sections[current_heading] = {}
                sections[current_heading]["paragraphs"] = []
                sections[current_heading]["heading"] = current_heading
                sections[current_heading]["type"] = paragraph["paragraphStyle"]['namedStyleType']
            elif current_heading:
                # Add paragraphs to the current heading section
                text = ''.join([el["textRun"]["content"] for el in paragraph["elements"] if "textRun" in el])
                
                if "bullet" in paragraph:
                    list_id = paragraph["bullet"]["listId"]
                    list_level = paragraph["bullet"].get("nestingLevel", 0)
                    if list_level == 0:
                        list_number = lists.get(list_id, 0) + 1
                        lists[list_id] = list_number
                        if any(node_type in text for node_type in NODE_TYPES):
                            text = f"{list_number}. {text}"
                        else:
                            text = f"• {text}"
                    else:
                        list_number = ""
                
                sections[current_heading]["paragraphs"].append(text.strip())

    return sections



## devide the sections by node types
def divide_sections_by_node_type(section):
    nodes = []
    current_node = None

    paragraphs = section["paragraphs"]

    node_index = 0
    for paragraph in paragraphs:
        node_type = next((node_type for node_type in NODE_TYPES if node_type in paragraph), None)
        if node_type and not any(skip_line in paragraph for skip_line in SKIP_LINES):
            if current_node:
                nodes.append(current_node)
            current_node = {"type": node_type, "content": [paragraph]}
            node_index = node_index + 1
            # print(f"Node {node_index}: {node_type}")
        else:
            if current_node:
                current_node["content"].append(paragraph)

    if current_node:
        nodes.append(current_node)

    return {"nodes": nodes}




def handle_node(node, previous_node_id=None):
    node_type = node.get("type")
    content = node.get("content", [])

    try:
        node_format = {
            "id": "",
            "type": node_type,
            "name": "",
            "title": "",
            "subtitle": "",
            "quest_character_id": "",
            "quest_character_animation": "",
            "quest_character_audio": "",
            "quest_text": "",
            "answer_placeholder": "",
            "points": 0,
            "options": [],
            "transcripts": [],
            "branches": [],
            "branches_from": [],
            "next_node_id": "",
        }

        if node_type == "MC SINGLE SELECT":
            node_type = "SINGLE-SELECT NODE"
            node_format['type'] = node_type

        if node_type == "MC MULTI SELECT":
            node_type = "MULTI-SELECT NODE"
            node_format['type'] = node_type

        ## Extract node id
        first_line = content[0] if content else ""
        node_format["name"] = first_line.replace("• ", "").replace("[no header]", "").strip()
        if any(node_type in first_line for node_type in NODE_TYPES):
            parts = first_line.split(".", 1)
            if len(parts) == 2 and parts[0].isdigit():
                node_format["id"] = parts[0].strip()

        ## if there is no node id, then check if previous_node_id is provided, if so, add 1 to the node_id
        if not node_format["id"] and previous_node_id:
            node_format["id"] = str(int(previous_node_id) + 1)

        # Remove "Branch to" line and extract next_node_id
        if node_type != "SINGLE-SELECT NODE" and node_type != "SINGLE SELECT MULTIPLE CHOICE NODE" and node_type != "CALCULATE SCORE":
            for line in content:
                if "Branch to" in line:
                    if "Branch to Node " in line:
                        next_node_id = line.split("Branch to Node ")[-1].strip()
                    elif "Branch to: Node " in line:
                        next_node_id = line.split("Branch to: Node ")[-1].strip()
                    node_format["next_node_id"] = ''.join(filter(str.isdigit, next_node_id))
                    content.remove(line)
                    break


        ## Extract node information based on node type
        if node_type == "TEXT NODE" or node_type == "TEXT SCREEN":
            node_title = first_line.split(":", 1)[-1].strip() if ":" in first_line else ""

            node_text = "\n".join(line.replace("[no header] ", "").replace("• ", "").strip() for line in content[1:] if "CTA" not in line and "/BUNDLE" not in line)
            node_text = node_text.replace("/", "\n")

            node_format["type"] = "TEXT NODE"

            if "alone on scree" in node_text:
                new_node_type = "INTRO NODE"
                nodes = []
                current_node = []
                for line in content:
                    if "alone on scree" in line:
                        if current_node:
                            nodes.append(current_node)
                        current_node = [new_node_type, line]
                    else:
                        if line:
                            current_node.append(line)

                if current_node:
                    nodes.append(current_node)
                
                new_nodes = []
                for node in nodes:
                    new_node = {
                        "type": new_node_type,
                        "content": node
                    }
                    new_nodes.append(new_node)

                return new_nodes

            node_title = node_title.replace("[no header]", "").strip()
            node_format["title"] = node_title
            node_format["subtitle"] = node_title

            if node_text:
                transcript = {
                    "character_id": "",
                    "animation": "",
                    "show_icon": False,
                    "text": node_text,
                    "audio_en": "",
                    "duration": 0
                }
                node_format["transcripts"].append(transcript)

            return node_format

        elif node_type == "INTRO NODE" or node_type == "Logline":
            node_title = first_line.split(":", 1)[-1].strip() if ":" in first_line else ""

            node_text = "\n".join(line.replace("[no header] ", "").replace("• ", "").strip() for line in content[1:] if "CTA" not in line and "/BUNDLE" not in line)
            node_text = node_text.replace("/", "\n")

            node_title = node_title.replace("[no header]", "").strip()
            node_format["title"] = node_title
            node_format["subtitle"] = node_title

            character_id = ""
            animation = ""

            if "alone on screen" in node_text or "):" in node_text:
                for line in content:
                    line = line.replace("• ", "").strip()

                    if line.strip() == "":
                        continue

                    parts = line.split(": ", 1)
                    if len(parts) == 2:
                        character_info, text = parts
                        if "(" in character_info and ")" in character_info:
                            character_id, animation = character_info.split(" (")
                            animation = animation.rstrip(")")
                        else:
                            character_id = character_info
                            animation = ""

                    node_text = line.split(": ", 1)[-1].strip()
            else:
                node_format["type"] = "TEXT NODE"

            if node_text:
                transcript = {
                    "character_id": character_id,
                    "animation": animation,
                    "show_icon": False,
                    "text": node_text,
                    "audio_en": "",
                    "duration": 0
                }
                node_format["transcripts"].append(transcript)

            return node_format


        elif node_type == "SINGLE-SELECT NODE" or node_type == "SINGLE SELECT MULTIPLE CHOICE NODE" or node_type == "INTERACTIVE SINGLE-SELECT NODE":
            node_format["quest_text"] = content[1] if len(content) > 1 else ""
            options = []
            option = {}
            line_count = 2

            for line in content[2:]:
                line = line.replace("• ", "").strip()
                next_line = content[line_count + 1] if line_count + 1 < len(content) else ""

                line_break_marks = [
                    "Branch to: Node",
                    "Branch to Node ",
                    "Point Value:",
                    "Note:"
                ]
                
                if any(mark in line for mark in line_break_marks):
                    next_line_is_break = any(mark in next_line for mark in line_break_marks)
                    if "Branch to: Node" in line or "Branch to Node " in line:
                        next_node_id = line.split("Branch to: Node ")[-1].strip()
                        option["next_node_id"] = ''.join(filter(str.isdigit, next_node_id))

                    if "Point Value:" in line:
                        option["points"] = int(line.replace("Point Value: ", "").strip())

                    if not next_line_is_break:
                        options.append(option)
                        option = {}

                else:
                    if "Correct" in line and "Feedback" not in line and "feedback" not in line:
                        option["is_correct"] = True
                        node_format["is_quiz"] = True
                    elif "Incorrect" in line and "Feedback" not in line and "feedback" not in line:
                        option["is_correct"] = False
                        node_format["is_quiz"] = True
                    elif "Neutral" in line and line == "Neutral":
                        option["is_correct"] = None
                    elif "Correct Feedback:" in line or "Correct feedback:" in line:
                        option["correct_feedback"] = line.replace("Correct Feedback:", "").strip()
                        option["correct_feedback"] = option["correct_feedback"].replace("Correct feedback:", "").strip()
                    elif "Incorrect Feedback:" in line or "Incorrect feedback:" in line:
                        option["incorrect_feedback"] = line.replace("Incorrect Feedback:", "").strip()
                        option["incorrect_feedback"] = option["incorrect_feedback"].replace("Incorrect feedback:", "").strip()
                    elif "No Feedback" in line:
                        option["correct_feedback"] = ""
                        option["incorrect_feedback"] = ""
                    elif "Show All Feedback" in line:
                        option["show_all_feedback"] = True
                    elif "Show Incorrect Feedback" in line:
                        option["show_incorrect_feedback"] = True
                    elif "Show Correct Feedback" in line:
                        option["show_correct_feedback"] = True
                    else:
                        option["text"] = line.strip()

                        if "(" in line and ")" in line:
                            option["value"] = line[line.find("(")+1:line.find(")")]
                        else:
                            option["text"] = line.strip()

                line_count = line_count + 1
                    

            node_format["options"] = options

            ## if there is no option, then return None
            if not options:
                return None
            
            return node_format

        elif node_type == "CALCULATE SCORE":
            # print(content)

            branches_from = content[0].split("CALCULATE SCORE: Nodes ")[-1].strip()

            if "-" in branches_from:
                start, end = map(int, branches_from.split("-"))
            elif " through " in branches_from:
                start, end = map(int, branches_from.split(" through "))
            else:
                return None
            
            node_format["branches_from"] = list(range(start, end + 1))

            branches = []
            for branch in content:
                if " Points = Branch to: Node " in branch:
                    parts = branch.split(" Points = Branch to: Node ")
                    if len(parts) == 2:
                        points_range, node_id = parts
                        min_points, max_points = map(int, points_range.split("-"))
                        branches.append({
                            "min_points": min_points,
                            "max_points": max_points,
                            "condition": "between",
                            "next_node_id": ''.join(filter(str.isdigit, node_id.strip()))
                        })
                elif "Mostly " in branch:
                    parts = branch.split("s = Branch to Node ")
                    if len(parts) == 2:
                        value, node_id = parts
                        value = value.split("Mostly ")[-1].strip()
                        node_id = node_id.split(" = Branch to Node ")[-1].strip()
                        branches.append({
                            "value": value,
                            "condition": "most",
                            "next_node_id": ''.join(filter(str.isdigit, node_id))
                        })

            node_format["branches"] = branches
            return node_format

        elif node_type == "BANTER":
            if node_format["name"] == "END BANTER":
                return None
            
            transcripts = []
            for line in content[1:]:
                line = line.replace("• ", "").strip()

                if line.strip() == "":
                    continue
                parts = line.split(": ", 1)
                if len(parts) == 2:
                    character_info, text = parts
                    if "(" in character_info and ")" in character_info:
                        character_id, animation = character_info.split(" (")
                        animation = animation.rstrip(")")
                    else:
                        character_id = character_info
                        animation = ""
                    transcript = {
                        "character_id": character_id.strip(),
                        "animation": animation.strip(),
                        "show_icon": True,
                        "text": text.strip(),
                        "audio_en": "",
                        "duration": 0
                    }
                    transcripts.append(transcript)

            node_format["transcripts"] = transcripts
            return node_format


        elif node_type == "JOURNAL NODE":
            if len(content) > 1:
                node_format["quest_text"] = content[1].replace("Journal prompt:", "").strip()
            return node_format

        elif node_type == "MULTI-SELECT NODE":
            node_format["quest_text"] = content[1] if len(content) > 1 else ""
            options = []
            option = {}
            for line in content[2:]:
                line = line.replace("• ", "").strip()

                if "Point Value:" in line:
                    option_points = int(line.replace("Point Value: ", "").strip())
                    option["points"] = option_points
                    options.append(option)
                    option = {}
                else:
                    if "Correct" in line and "Feedback" not in line and "feedback" not in line:
                        option["is_correct"] = True
                        node_format["is_quiz"] = True
                    elif "Incorrect" in line and "Feedback" not in line and "feedback" not in line:
                        option["is_correct"] = False
                        node_format["is_quiz"] = True
                    elif "Neutral" in line and line == "Neutral":
                        option["is_correct"] = None
                    elif "Correct Feedback:" in line or "Correct feedback:" in line:
                        option["correct_feedback"] = line.replace("Correct Feedback:", "").strip()
                        option["correct_feedback"] = option["correct_feedback"].replace("Correct feedback:", "").strip()
                    elif "Incorrect Feedback:" in line or "Incorrect feedback:" in line:
                        option["incorrect_feedback"] = line.replace("Incorrect Feedback:", "").strip()
                        option["incorrect_feedback"] = option["incorrect_feedback"].replace("Incorrect feedback:", "").strip()
                    elif "No Feedback" in line: 
                        option["correct_feedback"] = ""
                        option["incorrect_feedback"] = ""
                    elif "Show All Feedback" in line:
                        option["show_all_feedback"] = True
                    elif "Show Incorrect Feedback" in line:
                        option["show_incorrect_feedback"] = True
                    elif "Show Correct Feedback" in line:
                        option["show_correct_feedback"] = True
                    else:
                        option["text"] = line.strip()

            node_format["options"] = options
            return node_format
        
        elif node_type == "BUCKET NODE" or node_type == "MATCHING NODE" or node_type == "MATCHING":
            node_format['type'] = "MATCHING NODE"
            node_format["quest_text"] = content[1] if len(content) > 1 else ""
            left = ""
            right = ""
            options = []
            option = {}
            
            question_lines = []
            answer_lines = []
            extra_lines = []
            original_lines = []
            is_answer = False
            is_extra = False

            for line in content[2:]:
                if not is_extra:
                    original_lines.append(line.strip())

                if "TEXT NODE: Answer" in line or "ANSWER" in line:
                    is_answer = True
                    continue

                if is_answer:
                    if line == "":
                        is_extra = True
                    
                    if not is_extra:
                        answer_lines.append(line.strip())
                    else:
                        if line:
                            extra_lines.append(line.strip())
                else:
                    question_lines.append(line.strip())

            original_lines = content[0:2] + original_lines
            ## remove the last empty line
            if original_lines[-1] == "":
                original_lines.pop()

            nodes = []
            matching_node = {
                "type": node_type,
                "content": original_lines
            }

            nodes.append(matching_node)

            if extra_lines:
                extra_node = {
                    "type": "INTRO NODE",
                    "content": extra_lines
                }
                nodes.append(extra_node)
                return nodes
            
            index = 2
            for line in question_lines:
                line = line.strip()
                if line == "":
                    if not left:
                        left = content[index + 1].strip()
                    elif not right:
                        right = content[index + 1].strip()
                    else:
                        continue
                else:
                    line = line.replace("•", "").strip()
                    option = {
                        "text": line,
                        "points": 0,
                    }
                    if right:
                        option["position"] = "right"
                        option["position_label"] = right
                    elif left:
                        option["position"] = "left"
                        option["position_label"] = left

                    options.append(option)
                index = index + 1

            for line in answer_lines:
                if ":" in line or " = " in line:
                    if " = " in line:
                        left_label, right_label = line.split(" = ", 1)
                    else:
                        left_label, right_label = line.split(":", 1)

                    left_label = left_label.strip()
                    right_label = right_label.strip()

                    if not any(option.get("text") == left_label for option in options):
                        break
                    if not any(option.get("text") == right_label for option in options):
                        break

                    for option in options:
                        if option.get("position") == "left" and option.get("text") == left_label:
                            option["matching_label"] = right_label
                        elif option.get("position") == "right" and option.get("text") == right_label:
                            option["matching_label"] = left_label

                
            node_format["options"] = options

            return node_format


        elif node_type == "NUGGET NODE":
            node_title = content[0].replace("NUGGET NODE:", "").strip() if len(content) > 0 else ""
            node_text = content[1] if len(content) > 1 else ""

            node_format["title"] = node_title
            transcript = {
                "character_id": "",
                "animation": "",
                "show_icon": False,
                "text": node_text,
                "audio_en": "",
                "duration": 0
            }
            node_format["transcripts"].append(transcript)

            return node_format
        
        elif node_type == "INTERACTIVE SCREEN BUNDLE":
            new_node_type = "INTERACTIVE SINGLE-SELECT NODE"
            nodes = []

            current_node = []
            for line in content:
                if line.startswith("• ") and (content.index(line) + 1 < len(content) and content[content.index(line) + 1].startswith("• ")):
                    if current_node:
                        nodes.append(current_node)
                    current_node = [new_node_type, line.replace("• ", "")]
                else:
                    if line:
                        current_node.append(line)

            if current_node:
                nodes.append(current_node)
            
            new_nodes = []
            for node in nodes:
                new_node = {
                    "type": new_node_type,
                    "content": node
                }
                new_nodes.append(new_node)

            return new_nodes

        elif node_type == "SLIDER NODE" or node_type == "SLIDER BAR":
            node_format["type"] = "SLIDER NODE"
            node_format["quest_text"] = content[1].strip() if len(content) > 1 else ""
            left_label = ""
            right_label = ""
            if len(content) > 2:
                labels = content[2].split("/")
                if len(labels) == 2:
                    left_label = labels[0].strip()
                    right_label = labels[1].strip()

            if left_label and right_label:
                left_option = {
                    "position": "left",
                    "text": left_label,
                    "value": left_label
                }
                right_option = {
                    "position": "right",
                    "text": right_label,
                    "value": right_label
                }
                node_format["options"].append(left_option)
                node_format["options"].append(right_option)
            return node_format
        
        elif node_type == "RANKED ORDER NODE" or node_type == "RANKED CHOICE NODE":
            node_format["type"] = "RANKED ORDER NODE"
            node_format["quest_text"] = content[1] if len(content) > 1 else ""
            options = []

            question_lines = []
            answer_lines = []
            is_answer = False

            for line in content[2:]:
                if "TEXT NODE: Answer" in line or "ANSWER" in line:
                    is_answer = True
                    continue

                if line == "":
                    continue

                if is_answer:
                    answer_lines.append(line.strip())
                else:
                    question_lines.append(line.strip())

            for index, line in enumerate(question_lines, start=1):
                option = {
                    "text": line.strip(),
                    "value": line.strip(),
                    "index": index
                }
                options.append(option)
            node_format["options"] = options
            return node_format


        else:
            return None
        
    except Exception as e:
        print(e)
        print(content)
        return None
    


## create an api to import timezone mapping
@cms_import_api.doc(security='bearer')
@cms_import_api.route('/import_timezone_mapping', methods=['GET'])
class ImportTimezoneMapping(Resource):
    def get(self):
        from clientmodels import TimezoneMapping
        try:
            ## load the timezone mapping file
            from api.common.helper import timezone_mapping

            ## update the timezone mapping in the database
            for key, value in timezone_mapping.items():
                timezone = g.db_session.query(TimezoneMapping).filter(TimezoneMapping.abbreviation == key).first()
                if not timezone:
                    timezone = TimezoneMapping(abbreviation=key, full_name=value)
                    g.db_session.add(timezone)
            
            g.db_session.commit()

            return create_response("Timezone Mapping Updated")

        except Exception as e:
            return create_response(f"An error occurred: {e}", 500)