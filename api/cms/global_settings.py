import hashlib
import json
import uuid
from io import BytesIO

from flask import request
from flask_restx import Namespace, Resource, fields

from app import cache
from api.common.helper import create_response
from app import storage
from clientmodels import Setting, get_db_session

cms_global_settings_api = Namespace('api_cms_global_settings', description='Global settings related operations')


def generate_version_id():
    """Generate a unique version ID using UUID and a shorter hash"""
    # Generate a UUID
    unique_id = str(uuid.uuid4())
    # Create a shorter hash (first 8 characters)
    short_hash = hashlib.sha256(unique_id.encode()).hexdigest()[:8]
    return short_hash


# Parser for list endpoint
global_settings_parser = cms_global_settings_api.parser()
global_settings_parser.add_argument('page', type=int, default=1, help='Page number')
global_settings_parser.add_argument('limit', type=int, default=20, help='Items per page')


version_model = cms_global_settings_api.model('CityVersion', {
    'content': fields.Raw(required=True, description='City JSON content')
})


@cms_global_settings_api.route('/city/version')
class CityVersionList(Resource):
    @cms_global_settings_api.expect(global_settings_parser)
    def get(self):
        """Get list of city JSON versions"""
        
        versions = storage.list_files('global_settings/city/versions')
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        
        return create_response("City versions retrieved successfully", data=versions)

    @cms_global_settings_api.expect(version_model)
    @cache.invalidate_cache(pattern='global_setting:city')
    def post(self):
        """Create a new city JSON version"""
        data = request.json
        file_content = data.get('content')

        if not file_content:
            return create_response("File content is required", status=400)

        try:
            # Validate JSON content
            if isinstance(file_content, str):
                json_content = json.loads(file_content)
            else:
                json_content = file_content

            # Generate unique version ID
            version_id = generate_version_id()

            # Create version file name with timestamp
            filename = f"{version_id}.json"
            
            # Save to storage
            
            json_bytes = json.dumps(json_content).encode('utf-8')
            json_data = BytesIO(json_bytes)
            storage.upload_file(
                folder_name="global_settings/city/versions",
                file_name=filename,
                data=json_data
            )

            db_session = get_db_session()

            # Update settings table with latest version
            try:
                setting = db_session.query(Setting).filter(
                    Setting.key == 'current_city_version',
                    Setting.is_deleted == False
                ).first()
                
                if setting:
                    setting.value = version_id
                else:
                    setting = Setting(
                        key='current_city_version',
                        value=version_id,
                        description='Current city settings version'
                    )
                    db_session.add(setting)
                
                db_session.commit()
            except Exception as e:
                print(f"Error updating settings table: {str(e)}")
                # Don't fail the entire request if settings update fails
                pass
            
            return create_response("City version created successfully", 
                                data={"version": version_id, "filename": filename})
        except json.JSONDecodeError:
            return create_response("Invalid JSON content", status=400)
        except Exception as e:
            return create_response(f"Error creating version: {str(e)}", status=500)


@cms_global_settings_api.route('/city/version/<string:version>')
class CityVersionItem(Resource):
    def get(self, version):
        """Get specific city JSON version"""
        
        files = storage.list_files('global_settings/city/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        data = storage.download_file('global_settings/city/versions', f"{version_file['name']}")
        content = json.loads(data.read().decode('utf-8'))
        return create_response("Version retrieved successfully", data={"version": version, "content": content})

    @cache.invalidate_cache(pattern='global_setting:city')
    def delete(self, version):
        """Delete a specific city JSON version"""
        
        files = storage.list_files('global_settings/city/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        storage.delete_file('global_settings/city/versions', f"{version_file['name']}")
        
        return create_response("City version deleted successfully")


school_version_model = cms_global_settings_api.model('SchoolVersion', {
    'content': fields.Raw(required=True, description='School JSON content')
})


@cms_global_settings_api.route('/school/version')
class SchoolVersionList(Resource):
    @cms_global_settings_api.expect(global_settings_parser)
    def get(self):
        """Get list of school JSON versions"""
        
        versions = storage.list_files('global_settings/school/versions')
        versions.sort(key=lambda x: x['last_modified'], reverse=True)
        
        return create_response("School versions retrieved successfully", data=versions)

    @cms_global_settings_api.expect(school_version_model)
    @cache.invalidate_cache(pattern='global_setting:school')
    def post(self):
        """Create a new school JSON version"""
        data = request.json
        file_content = data.get('content')

        if not file_content:
            return create_response("File content is required", status=400)

        try:
            # Validate JSON content
            if isinstance(file_content, str):
                json_content = json.loads(file_content)
            else:
                json_content = file_content

            # Generate unique version ID
            version_id = generate_version_id()

            # Create version file name with timestamp
            filename = f"{version_id}.json"
            
            # Save to storage
            
            json_bytes = json.dumps(json_content).encode('utf-8')
            json_data = BytesIO(json_bytes)
            storage.upload_file(
                folder_name="global_settings/school/versions",
                file_name=filename,
                data=json_data
            )

            db_session = get_db_session()

            # Update settings table with latest version
            try:
                setting = db_session.query(Setting).filter(
                    Setting.key == 'current_school_version',
                    Setting.is_deleted == False
                ).first()
                
                if setting:
                    setting.value = version_id
                else:
                    setting = Setting(
                        key='current_school_version',
                        value=version_id,
                        description='Current school settings version'
                    )
                    db_session.add(setting)
                
                db_session.commit()
            except Exception as e:
                print(f"Error updating settings table: {str(e)}")
                # Don't fail the entire request if settings update fails
                pass
            
            return create_response("School version created successfully", 
                                data={"version": version_id, "filename": filename})
        except json.JSONDecodeError:
            return create_response("Invalid JSON content", status=400)
        except Exception as e:
            return create_response(f"Error creating version: {str(e)}", status=500)


@cms_global_settings_api.route('/school/version/<string:version>')
class SchoolVersionItem(Resource):
    def get(self, version):
        """Get specific school JSON version"""
        
        files = storage.list_files('global_settings/school/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        data = storage.download_file('global_settings/school/versions', f"{version_file['name']}")
        content = json.loads(data.read().decode('utf-8'))
        return create_response("Version retrieved successfully", data={"version": version, "content": content})

    @cache.invalidate_cache(pattern='global_setting:school')
    def delete(self, version):
        """Delete a specific school JSON version"""
        
        files = storage.list_files('global_settings/school/versions')
        
        # Find the file matching the requested version
        version_file = next((f for f in files if version in f['name']), None)
        
        if not version_file:
            return create_response("Version not found", status=404)
            
        storage.delete_file('global_settings/school/versions', f"{version_file['name']}")
        
        return create_response("School version deleted successfully")
