import datetime
import json
from flask import g, request
from flask_restx import Namespace, Resource, fields

from api.common.helper import create_response
from api.common.file import FileService
from clientmodels import Asset, Chest, ChestAssetAssociation, Program, Category, Tag, Facet, Badge, UserProgram, User
from clientmodels import ProgramCategoryAssociation, ProgramTagAssociation, ProgramFacetAssociation, \
    ProgramBadgeAssociation, ProgramQuestAssociation
from clientmodels import Quest, ProgramDivider
from api.common.publish_utils import release_program, release_quest

cms_programs_api = Namespace('api_cms_programs', description='Program management related operations')

program_model = cms_programs_api.model('Program', {
    'name': fields.String(required=True, description='Program Name'),
    'description': fields.String(required=False, description='Program Description'),
    'learning_objective': fields.String(required=False, description='Learning Objective'),
    'level': fields.Integer(required=False, description='Program Level', default=1),
    'category_ids': fields.List(fields.String, required=False, description='Category IDs'),
    'tag_ids': fields.List(fields.String, required=False, description='Tag IDs'),
    'facet_ids': fields.List(fields.String, required=False, description='Facet IDs'),
    'badge_ids': fields.List(fields.String, required=False, description='Badge IDs'),
    'image': fields.String(required=False, description='Thumbnail Image'),
    'image_webapp': fields.String(required=False, description='Web Application Image'),
    'dividers': fields.List(fields.Nested(cms_programs_api.model('ProgramDivider', {
        'divider_id': fields.String(required=True, description='Divider ID'),
        'order': fields.Integer(required=False, description='Order', default=0),
        'quest_ids': fields.List(fields.String, required=False, description='Quest IDs')
    })), required=False, description='Dividers'),
    'chest': fields.Nested(cms_programs_api.model('QuestChest', {
        'xp': fields.Integer(required=False, description='The quest xp', default=0),
        'coins': fields.Integer(required=False, description='The quest coins', default=0),
        'gems': fields.Integer(required=False, description='The quest gems', default=0),
        'keys': fields.Integer(required=False, description='The quest gems', default=0),
        'asset_ids': fields.List(fields.String, required=False, description='The asset identifiers'),
        'name': fields.String(required=False, description='The chest name'),
        'button_label': fields.String(required=False, description='The button label'),
        'facet_xp': fields.List(fields.Nested(cms_programs_api.model('Facet', {
            'id': fields.String(required=True, description='The facet id'),
            'xp': fields.Integer(required=False, description='The facet xp', default=0)
        })))
    })),
    'requires_quest_completion': fields.Boolean(required=False, description='Requires Quest Completion', default=True),
    'keys_required': fields.Integer(required=False, description='Keys Required', default=0)
})

programs_parser = cms_programs_api.parser()
programs_parser.add_argument('page', type=int, help='page number', location='args', required=False, default=1)
programs_parser.add_argument('limit', type=int, help='number of items per page', location='args', required=False, default=20)
programs_parser.add_argument('search', type=str, help='search query', location='args', required=False, default='')
programs_parser.add_argument('status', type=str, help='Filter by status', location='args', required=False, default=None)
programs_parser.add_argument('sort', type=str, help='Sort by', location='args', required=False, default=None)

@cms_programs_api.doc(security='bearer')
@cms_programs_api.route('/list')
class ProgramsList(Resource):
    @cms_programs_api.expect(programs_parser)
    def get(self):
        args = programs_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        search = args.get('search', '')
        status = args.get('status', None)
        sort = args.get('sort', None)
        offset = (page - 1) * limit

        from sqlalchemy.orm import aliased
        from sqlalchemy import or_
        create_user = aliased(User)
        update_user = aliased(User)

        query = g.client_db_session.query(
            Program,
            create_user,
            update_user
        ).outerjoin(
            create_user,
            create_user.id == Program.create_user
        ).outerjoin(
            update_user,
            update_user.id == Program.update_user
        )

        if search:
            query = query.filter(
                or_(
                    Program.name.ilike(f'%{search}%'),
                    Program.description.ilike(f'%{search}%')
                )
            )
        
        if status:
            query = query.filter(Program.status == status)

        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(Program, sort[1:]).desc())
            else:
                query = query.order_by(getattr(Program, sort))
        else:
            query = query.order_by(Program.date_created.desc())
        
        total = query.count()
        programs = query.offset(offset).limit(limit)
        
        data = []
        for program, create_user, update_user in programs:
            program_dict = program.to_dict()
            program_dict['create_user'] = create_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if create_user else None
            program_dict['update_user'] = update_user.to_dict(['id', 'first_name', 'last_name', 'email', 'image']) if update_user else None
            data.append(program_dict)

        return create_response("Programs List", data=data, total=total, page=page, limit=limit)
        
    
delete_parser = cms_programs_api.parser()
delete_parser.add_argument('force', type=bool, help='force delete', location='args', required=False, default=False)

@cms_programs_api.doc(security='bearer')
@cms_programs_api.route('/', methods=['POST'])
@cms_programs_api.route('/<string:program_id>', methods=['GET', 'PUT', 'DELETE'])
class ProgramItem(Resource):
    @cms_programs_api.expect(program_model)
    def post(self):
        data = request.json
        name = data.get('name', '')
        description = data.get('description', '')
        learning_objective = data.get('learning_objective', '')
        level = data.get('level', 0)
        image = data.get('image', '')
        image_webapp = data.get('image_webapp', '')
        category_ids = data.get('category_ids', [])
        tag_ids = data.get('tag_ids', [])
        facet_ids = data.get('facet_ids', [])
        badge_ids = data.get('badge_ids', [])
        requires_quest_completion = data.get('requires_quest_completion', True)
        keys_required = data.get('keys_required', 0)

        ## check if the name is already exists
        program = g.client_db_session.query(Program).filter(Program.name == name).first()
        if program is not None:
            return create_response("Program Name Already Exists", status=400)
        
        program = Program(name=name, description=description, learning_objective=learning_objective, level=level)
        program.requires_quest_completion = requires_quest_completion
        program.keys_required = keys_required
        g.client_db_session.add(program)
        g.client_db_session.commit()

        FileService.process_entity_image(program, image, 'program', program.id)
        
        FileService.process_entity_image(program, image_webapp, 'program', program.id, 'image_webapp')

        ## save categories
        for category_id in category_ids:
            category = g.client_db_session.query(Category).get(category_id)
            if category is not None:
                association = ProgramCategoryAssociation(program_id=program.id, category_id=category_id)
                g.client_db_session.add(association)

        ## save tags
        for tag_id in tag_ids:
            tag = g.client_db_session.query(Tag).get(tag_id)
            if tag is not None:
                association = ProgramTagAssociation(program_id=program.id, tag_id=tag_id)
                g.client_db_session.add(association)

        ## save facets
        for facet_id in facet_ids:
            facet = g.client_db_session.query(Facet).get(facet_id)
            if facet is not None:
                association = ProgramFacetAssociation(program_id=program.id, facet_id=facet_id)
                g.client_db_session.add(association)

        ## save badges
        for badge_id in badge_ids:
            badge = g.client_db_session.query(Badge).get(badge_id)
            if badge is not None:
                association = ProgramBadgeAssociation(program_id=program.id, badge_id=badge_id)
                g.client_db_session.add(association)

        ## save dividers
        dividers = data.get('dividers', [])
        for divider_data in dividers:
            divider_id = divider_data.get('divider_id')
            quest_ids = divider_data.get('quest_ids', [])

            if divider_id:
                divider = g.client_db_session.query(ProgramDivider).get(divider_id)
                if divider is not None:
                    divider.order = dividers.index(divider_data)
                    g.client_db_session.add(divider)

                for quest_id in quest_ids:
                    quest = g.client_db_session.query(Quest).get(quest_id)
                    if quest is not None:
                        association = ProgramQuestAssociation(program_id=program.id, quest_id=quest_id, program_divider_id=divider_id)
                        association.order = quest_ids.index(quest_id)
                        g.client_db_session.add(association)


        ## add chest
        chest = data.get('chest', {})
        if chest:
            xp = chest.get('xp', 0)
            coins = chest.get('coins', 0)
            gems = chest.get('gems', 0)
            keys = chest.get('keys', 0)
            asset_ids = chest.get('asset_ids', [])
            chest_name = chest.get('name', "YOUR REWARDS")
            button_label = chest.get('button_label', "Claim Rewards")
            facets = chest.get('facet_xp', [])

            ## create chest
            chest = Chest(
                xp=xp,
                coins=coins,
                gems=gems,
                keys=keys,
                name=chest_name,
                button_label=button_label,
                facet_xp = json.dumps(facets)
            )
            g.client_db_session.add(chest)
            g.client_db_session.commit()

            ## save chest assets
            for asset_id in asset_ids:
                asset = g.client_db_session.query(Asset).get(asset_id)
                if asset is not None:
                    association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                    g.client_db_session.add(association)

            ## save chest id in quest
            program.chest_id = chest.id

        g.client_db_session.add(program)
        g.client_db_session.commit()
        
        data = program.to_dict()

        return create_response("Program Created", data=data)
    
    def get(self, program_id):
        program = g.client_db_session.query(Program).get(program_id)
        if program is None:
            return create_response("Program Not Found", status=404)
        data = program.to_dict()

        ## get categories, tags, facets
        data['category_ids'] = [category.id for category in program.categories]
        data['tag_ids'] = [tag.id for tag in program.tags]
        data['facet_ids'] = [facet.id for facet in program.facets]
        data['badge_ids'] = [badge.id for badge in program.badges]

        ## get chest
        chest = {}
        if program.chest_id:
            chest = g.client_db_session.query(Chest).get(program.chest_id)
            if chest:
                chest_data = chest.to_dict()
                assets = []
                for asset in chest.assets:
                    assets.append(asset.id)
                chest_data['asset_ids'] = assets
                data['chest'] = chest_data
        else:
            data['chest'] = {
                'xp': 0,
                'coins': 0,
                'gems': 0,
                'keys': 0,
                'asset_ids': [],
                'name': "YOUR REWARDS",
                'button_label': "Claim Rewards",
                'facet_xp': []
            }
        
        data['dividers'] = []
        dividers = g.client_db_session.query(ProgramDivider).filter_by(program_id=program_id).all()
        for divider in dividers:
            quest_ids = [association.quest_id for association in g.client_db_session.query(ProgramQuestAssociation).filter_by(program_divider_id=divider.id).all()]
            data['dividers'].append({
                'divider_id': divider.id,
                'quest_ids': quest_ids,
                'name': divider.name,
                'order': divider.order if divider.order is not None else 0
            })

        ## sort dividers by order
        data['dividers'] = sorted(data['dividers'], key=lambda x: x['order'])

        return create_response("Program Detail", data=data)
    
    @cms_programs_api.expect(program_model)
    def put(self, program_id):
        data = request.json
        
        program = g.client_db_session.query(Program).filter_by(id=program_id).first()

        if program is None:
            return create_response("Program Not Found", status=404)
        
        name = data.get('name', '')
        description = data.get('description', '')
        learning_objective = data.get('learning_objective', '')
        level = data.get('level', 0)
        image = data.get('image', '')
        image_webapp = data.get('image_webapp', '')
        category_ids = data.get('category_ids', [])
        tag_ids = data.get('tag_ids', [])
        facet_ids = data.get('facet_ids', [])
        badge_ids = data.get('badge_ids', [])
        requires_quest_completion = data.get('requires_quest_completion', True)
        keys_required = data.get('keys_required', 0)
        
        ## check if the name is already exists
        if name != program.name:
            existing_program = g.client_db_session.query(Program).filter(Program.name.ilike(name)).first()
            if existing_program:
                return create_response("Program Name Already Exists", status=400)
            
        program.name = name
        program.description = description
        program.learning_objective = learning_objective
        program.level = level
        program.requires_quest_completion = requires_quest_completion
        program.keys_required = keys_required

        FileService.process_entity_image(program, image, 'program', program.id)
        
        FileService.process_entity_image(program, image_webapp, 'program', program.id, 'image_webapp')

        ## save categories, add new categories and remove deleted categories
        existing_categories = [category.id for category in program.categories]
        for category_id in category_ids:
            if category_id not in existing_categories:
                category = g.client_db_session.query(Category).get(category_id)
                if category is not None:
                    association = ProgramCategoryAssociation(program_id=program.id, category_id=category_id)
                    g.client_db_session.add(association)
        for category_id in existing_categories:
            if category_id not in category_ids:
                association = g.client_db_session.query(ProgramCategoryAssociation).filter_by(program_id=program.id, category_id=category_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## save tags, add new tags and remove deleted tags
        existing_tags = [tag.id for tag in program.tags]
        for tag_id in tag_ids:
            if tag_id not in existing_tags:
                tag = g.client_db_session.query(Tag).get(tag_id)
                if tag is not None:
                    association = ProgramTagAssociation(program_id=program.id, tag_id=tag_id)
                    g.client_db_session.add(association)
        for tag_id in existing_tags:
            if tag_id not in tag_ids:
                association = g.client_db_session.query(ProgramTagAssociation).filter_by(program_id=program.id, tag_id=tag_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## save facets, add new facets and remove deleted facets
        existing_facets = [facet.id for facet in program.facets]
        for facet_id in facet_ids:
            if facet_id not in existing_facets:
                facet = g.client_db_session.query(Facet).get(facet_id)
                if facet is not None:
                    association = ProgramFacetAssociation(program_id=program.id, facet_id=facet_id)
                    g.client_db_session.add(association)

        for facet_id in existing_facets:
            if facet_id not in facet_ids:
                association = g.client_db_session.query(ProgramFacetAssociation).filter_by(program_id=program.id, facet_id=facet_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## save badges, add new badges and remove deleted badges
        existing_badges = [badge.id for badge in program.badges]
        for badge_id in badge_ids:
            if badge_id not in existing_badges:
                badge = g.client_db_session.query(Badge).get(badge_id)
                if badge is not None:
                    association = ProgramBadgeAssociation(program_id=program.id, badge_id=badge_id)
                    g.client_db_session.add(association)

        for badge_id in existing_badges:
            if badge_id not in badge_ids:
                association = g.client_db_session.query(ProgramBadgeAssociation).filter_by(program_id=program.id, badge_id=badge_id).first()
                if association is not None:
                    g.client_db_session.delete(association)

        ## save quests, add new quests and remove deleted quests
        existing_dividers = g.client_db_session.query(ProgramDivider).filter_by(program_id=program.id).all()
        existing_divider_ids = [divider.id for divider in existing_dividers]

        dividers = data.get('dividers', [])
        for divider_data in dividers:
            divider_id = divider_data.get('divider_id')
            quest_ids = divider_data.get('quest_ids', [])

            divider = g.client_db_session.query(ProgramDivider).get(divider_id)
            if divider is not None:
                divider.order = dividers.index(divider_data)
                g.client_db_session.add(divider)
                
            if divider_id in existing_divider_ids:
                existing_quests = g.client_db_session.query(ProgramQuestAssociation).filter_by(program_divider_id=divider_id).all()
                existing_quest_ids = [association.quest_id for association in existing_quests]

            for quest_id in quest_ids:
                if quest_id not in existing_quest_ids:
                    quest = g.client_db_session.query(Quest).get(quest_id)
                    if quest is not None:
                        association = ProgramQuestAssociation(program_id=program.id, quest_id=quest_id, program_divider_id=divider_id)
                        association.order = quest_ids.index(quest_id)
                        g.client_db_session.add(association)
                else:
                    association = g.client_db_session.query(ProgramQuestAssociation).filter_by(program_id=program.id, quest_id=quest_id, program_divider_id=divider_id).first()
                    if association is not None:
                        association.order = quest_ids.index(quest_id)
                        g.client_db_session.add(association)

            for quest_id in existing_quest_ids:
                if quest_id not in quest_ids:
                    association = g.client_db_session.query(ProgramQuestAssociation).filter_by(program_id=program.id, quest_id=quest_id, program_divider_id=divider_id).first()
                    if association is not None:
                        g.client_db_session.delete(association)

        ## update chest if exists, add new assets and remove deleted assets. If chest does not exist, create a new chest
        chest = data.get('chest', {})
        if chest:
            xp = chest.get('xp', 0)
            coins = chest.get('coins', 0)
            gems = chest.get('gems', 0)
            keys = chest.get('keys', 0)
            asset_ids = chest.get('asset_ids', [])
            chest_name = chest.get('name', "YOUR REWARDS")
            button_label = chest.get('button_label', "Claim Rewards")
            facets = chest.get('facet_xp', [])

            ## check if chest exists
            if program.chest_id:
                chest = g.client_db_session.query(Chest).get(program.chest_id)
                if chest:
                    chest.xp = xp
                    chest.coins = coins
                    chest.gems = gems
                    chest.keys = keys
                    chest.name = chest_name
                    chest.button_label = button_label
                    chest.facet_xp = json.dumps(facets)

                ## check if chest assets changed, add new assets and remove deleted assets
                existing_assets = [asset.id for asset in chest.assets]
                for asset_id in asset_ids:
                    if asset_id not in existing_assets:
                        asset = g.client_db_session.query(Asset).get(asset_id)
                        if asset is not None:
                            association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                            g.client_db_session.add(association)
                for asset_id in existing_assets:
                    if asset_id not in asset_ids:
                        association = g.client_db_session.query(ChestAssetAssociation).filter(ChestAssetAssociation.chest_id == chest.id, ChestAssetAssociation.asset_id == asset_id).first()
                        if association is not None:
                            g.client_db_session.delete(association)

            else:
                ## create chest
                chest = Chest(
                    xp=xp,
                    coins=coins,
                    gems=gems,
                    keys=keys,
                    name=chest_name,
                    button_label=button_label,
                    facet_xp = json.dumps(facets)
                )
                g.client_db_session.add(chest)
                g.client_db_session.commit()

                ## save chest assets
                for asset_id in asset_ids:
                    asset = g.client_db_session.query(Asset).get(asset_id)
                    if asset is not None:
                        association = ChestAssetAssociation(chest_id=chest.id, asset_id=asset_id)
                        g.client_db_session.add(association)

                ## save chest id in program
                program.chest_id = chest.id

        ## update program modified date
        program.update_user = g.user_id
        program.date_updated = datetime.datetime.utcnow()


        g.client_db_session.commit()
        data = program.to_dict()
        
        return create_response("Program Updated", data=data)
    
    @cms_programs_api.expect(delete_parser)
    def delete(self, program_id):
        program = g.client_db_session.query(Program).filter_by(id=program_id).first()

        if program is None:
            return create_response("Program Not Found", status=404)
        
        args = delete_parser.parse_args()
        force = args.get('force', False)

        program.status = 'archived'
        g.client_db_session.commit()

        force_delete = request.args.get('force', 'false')
        if force_delete and force_delete == 'true':
            g.client_db_session.query(ProgramCategoryAssociation).filter_by(program_id=program_id).delete()
            g.client_db_session.query(ProgramTagAssociation).filter_by(program_id=program_id).delete()
            g.client_db_session.query(ProgramFacetAssociation).filter_by(program_id=program_id).delete()
            g.client_db_session.query(ProgramBadgeAssociation).filter_by(program_id=program_id).delete()
            g.client_db_session.query(ProgramQuestAssociation).filter_by(program_id=program_id).delete()
            g.client_db_session.query(ProgramDivider).filter_by(program_id=program_id).delete()
            chest_id = program.chest_id
            if chest_id:
                program.chest_id = None
                g.client_db_session.query(ChestAssetAssociation).filter(ChestAssetAssociation.chest_id == chest_id).delete()
                g.client_db_session.query(Chest).filter(Chest.id == chest_id).delete()

            g.db_session.query(UserProgram).filter(UserProgram.program_id == program_id).delete()
            
            g.client_db_session.delete(program)
            g.client_db_session.commit()

            return create_response("Program Deleted")

        return create_response("Program Archived")


divider_model = cms_programs_api.model('Divider', {
    'name': fields.String(required=True, description='Divider Name'),
    'program_id': fields.String(required=True, description='Program ID'),
    'order': fields.Integer(required=False, description='Order', default=0)
})

@cms_programs_api.doc(security='bearer')
@cms_programs_api.route('/divider', methods=['POST'])
@cms_programs_api.route('/divider/<string:divider_id>', methods=['DELETE'])
class ProgramDividerItem(Resource):
    @cms_programs_api.expect(divider_model)
    def post(self):
        data = request.json
        name = data.get('name', '')
        program_id = data.get('program_id', '')
        order = data.get('order', 0)

        program = g.client_db_session.query(Program).get(program_id)
        if program is None:
            return create_response("Program Not Found", status=404)

        divider = ProgramDivider(name=name, program_id=program_id, order=order)
        g.client_db_session.add(divider)
        g.client_db_session.commit()

        data = divider.to_dict()

        return create_response("Program Divider Created", data=data)
    

    def delete(self, divider_id):
        divider = g.client_db_session.query(ProgramDivider).get(divider_id)
        if divider is None:
            return create_response("Divider Not Found", status=404)
        g.client_db_session.delete(divider)
        g.client_db_session.commit()
        return create_response("Program Divider Deleted")


status_model = cms_programs_api.model('Status', {
    'status': fields.String(required=True, description='Program Status', enum=['Draft', 'Ready', 'Published', 'Archived'], default='Draft')
})

@cms_programs_api.doc(security='bearer')
@cms_programs_api.route('/<string:program_id>/status', methods=['PUT'])
class ProgramStatus(Resource):
    @cms_programs_api.expect(status_model)
    def put(self, program_id):
        data = request.json
        status = data.get('status', '')

        program = g.client_db_session.query(Program).get(program_id)
        if program is None:
            return create_response("Program Not Found", status=404)

        program.status = status
        g.client_db_session.commit()

        if status == 'Published':
            release_program(program, is_latest=False)

            ## get all quests in the program and release them
            quests = g.client_db_session.query(Quest).join(ProgramQuestAssociation).filter(ProgramQuestAssociation.program_id == program_id).all()
            for quest in quests:
                release_quest(quest, is_latest=False)
            
        return create_response("Program Status Updated", data={'program_id': program_id, 'status': status})