from flask import g
from flask import request
from flask_restx import Namespace, Resource, fields

from app import cache
from api.common.helper import create_response
from api.common.file import FileService
from clientmodels import Banner

cms_banners_api = Namespace('api_cms_banners', description='Banner management operations')

# Banner model for API documentation and validation
banner_model = cms_banners_api.model('BannerItem', {
    'title': fields.String(required=True, description='Banner Name'),
    'description': fields.String(required=False, description='Banner Description'),
    'image': fields.String(required=True, description='Banner Image Path'),
    'image_webapp': fields.String(required=False, description='Banner Image Path for Webapp'),
    'link': fields.String(required=False, description='Banner Link'),
    'link_type': fields.String(required=False, description='Banner Link Type (internal, external)'),
    'order': fields.Integer(required=False, description='Banner Order'),
})

@cms_banners_api.doc(security='bearer')
@cms_banners_api.route('/', methods=['POST'])
@cms_banners_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class BannerItem(Resource):
    def get(self, id):
        """Get a specific banner"""
        banner = g.client_db_session.query(Banner).filter_by(id=id).first()
        if banner is None:
            return create_response("Banner not found", status=404)

        return create_response("Banner retrieved successfully", 
                             data=banner.to_dict())

    @cms_banners_api.expect(banner_model)
    @cache.invalidate_cache(pattern='home:banners')
    def post(self):
        """Create a new banner"""
        # Parse request data
        data = request.json

        title = data.get('title', '').strip()
        description = data.get('description', '').strip()
        image = data.get('image', '').strip()
        image_webapp = data.get('image_webapp', '').strip()
        link = data.get('link', '').strip()
        link_type = data.get('link_type', '').strip()
        order = data.get('order', 0)

        banner = Banner()
        banner.title = title
        banner.description = description
        banner.link = link
        banner.link_type = link_type
        banner.order = order

        FileService.process_entity_image(banner, image, 'banner', banner.id)
        
        FileService.process_entity_image(banner, image_webapp, 'banner', banner.id, 'image_webapp')

        g.client_db_session.add(banner)
        g.client_db_session.commit()

        return create_response("Banner created successfully", data=banner.to_dict())

    @cms_banners_api.expect(banner_model)
    @cache.invalidate_cache(pattern='home:banners')
    def put(self, id):
        """Update a specific banner"""
        # Parse request data
        data = request.json

        banner = g.client_db_session.query(Banner).filter_by(id=id).first()
        if banner is None:
            return create_response("Banner not found", status=404)

        title = data.get('title', '').strip()
        description = data.get('description', '').strip()
        link = data.get('link', '').strip()
        link_type = data.get('link_type', '').strip()
        order = data.get('order', 0)

        image = data.get('image', '').strip()
        FileService.process_entity_image(banner, image, 'banner', banner.id)
        
        image_webapp = data.get('image_webapp', '').strip()
        FileService.process_entity_image(banner, image_webapp, 'banner_webapp', banner.id, 'image_webapp')
        
        banner.title = title
        banner.description = description
        banner.link = link
        banner.link_type = link_type
        banner.order = order

        g.client_db_session.commit()

        return create_response("Banner updated successfully", data=banner.to_dict())

    @cache.invalidate_cache(pattern='home:banners')
    def delete(self, id):
        """Delete a specific banner"""
        banner = g.client_db_session.query(Banner).filter_by(id=id).first()
        if banner is None:
            return create_response("Banner not found", status=404)

        g.client_db_session.delete(banner)
        g.client_db_session.commit()

        return create_response("Banner deleted successfully")
    

@cms_banners_api.doc(security='bearer')
@cms_banners_api.route('/list')
class BannerList(Resource):
    def get(self):
        """Get all banners"""
        banners = g.client_db_session.query(Banner).order_by(Banner.order).all()
        data = [banner.to_dict() for banner in banners]
        return create_response("Banners retrieved successfully", 
                             data=data)
    

@cms_banners_api.doc(security='bearer')
@cms_banners_api.route('/sort', methods=['POST'])
class BannerSort(Resource):
    @cache.invalidate_cache(pattern='home:banners')
    def post(self):
        """Sort banners"""
        # Parse request data
        data = request.json

        for index, item in enumerate(data):
            banner = g.client_db_session.query(Banner).filter_by(id=item['id']).first()
            if banner is None:
                return create_response("Banner not found", status=404)

            banner.order = index

        g.client_db_session.commit()

        return create_response("Banners reordered successfully")