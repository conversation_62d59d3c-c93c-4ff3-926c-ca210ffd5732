import datetime

from flask import g, request
from flask_restx import Namespace, Resource, fields, reqparse
from sqlalchemy import cast
from sqlalchemy.dialects.postgresql import JSONB

from api.common.decorator import check_user_permission
from api.common.helper import create_response
from api.common.helper import format_datetime_with_timezone
from api.common.helper import parse_bool_or_none
from api.common.notification import NotificationFunction
from clientmodels import Notification, UserNotification, User, NotificationTemplate, Client, Admin, EntitlementGroup

cms_notifications_api = Namespace('api_cms_notifications', description='Notification management operations')

# Define the parser for the notification list endpoint
parser = reqparse.RequestParser()
parser.add_argument('page', type=int, help='Page Number', default=1)
parser.add_argument('limit', type=int, help='Limit per page', default=20)
parser.add_argument('search', type=str, help='Search query', default='')
parser.add_argument('status', type=str, help='Notification Status')
parser.add_argument('sender_type', type=str, help='Sender Type, one of "super_admin", "admin", "automatic"')
parser.add_argument('is_sent', type=parse_bool_or_none, help='Is Sent, true or false')
parser.add_argument('schedule_type', type=str, help='Schedule Type, one of "scheduled", "immediate"')
parser.add_argument('to_recipients', type=str, action='append', help='Filter by recipients (list of user_ids or "all")')
parser.add_argument('sort', type=str, help='Sort by field')

# Define the notification list endpoint
@cms_notifications_api.doc(security='bearer')
@cms_notifications_api.route('/list', methods=['GET'])
class NotificationList(Resource):
    @cms_notifications_api.expect(parser)
    def get(self):
        """Get list of notifications with filters"""
        args = parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        offset = (page - 1) * limit
        search = args.get('search', '')
        status = args.get('status', '')
        sender_type = args.get('sender_type', '')
        is_sent = args.get('is_sent', None)
        schedule_type = args.get('schedule_type', '')
        to_recipients = args.get('to_recipients', '')
        sort = args.get('sort', None)

        query = g.db_session.query(
            Notification, 
            User.id, 
            User.first_name, 
            User.last_name, 
            User.email,
            Admin.role
        ).join(
            User, 
            User.id == Notification.create_user
        ).join(
            Client,
            Client.id == Notification.client_id
        ).join(
            Admin,
            Admin.user_id == User.id
        ).filter(
            Notification.is_deleted == False,
            Client.id_key == g.tenant_id
        )

        if search:
            query = query.filter(Notification.title.ilike(f'%{search}%'))

        if status:
            query = query.filter(Notification.status == status)

        # Filter by sender type
        if sender_type:
            if sender_type == 'super_admin':
                query = query.filter(Admin.role == 'super_admin')
            elif sender_type == 'admin':
                query = query.filter(Admin.role == 'admin')
            elif sender_type == 'communication':
                query = query.filter(Admin.role == 'communication')
            elif sender_type == 'automatic':
                query = query.filter(Notification.notification_type == 'in-app')

        # Filter by sent status
        if is_sent:
            if is_sent:
                query = query.filter(Notification.status == 'sent')
            else:
                query = query.filter(Notification.status.in_(['pending', 'failed']))

        # Filter by schedule type
        if schedule_type:
            if schedule_type == 'immediate':
                query = query.filter(Notification.scheduled_for.is_(None))
            else:  # scheduled
                query = query.filter(Notification.scheduled_for.isnot(None))

        # Filter by recipients
        if to_recipients:
            if to_recipients == 'all':
                # If 'all' is in the list, include notifications sent to everyone
                query = query.filter(Notification.recipient_type == 'all')
            elif to_recipients == 'entitlement_group':
                query = query.filter(Notification.recipient_type == 'entitlement_group')
            elif to_recipients == 'individual':
                # For each user_id in to_recipients, check if it exists in the user_ids JSON array
                query = query.filter(Notification.recipient_type == 'individual')

        # Sort by field
        if sort:
            if sort.startswith('-'):
                query = query.order_by(getattr(Notification, sort[1:]).desc())
            else:
                query = query.order_by(getattr(Notification, sort))
        else:
            query = query.order_by(Notification.date_created.desc())

        notifications = query.offset(offset).limit(limit).all()
        total = query.count()

        data = []
        for notification, user_id, user_first_name, user_last_name, user_email, role in notifications:
            notification_data = {
                'id': notification.id,
                'title': notification.title,
                'body': notification.body,
                'data': notification.data,
                'enable_notification_push': notification.enable_notification_push,
                'notification_type': notification.notification_type,
                'scheduled_for': format_datetime_with_timezone(notification.scheduled_for),
                'update_badge': notification.update_badge,
                'status': notification.status,
                'date_created': format_datetime_with_timezone(notification.date_created),
                'date_updated': format_datetime_with_timezone(notification.date_updated),
                'create_user': {
                    'id': user_id,
                    'first_name': user_first_name,
                    'last_name': user_last_name,
                    'email': user_email,
                    'role': role
                },
                'recipient_type': notification.recipient_type,
                'recipient_ids': notification.recipient_ids,
            }
            data.append(notification_data)

        return create_response("Notification list", data=data, total=total, page=page, limit=limit)


# Define the notification model
notification_model = cms_notifications_api.model('NotificationItem', {
    'recipient_type': fields.String(required=True, description='Recipient Type, one of "all", "entitlement_group", "individual"'),
    'recipient_ids': fields.List(fields.String, required=True, description='Recipient IDs, list of user_ids or "all"'),
    'title': fields.String(required=True, description='Notification Title'),
    'body': fields.String(required=True, description='Notification Body'),
    'data': fields.Raw(required=False, description='Notification Data'),
    'notification_type': fields.String(required=True, description='Notification Type'),
    'enable_notification_push': fields.Boolean(required=True, description='Enable Notification Push', default=False),
    'update_badge': fields.Boolean(required=True, description='Update Badge Count', default=False),
    'scheduled_for': fields.String(required=False, description='Scheduled Notification Time'),
})


# Define the notification object endpoint
@cms_notifications_api.doc(security='bearer')
@cms_notifications_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
@cms_notifications_api.route('/', methods=['POST'])
class NotificationObject(Resource):
    def get(self, id):

        notification = g.db_session.query(Notification).filter(
            Notification.id == id, 
            Notification.is_deleted == False
        ).first()

        if notification is None:
            return create_response("Notification not found", status=404)

        data = notification.to_dict([
            'id',
            'title',
            'body',
            'data',
            'enable_notification_push',
            'notification_type',
            'scheduled_for',
            'update_badge',
            'status',
            'date_created',
            'date_updated',
            'recipient_type',
        ])
        if notification.recipient_type == 'all':
            data.update({
                'recipients': 'all'
            })
        elif notification.recipient_type == 'entitlement_group':
            entitlement_groups = g.client_db_session.query(EntitlementGroup).filter(EntitlementGroup.id.in_(notification.recipient_ids))
            data.update({
                'entitlement_groups': [entitlement_group.to_dict(['id', 'name']) for entitlement_group in entitlement_groups]
            })
        elif notification.recipient_type == 'individual':
            users = g.client_db_session.query(User).filter(User.id.in_(notification.recipient_ids))
            data.update({
                'users': [user.to_dict(['id', 'first_name', 'last_name', 'email']) for user in users]
            })

        return create_response("Get notification", data=data)

    @cms_notifications_api.expect(notification_model)
    # @check_user_permission('admin')
    def post(self):
        data = request.json

        recipient_type = data.get('recipient_type')
        recipient_ids = data.get('recipient_ids')
        title = data.get('title')
        body = data.get('body')
        msg_data = data.get('data', {})
        notification_type = data.get('notification_type')
        enable_notification_push = data.get('enable_notification_push', False)
        update_badge = data.get('update_badge', False)
        scheduled_for = datetime.datetime.strptime(data.get('scheduled_for'), '%Y-%m-%d %H:%M:%SZ') if data.get('scheduled_for', '') else None

        # Get client info
        client = g.db_session.query(Client).filter(
            Client.id_key == g.tenant_id
        ).first()

        if not client:
            return create_response("Client not found", status=404)

        # Create notification using NotificationFunction
        nf = NotificationFunction()
        result = nf.create_notification(
            client_id=client.id,
            recipient_type=recipient_type,
            recipient_ids=recipient_ids,
            title=title,
            body=body,
            data=msg_data,
            notification_type=notification_type,
            enable_notification_push=enable_notification_push,
            update_badge=update_badge,
            scheduled_for=scheduled_for,
        )

        if result:
            return create_response("Notification created successfully", data=result.to_dict())
        else:
            return create_response("Failed to create notification", status=500)

    @cms_notifications_api.expect(notification_model)
    def put(self, id):
        """Update a notification"""
        try:
            data = request.json
            if not data:
                return create_response("No data found in request body", status=400)

            # Get notification
            notification = g.db_session.query(Notification).filter(
                Notification.id == id,
                Notification.is_deleted == False
            ).first()

            if not notification:
                return create_response("Notification not found", status=404)

            # Update notification fields
            recipient_type = data.get('recipient_type', '')
            recipient_ids = data.get('recipient_ids', [])
            title = data.get('title', '')
            body = data.get('body', '')
            msg_data = data.get('data', {})
            notification_type = data.get('notification_type', '')
            enable_notification_push = data.get('enable_notification_push', False)
            update_badge = data.get('update_badge', False)
            scheduled_for = datetime.datetime.strptime(data.get('scheduled_for'), '%Y-%m-%d %H:%M:%SZ') if data.get('scheduled_for', '') else None

            utc_now = datetime.datetime.utcnow()

            if scheduled_for and scheduled_for <= utc_now:
                return create_response("Cannot schedule notification in the past", status=400)

            if notification.status != 'pending':
                return create_response("Cannot update notification that has already been sent", status=400)

            # Update notification in queue if it hasn't been sent yet
            nf = NotificationFunction()
            result = nf.update_notification(
                notification_id=id,
                recipient_type=recipient_type if recipient_type and recipient_type != notification.recipient_type else None,
                recipient_ids=recipient_ids if recipient_ids and recipient_ids != notification.recipient_ids else None,
                title=title if title and title != notification.title else None,
                body=body if body and body != notification.body else None,
                data=msg_data if msg_data and msg_data != notification.data else None,
                notification_type=notification_type if notification_type and notification_type != notification.notification_type else None,
                enable_notification_push=enable_notification_push if enable_notification_push != notification.enable_notification_push else None,
                update_badge=update_badge if update_badge != notification.update_badge else None,
                scheduled_for=scheduled_for,
            )

            if result:
                return create_response("Notification updated successfully", data=notification.to_dict())
            else:
                return create_response("Failed to update notification", status=500)

        except Exception as e:
            return create_response(f"Failed to update notification: {str(e)}", status=500)

    def delete(self, id):
        notification = g.db_session.query(Notification).filter_by(id=id).first()
        if notification is None:
            return create_response("Notification not found", status=404)

        # Delete notification from queue if it hasn't been sent yet
        if notification.status not in ['pending']:
            return create_response("Failed to delete notification from queue", status=500)

        nf = NotificationFunction()
        result = nf.delete_notification(notification.id)
        
        if not result:
            return create_response("Failed to delete notification from queue", status=500)
        else:
            return create_response("Notification deleted successfully")


# Define the parser for the notification list endpoint
details_parser = reqparse.RequestParser()
details_parser.add_argument('page', type=int, help='Page Number', default=1)
details_parser.add_argument('limit', type=int, help='Limit per page', default=20)
details_parser.add_argument('status', type=str, help='Notification Status')


@cms_notifications_api.doc(security='bearer')
@cms_notifications_api.route('/<string:id>/details', methods=['GET'])
class NotificationDetails(Resource):
    @cms_notifications_api.expect(details_parser)
    def get(self, id):
        args = details_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        offset = (page - 1) * limit
        status = args.get('status', '')

        notification = g.db_session.query(Notification).filter(Notification.id == id, Notification.is_deleted == False).first()
        if notification is None:
            return create_response("Notification not found", status=404)

        user_notifications = g.db_session.query(UserNotification).filter(UserNotification.notification_id == id)

        if status:
            user_notifications = user_notifications.filter(UserNotification.status == status)

        total = user_notifications.count()
        user_notifications = user_notifications.offset(offset).limit(limit).all()

        data = []
        for user_notification in user_notifications:
            data.append({
                'id': user_notification.id,
                'notification_id': user_notification.notification_id,
                'user': {
                    'id': user_notification.user.id,
                    'first_name': user_notification.user.first_name,
                    'last_name': user_notification.user.last_name,
                    'email': user_notification.user.email
                },
                'status': user_notification.status,
                'is_read': user_notification.is_read,
                'date_created': format_datetime_with_timezone(user_notification.date_created),
                'date_updated': format_datetime_with_timezone(user_notification.date_updated),
            })

        return create_response("Get notification details", data=data, total=total, page=page, limit=limit)


# Define the notification template model
notification_template_model = cms_notifications_api.model('NotificationTemplateItem', {
    'name': fields.String(required=True, description='Notification Template Name'),
    'title': fields.String(required=True, description='Notification Title'),
    'body': fields.String(required=True, description='Notification Body'),
    'data': fields.Raw(required=False, description='Additional Notification Data'),
    'notification_type': fields.String(required=True, description='Notification Type'),
    'recipient_type': fields.String(required=True, description='Recipient Type, one of "all", "entitlement_group", "individual"'),
    'recipient_ids': fields.Raw(required=False, description='Recipient IDs, required if recipient_type is not "all"'),
}) 

# Define the parser for the notification template list endpoint
template_parser = reqparse.RequestParser()
template_parser.add_argument('page', type=int, help='Page Number', default=1)
template_parser.add_argument('limit', type=int, help='Limit per page', default=20)
template_parser.add_argument('search', type=str, help='Search query', default='')
template_parser.add_argument('status', type=str, help='Template Status')

# Define the notification template list endpoint
@cms_notifications_api.doc(security='bearer')
@cms_notifications_api.route('/templates/list', methods=['GET'])
class NotificationTemplateList(Resource):
    @cms_notifications_api.expect(template_parser)
    def get(self):
        args = template_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 20)
        offset = (page - 1) * limit
        search = args.get('search', '')
        status = args.get('status', '')

        query = g.db_session.query(
            NotificationTemplate, 
            User.id, 
            User.first_name, 
            User.last_name, 
            User.email
        ).join(
            User, 
            User.id == NotificationTemplate.create_user
        ).join(
            Client, 
            Client.id == NotificationTemplate.client_id
        ).filter(
            NotificationTemplate.is_deleted == False,
            Client.id_key == g.tenant_id
        )

        if search:
            query = query.filter(NotificationTemplate.title.ilike(f'%{search}%'))

        if status:
            query = query.filter(NotificationTemplate.status == status)

        templates = query.order_by(NotificationTemplate.date_created.desc()).offset(offset).limit(limit).all()
        total = query.count()

        data = []
        for template, user_id, user_first_name, user_last_name, user_email in templates:
            data.append({
                'id': template.id,
                'name': template.name,
                'title': template.title,
                'body': template.body,
                'data': template.data,
                'notification_type': template.notification_type,
                'recipient_type': template.recipient_type,
                'recipient_ids': template.recipient_ids,
                'status': template.status,
                'date_created': format_datetime_with_timezone(template.date_created),
                'date_updated': format_datetime_with_timezone(template.date_updated),
                'create_user': {
                    'id': user_id,
                    'first_name': user_first_name,
                    'last_name': user_last_name,
                    'email': user_email
                }
            })

        return create_response("Notification template list", data=data, total=total, page=page, limit=limit)

# Define the notification template object endpoint
@cms_notifications_api.doc(security='bearer')
@cms_notifications_api.route('/templates/<string:id>', methods=['GET', 'PUT', 'DELETE'])
@cms_notifications_api.route('/templates/', methods=['POST'])
class NotificationTemplateObject(Resource):
    def get(self, id):
        template = g.db_session.query(
            NotificationTemplate
        ).filter(
            NotificationTemplate.id == id, 
            NotificationTemplate.is_deleted == False
        ).first()

        if template is None:
            return create_response("Notification template not found", status=404)

        data = template.to_dict([
            'id', 
            'name', 
            'title', 
            'body', 
            'data', 
            'notification_type', 
            'recipient_type', 
            'status', 
            'date_created', 
            'date_updated'
        ])

        # Handle different recipient types
        if template.recipient_type == 'all':
            data.update({'recipients': 'all'})
        elif template.recipient_type == 'entitlement_group':
            # Fetch entitlement group details
            groups = g.client_db_session.query(EntitlementGroup).filter(
                EntitlementGroup.id.in_(template.recipient_ids)
            )
            data.update({
                'entitlement_groups': [group.to_dict(['id', 'name']) for group in groups]
            })
        elif template.recipient_type == 'individual':  # individual recipients
            users = g.client_db_session.query(User).filter(User.id.in_(template.recipient_ids))
            data.update({
                'users': [user.to_dict(['id', 'first_name', 'last_name', 'email']) for user in users]
            })

        return create_response("Get notification template", data=data)

    @cms_notifications_api.expect(notification_template_model)
    # @check_user_permission('admin')
    def post(self):
        data = request.json
        
        name = data.get('name')
        title = data.get('title')
        body = data.get('body')
        msg_data = data.get('data', {})
        notification_type = data.get('notification_type')
        recipient_type = data.get('recipient_type', 'all')
        recipient_ids = data.get('recipient_ids', [])
        
        # Get client info
        client = g.db_session.query(Client).filter(
            Client.id_key == g.tenant_id
        ).first()

        if not client:
            return create_response("Client not found", status=404)

        template = NotificationTemplate(
            name=name,
            title=title,
            body=body,
            data=msg_data,
            notification_type=notification_type,
            recipient_type=recipient_type,
            recipient_ids=recipient_ids,
            client_id=client.id
        )

        g.db_session.add(template)
        g.db_session.commit()

        return create_response("Notification template created successfully", data=template.to_dict())

    @cms_notifications_api.expect(notification_template_model)
    def put(self, id):
        try:
            data = request.json
            if not data:
                return create_response("No data found in request body", status=400)

            template = g.db_session.query(NotificationTemplate).filter(
                NotificationTemplate.id == id,
                NotificationTemplate.is_deleted == False
            ).first()

            if not template:
                return create_response("Notification template not found", status=404)

            if 'name' in data:
                template.name = data['name']
            if 'title' in data:
                template.title = data['title']
            if 'body' in data:
                template.body = data['body']
            if 'data' in data:
                template.data = data['data']
            if 'notification_type' in data:
                template.notification_type = data['notification_type']
            if 'recipient_type' in data:
                template.recipient_type = data['recipient_type']
            if 'recipient_ids' in data:
                template.recipient_ids = data['recipient_ids']

            g.db_session.commit()

            return create_response("Notification template updated successfully", data=template.to_dict())
        except Exception as e:
            return create_response(f"Failed to update notification template: {str(e)}", status=500)

    def delete(self, id):
        template = g.db_session.query(NotificationTemplate).filter(
            NotificationTemplate.id == id,
            NotificationTemplate.is_deleted == False
        ).first()

        if not template:
            return create_response("Notification template not found", status=404)

        template.delete()
        g.db_session.commit()

        return create_response("Notification template deleted successfully")
