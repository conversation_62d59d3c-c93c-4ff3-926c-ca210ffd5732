import datetime
import logging

from flask_restx import Namespace, Resource, fields
from flask import request, g
from urllib.parse import unquote

from app import cache
from clientmodels import (
    Client, Feed, FeedLike, FeedComment, UserAssignment, Xircle, 
    UserQuest, UserProgram, UserXperience, UserCheckin, Program, Xperience, Quest, User, UserStreak, UserStats,
    PackageProgramAssociation, ClientPackageAssociation, PackageXperienceAssociation, XperienceQuestAssociation,
    ProgramQuestAssociation,
    get_db_session
)
from api.common.helper import create_response
from api.common.decorator import check_user_permission
from sqlalchemy.orm import aliased
from services.posthog_query import PosthogQuery

logger = logging.getLogger(__name__)

cms_analyses_api = Namespace('api_cms_analyses', description='CMS Analyses APIs')

parser = cms_analyses_api.parser()

parser.add_argument('start_date', type=str, help='Start date', location='args', required=True)
parser.add_argument('end_date', type=str, help='End date', location='args', required=True)
parser.add_argument('client_id', type=str, help='Client ID', location='args', required=False)


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/xircle')
class XircleAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get Xircle creation analysis between two time periods across all client databases"""
        # Get start and end dates from request parameters
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)
        
        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)
            
        # Calculate metrics for both periods
        current_metrics = self.get_xircle_metrics(client_id, start_date, end_date) or {
            'xircle_count': 0,
            'feed_count': 0,
            'like_count': 0,
            'comment_count': 0
        }
        previous_period_length = end_date - start_date
        previous_start = start_date - previous_period_length
        previous_metrics = self.get_xircle_metrics(client_id, previous_start, start_date) or {
            'xircle_count': 0,
            'feed_count': 0,
            'like_count': 0,
            'comment_count': 0
        }
        
        # Calculate changes
        analysis = {
            'current_period': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'previous_period': {
                'start': previous_start.isoformat(),
                'end': start_date.isoformat()
            },
            'metrics': {
                'current': current_metrics,
                'previous': previous_metrics
            }
        }
        
        return create_response("Xircle analysis retrieved successfully", data=analysis)
    
    def get_xircle_metrics(self, client_id, start_date, end_date):
        """Calculate Xircle metrics for a given time period across all client databases"""
        from sqlalchemy import func
        
        # Initialize aggregated metrics
        total_xircle_count = 0
        total_feed_count = 0
        total_like_count = 0
        total_comment_count = 0
        
        try:
            # Get main database session
            db_session = get_db_session()
            
            # Get all active clients
            if client_id:
                clients = db_session.query(Client).filter(Client.id == client_id).all()
            else:
                clients = db_session.query(Client).filter(Client.id_key != 'global').all()
            
            if not clients:
                return None
            
            # Iterate through each client's database
            for client in clients:
                try:
                    # Get client database session
                    client_db_session = get_db_session(client.id_key)
                    if not client_db_session:
                        print(f"Could not get database session for client {client.id_key}")
                        continue
                    
                    # Get count of Xircles created during the period
                    xircle_count = client_db_session.query(Xircle.id).filter(
                        Xircle.date_created < end_date,
                        Xircle.is_deleted == False
                    ).count() or 0
                    total_xircle_count += xircle_count

                    # Get Xircles that are not deleted
                    xircle_subquery = (
                        client_db_session.query(Xircle.id).filter(
                            Xircle.is_deleted == False,
                            Xircle.name.not_ilike('%xapa%')
                        ).subquery()
                    )

                    if client_db_session.query(xircle_subquery).count() == 0:
                        print(f"No xircles found in client {client.id_key}")
                        continue
                    
                    # Create a subquery for posts created during the period
                    feed_subquery = (
                        client_db_session.query(Feed.id).join(
                            Xircle,
                            Feed.xircle_id == Xircle.id
                        ).filter(
                            Feed.date_created >= start_date,
                            Feed.date_created < end_date,
                            Feed.is_deleted == False,
                            Xircle.is_deleted == False,
                            Xircle.name.not_ilike('%xapa%')
                        ).subquery()
                    )

                    if client_db_session.query(feed_subquery).count() == 0:
                        print(f"No feeds found in client {client.id_key}")
                        continue
                    
                    # Get feed count
                    feed_count = client_db_session.query(
                        func.count(feed_subquery.c.id)
                    ).scalar() or 0
                    total_feed_count += feed_count
                    
                    # Get likes count using subquery
                    like_count = client_db_session.query(FeedLike).join(
                        Feed,
                        FeedLike.feed_id == Feed.id
                    ).join(
                        Xircle,
                        Feed.xircle_id == Xircle.id
                    ).filter(
                        FeedLike.date_created >= start_date,
                        FeedLike.date_created < end_date,
                        Feed.is_deleted == False,
                        Xircle.is_deleted == False,
                        Xircle.name.not_ilike('%xapa%')
                    ).count() or 0
                    total_like_count += like_count
                    
                    # Get comments count using subquery
                    comment_count = client_db_session.query(FeedComment).join(
                        Feed,
                        FeedComment.feed_id == Feed.id
                    ).join(
                        Xircle,
                        Feed.xircle_id == Xircle.id
                    ).filter(
                        FeedComment.date_created >= start_date,
                        FeedComment.date_created < end_date,
                        FeedComment.is_deleted == False,
                        Feed.is_deleted == False,
                        Xircle.is_deleted == False,
                        Xircle.name.not_ilike('%xapa%')
                    ).count() or 0
                    total_comment_count += comment_count
                    
                except Exception as e:
                    # Log the error but continue processing other clients
                    logger.error(f"Error processing client {client.id_key}: {str(e)}", exc_info=True)
                    continue
            
            return {
                'xircle_count': total_xircle_count,
                'feed_count': total_feed_count,
                'like_count': total_like_count,
                'comment_count': total_comment_count
            }
        except Exception as e:
            logger.error(f"Error in get_xircle_metrics: {str(e)}", exc_info=True)
            return None


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/xapa-xircle')
class XapaXircleAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get Xapa Xircle analysis including feed, like, and comment metrics with period comparison"""
        # Get start and end dates from request parameters
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)
        
        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)
        
        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)
            
        # Calculate metrics for both periods
        current_metrics = self.get_xapa_metrics(client_id, start_date, end_date) or {
            'feed_count': 0,
            'like_count': 0,
            'comment_count': 0,
        }
        
        # Calculate previous period metrics
        previous_period_length = end_date - start_date
        previous_start = start_date - previous_period_length
        previous_metrics = self.get_xapa_metrics(client_id, previous_start, start_date) or {
            'feed_count': 0,
            'like_count': 0,
            'comment_count': 0,
        }
        
        # Calculate changes
        analysis = {
            'current_period': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'previous_period': {
                'start': previous_start.isoformat(),
                'end': start_date.isoformat()
            },
            'metrics': {
                'current': current_metrics,
                'previous': previous_metrics
            }
        }
        
        return create_response("Xapa Xircle analysis retrieved successfully", data=analysis)
    
    def get_xapa_metrics(self, client_id, start_date, end_date):
        """Calculate Xapa metrics for a given time period across client databases"""
        try:
            # Get main database session
            db_session = get_db_session()
            
            # Get all active clients
            if client_id:
                clients = db_session.query(Client).filter(Client.id == client_id).all()
            else:
                clients = db_session.query(Client).filter(Client.id_key != 'global').all()
            
            if not clients:
                return None
            
            total_feed_count = 0
            total_like_count = 0
            total_comment_count = 0
            
            # Query for Xapa xircles
            xapa_xircle_subquery = (
                db_session.query(Xircle.id)
                .filter(
                    Xircle.is_deleted == False,
                    Xircle.name.ilike('xapa')  # Case-insensitive match
                )
                .subquery()
            )
            
            # Get feed count from main database
            feed_count = db_session.query(Feed).join(
                xapa_xircle_subquery,
                Feed.xircle_id == xapa_xircle_subquery.c.id
            ).filter(
                Feed.date_created >= start_date,
                Feed.date_created < end_date,
                Feed.is_deleted == False
            ).count()
            
            total_feed_count += feed_count
            
            # Iterate through each client's database
            for client in clients:
                try:
                    # Get client database session
                    client_db_session = get_db_session(client.id_key)
                    if not client_db_session:
                        print(f"Could not get database session for client {client.id_key}")
                        continue

                    # Query for Xapa xircles in client database
                    xapa_xircle_subquery = (
                        client_db_session.query(Xircle.id)
                        .filter(
                            Xircle.is_deleted == False,
                            Xircle.name.ilike('xapa')  # Case-insensitive match
                        )
                        .subquery()
                    )
                    
                    # Get likes count with proper joins
                    like_count = client_db_session.query(FeedLike).join(
                        Feed,
                        FeedLike.feed_id == Feed.id
                    ).join(
                        xapa_xircle_subquery,
                        Feed.xircle_id == xapa_xircle_subquery.c.id
                    ).filter(
                        FeedLike.date_created >= start_date,
                        FeedLike.date_created < end_date,
                        Feed.is_deleted == False
                    ).count()
                    total_like_count += like_count
                    
                    # Get comments count with proper joins
                    comment_count = client_db_session.query(FeedComment).join(
                        Feed,
                        FeedComment.feed_id == Feed.id
                    ).join(
                        xapa_xircle_subquery,
                        Feed.xircle_id == xapa_xircle_subquery.c.id
                    ).filter(
                        FeedComment.date_created >= start_date,
                        FeedComment.date_created < end_date,
                        FeedComment.is_deleted == False,
                        Feed.is_deleted == False
                    ).count()
                    total_comment_count += comment_count
                    
                except Exception as e:
                    logger.error(f"Error processing client {client.id_key}: {str(e)}", exc_info=True)
                    continue
            
            return {
                'feed_count': total_feed_count,
                'like_count': total_like_count,
                'comment_count': total_comment_count,
            }
            
        except Exception as e:
            logger.error(f"Error in get_xapa_metrics: {str(e)}", exc_info=True)
            return None


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/company-xircle')
class CompanyXircleAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('admin')
    def get(self):
        """Get Company Xircle analysis including feed, like, and comment metrics with period comparison"""
        # Get start and end dates from request parameters
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)
        
        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)
            
        # Calculate metrics for both periods
        current_metrics = self.get_company_metrics(client_id, start_date, end_date) or {
            'feed_count': 0,
            'like_count': 0,
            'comment_count': 0,
        }
        
        # Calculate previous period metrics
        previous_period_length = end_date - start_date
        previous_start = start_date - previous_period_length
        previous_metrics = self.get_company_metrics(client_id, previous_start, start_date) or {
            'feed_count': 0,
            'like_count': 0,
            'comment_count': 0,
        }
        
        # Calculate changes
        analysis = {
            'current_period': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'previous_period': {
                'start': previous_start.isoformat(),
                'end': start_date.isoformat()
            },
            'metrics': {
                'current': current_metrics,
                'previous': previous_metrics
            }
        }
        
        return create_response("Company Xircle analysis retrieved successfully", data=analysis)
    
    def get_company_metrics(self, client_id, start_date, end_date):
        """Calculate Company Xircle metrics for a given time period across client databases"""
        try:
            # Get main database session
            db_session = get_db_session()
            
            # Get all active clients
            if client_id:
                clients = db_session.query(Client).filter(Client.id == client_id).all()
            else:
                clients = db_session.query(Client).filter(Client.id_key != 'global').all()
            
            if not clients:
                return None
            
            total_feed_count = 0
            total_like_count = 0
            total_comment_count = 0
            
            # Iterate through each client's database
            for client in clients:
                try:
                    # Get client database session
                    client_db_session = get_db_session(client.id_key)
                    if not client_db_session:
                        print(f"Could not get database session for client {client.id_key}")
                        continue

                    # Query for company xircles (excluding Xapa)
                    company_xircle_subquery = (
                        client_db_session.query(Xircle.id)
                        .filter(
                            Xircle.is_deleted == False,
                            Xircle.id == '00000000-0000-0000-0000-000000000000'
                        ).subquery()
                    )
                    
                    # Get feeds from company xircles
                    feed_subquery = (
                        client_db_session.query(Feed.id)
                        .join(
                            company_xircle_subquery,
                            Feed.xircle_id == company_xircle_subquery.c.id
                        )
                        .filter(
                            Feed.date_created >= start_date,
                            Feed.date_created < end_date,
                            Feed.is_deleted == False
                        )
                        .subquery()
                    )

                    # Get feed count
                    feed_count = client_db_session.query(feed_subquery).count()
                    total_feed_count += feed_count

                    if feed_count == 0:
                        continue
                    
                    # Get likes count with proper joins
                    like_count = client_db_session.query(FeedLike).join(
                        Feed,
                        FeedLike.feed_id == Feed.id
                    ).join(
                        company_xircle_subquery,
                        Feed.xircle_id == company_xircle_subquery.c.id
                    ).filter(
                        FeedLike.date_created >= start_date,
                        FeedLike.date_created < end_date,
                        Feed.is_deleted == False
                    ).count()
                    total_like_count += like_count
                    
                    # Get comments count with proper joins
                    comment_count = client_db_session.query(FeedComment).join(
                        Feed,
                        FeedComment.feed_id == Feed.id
                    ).join(
                        company_xircle_subquery,
                        Feed.xircle_id == company_xircle_subquery.c.id
                    ).filter(
                        FeedComment.date_created >= start_date,
                        FeedComment.date_created < end_date,
                        FeedComment.is_deleted == False,
                        Feed.is_deleted == False
                    ).count()
                    total_comment_count += comment_count
                    
                except Exception as e:
                    logger.error(f"Error processing client {client.id_key}: {str(e)}", exc_info=True)
                    continue
            
            return {
                'feed_count': total_feed_count,
                'like_count': total_like_count,
                'comment_count': total_comment_count,
            }
            
        except Exception as e:
            logger.error(f"Error in get_company_metrics: {str(e)}", exc_info=True)
            return None


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/xapa-joy-score')
class XapaJoyScoreAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get Xapa joy score analysis based on user emotions from check-ins"""
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)
        
        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)
        
        # Determine if we should use daily or monthly aggregation
        use_daily = (end_date - start_date).days <= 180
            
        # Calculate the previous period for comparison
        period_length = end_date - start_date
        previous_end = start_date
        previous_start = previous_end - period_length
        
        # Get metrics for current period
        current_metrics = self.get_joy_score_metrics(client_id, start_date, end_date, use_daily) or {
            'total_checkins': 0,
            'total_users': 0,
            'average_joy_score': 0,
            'periods': [],
            'scores': [],
            'counts': []
        }
        
        # Get metrics for previous period
        previous_metrics = self.get_joy_score_metrics(client_id, previous_start, previous_end, use_daily) or {
            'total_checkins': 0,
            'total_users': 0,
            'average_joy_score': 0,
            'periods': [],
            'scores': [],
            'counts': []
        }
        
        return create_response('Successfully retrieved joy score metrics', data={
            'period': {
                'current': {
                    'start': start_date.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': end_date.strftime('%Y-%m-%d %H:%M:%SZ')
                },
                'previous': {
                    'start': previous_start.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': previous_end.strftime('%Y-%m-%d %H:%M:%SZ')
                },
                'aggregation': 'daily' if use_daily else 'monthly'
            },
            'metrics': {
                'current': current_metrics,
                'previous': previous_metrics
            }
        })
    
    def get_joy_score_metrics(self, client_id, start_date, end_date, use_daily=True):
        """Calculate joy score metrics based on user emotions in check-ins"""
        from sqlalchemy import and_
        
        # Define emotion scores
        emotion_scores = {
            'Happy': 80,
            'Valued': 80,
            'Excited': 75,
            'Confident': 70,
            'Proud': 70,
            'Okay': 30,
            'Under-Appreciated': 10,
            'Tired': 0,
            'Stressed': -10,
            'Frustrated': 0,
            'Anxious': -20,
            'Angry': -40
        }
        
        try:
            # Get main database session
            db_session = get_db_session()
            user_filter = db_session.query(User.id).join(UserAssignment, (User.id == UserAssignment.user_id) & (UserAssignment.client_id == client_id)).subquery()
            
            # Generate list of time periods (days or months)
            time_periods = []
            current_date = start_date
            
            if use_daily:
                # Daily aggregation
                while current_date <= end_date:
                    time_periods.append(current_date.strftime('%Y-%m-%d'))
                    current_date = current_date + datetime.timedelta(days=1)
            else:
                # Monthly aggregation
                while current_date <= end_date:
                    time_periods.append(current_date.strftime('%Y-%m'))
                    # Move to first day of next month
                    if current_date.month == 12:
                        current_date = current_date.replace(year=current_date.year + 1, month=1)
                    else:
                        current_date = current_date.replace(month=current_date.month + 1)
            
            # Initialize period scores and emotions dictionary
            period_scores = {period: {'total': 0, 'count': 0, 'emotions': {emotion: 0 for emotion in emotion_scores.keys()}} for period in time_periods}
            
            # Get all check-ins within the period in a single query
            checkins_query = db_session.query(
                UserCheckin.user_id,
                UserCheckin.date_created,
                UserCheckin.notes
            ).filter(
                and_(
                    UserCheckin.date_created >= start_date,
                    UserCheckin.date_created < end_date
                )
            )

            if client_id:
                checkins_query = checkins_query.filter(UserCheckin.user_id.in_(user_filter.select()))

            checkins = checkins_query.all()
            
            # Count total checkins
            total_checkins = len(checkins)
            
            # Count unique users
            unique_users = len(set(checkin.user_id for checkin in checkins))
            
            # Calculate total joy score
            total_joy_score = 0
            scored_checkins = 0
            
            for checkin in checkins:
                if not checkin.notes:
                    continue
                
                # Find the first matching emotion and its score
                score = None
                for emotion, value in emotion_scores.items():
                    if emotion in checkin.notes:
                        score = value
                        break
                
                if score is not None:
                    # Add to total joy score
                    total_joy_score += score
                    scored_checkins += 1
                    
                    # Determine the period key
                    if use_daily:
                        period_key = checkin.date_created.strftime('%Y-%m-%d')
                    else:
                        period_key = checkin.date_created.strftime('%Y-%m')
                    
                    # Add to period scores and track emotion count
                    if period_key in period_scores:
                        period_scores[period_key]['total'] += score
                        period_scores[period_key]['count'] += 1
                        # Track emotion count
                        for emotion in emotion_scores.keys():
                            if emotion in checkin.notes:
                                period_scores[period_key]['emotions'][emotion] += 1
                                break
            
            # Calculate average joy score
            average_joy_score = round(total_joy_score / scored_checkins, 2) if scored_checkins > 0 else 0
            
            # Convert period scores to separate arrays
            periods = []
            scores = []
            counts = []
            emotion_percentages = []
            
            # Sort periods chronologically
            sorted_periods = sorted(period_scores.keys())
            
            for period in sorted_periods:
                data = period_scores[period]
                periods.append(period)
                scores.append(round(data['total'] / data['count'], 2) if data['count'] > 0 else 0)
                counts.append(data['count'])
                
                # Calculate emotion percentages for the period
                period_percentages = {}
                total_emotions = sum(data['emotions'].values())
                if total_emotions > 0:
                    for emotion, count in data['emotions'].items():
                        period_percentages[emotion] = round((count / total_emotions) * 100, 2)
                else:
                    period_percentages = {emotion: 0 for emotion in emotion_scores.keys()}
                emotion_percentages.append(period_percentages)
            
            return {
                'total_checkins': total_checkins,
                'total_users': unique_users,
                'average_joy_score': average_joy_score,
                'periods': periods,
                'scores': scores,
                'counts': counts,
                'emotion_percentages': emotion_percentages
            }
            
        except Exception as e:
            logger.error(f"Error calculating joy score metrics: {str(e)}", exc_info=True)
            return None


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/engagement-content')
class EngagementContentAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get metrics for started and completed quests, programs, and experiences with time-based aggregation"""
        client_id = request.args.get('client_id', None)
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)

        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)

        # Calculate the previous period for comparison
        period_length = end_date - start_date
        previous_end = start_date
        previous_start = previous_end - period_length

        # Determine if we should use daily or monthly aggregation
        use_daily = (end_date - start_date).days <= 180
        
        # Get metrics for current period
        current_metrics = self.get_aggregated_metrics(client_id, start_date, end_date, use_daily) or {
            'periods': [],
            'started': {
                'quest': [],
                'program': [],
                'xperience': []
            },
            'completed': {
                'quest': [],
                'program': [],
                'xperience': []
            },
            'unique_users': []
        }
        
        # Get metrics for previous period
        previous_metrics = self.get_aggregated_metrics(client_id, previous_start, previous_end, use_daily) or {
            'periods': [],
            'started': {
                'quest': [],
                'program': [],
                'xperience': []
            },
            'completed': {
                'quest': [],
                'program': [],
                'xperience': []
            },
            'unique_users': []
        }

        response = {
            'period': {
                'current': {
                    'start': start_date.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': end_date.strftime('%Y-%m-%d %H:%M:%SZ')
                },
                'previous': {
                    'start': previous_start.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': previous_end.strftime('%Y-%m-%d %H:%M:%SZ')
                },
                'aggregation': 'daily' if use_daily else 'monthly'
            },
            'metrics': {
                'current': current_metrics,
                'previous': previous_metrics
            },
            'averages': {
                'current': self.calculate_averages(current_metrics),
                'previous': self.calculate_averages(previous_metrics)
            }
        }

        return create_response('Successfully retrieved engagement metrics', data=response)

    def get_aggregated_metrics(self, client_id, start_date, end_date, use_daily=True):
        """Get aggregated metrics for started and completed content"""
        try:
            db_session = get_db_session()
            user_filter = db_session.query(User.id).join(UserAssignment, (User.id == UserAssignment.user_id) & (UserAssignment.client_id == client_id)).subquery()
            
            # Generate list of time periods (days or months)
            periods = []
            time_periods = []
            current_date = start_date
            
            if use_daily:
                # Daily aggregation
                while current_date <= end_date:
                    periods.append(current_date.strftime('%Y-%m-%d'))
                    time_periods.append(current_date.strftime('%Y-%m-%d'))
                    current_date = current_date + datetime.timedelta(days=1)
            else:
                # Monthly aggregation
                while current_date <= end_date:
                    periods.append(current_date.strftime('%Y-%m'))
                    time_periods.append(current_date.strftime('%Y-%m'))
                    # Move to first day of next month
                    if current_date.month == 12:
                        current_date = current_date.replace(year=current_date.year + 1, month=1)
                    else:
                        current_date = current_date.replace(month=current_date.month + 1)
            
            # Initialize metrics structure
            metrics = {
                'periods': periods,
                'started': {
                    'quest': [0] * len(periods),
                    'program': [0] * len(periods),
                    'xperience': [0] * len(periods)
                },
                'completed': {
                    'quest': [0] * len(periods),
                    'program': [0] * len(periods),
                    'xperience': [0] * len(periods)
                },
                'unique_users': [0] * len(periods)
            }

            # Create a mapping of time period to index
            period_to_index = {period: idx for idx, period in enumerate(time_periods)}

            # For started quests - count distinct user_id, quest_id combinations
            # This ensures each quest is counted only once per user
            starred_quests_query = db_session.query(
                UserQuest.date_created,
                UserQuest.user_id,
                UserQuest.quest_id
            ).filter(
                UserQuest.date_created >= start_date,
                UserQuest.date_created <= end_date
            )

            if client_id:
                ClientPackageAssociationAlias = aliased(ClientPackageAssociation)
                # Filter quests by client
                client_quests_subquery = db_session.query(Quest.id).distinct().join(
                    XperienceQuestAssociation,
                    XperienceQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == XperienceQuestAssociation.xperience_id,
                    isouter=True
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id,
                    isouter=True
                ).join(
                    ProgramQuestAssociation,
                    ProgramQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == ProgramQuestAssociation.program_id,
                    isouter=True
                ).join(
                    ClientPackageAssociationAlias,
                    ClientPackageAssociationAlias.package_id == PackageProgramAssociation.package_id,
                    isouter=True
                ).filter(
                    (ClientPackageAssociation.client_id == client_id) |
                    (ClientPackageAssociationAlias.client_id == client_id)
                ).subquery()
                
                starred_quests_query = starred_quests_query.filter(UserQuest.quest_id.in_(client_quests_subquery.select()), UserQuest.user_id.in_(user_filter.select()))

            starred_quests = starred_quests_query.distinct(UserQuest.user_id, UserQuest.quest_id).all()

            # For started programs (no change needed as there's only one record per user-program)
            starred_programs_query = db_session.query(
                UserProgram.date_created,
                UserProgram.user_id
            ).filter(
                UserProgram.date_created >= start_date,
                UserProgram.date_created <= end_date
            )

            if client_id:
                # Filter programs by client
                client_programs_subquery = db_session.query(Program.id).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == Program.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageProgramAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                starred_programs_query = starred_programs_query.filter(UserProgram.program_id.in_(client_programs_subquery.select()), UserProgram.user_id.in_(user_filter.select()))

            starred_programs = starred_programs_query.all()

            # For started xperiences (no change needed as there's only one record per user-xperience)
            starred_xperiences_query = db_session.query(
                UserXperience.date_created,
                UserXperience.user_id
            ).filter(
                UserXperience.date_created >= start_date,
                UserXperience.date_created <= end_date
            )

            if client_id:
                # Filter xperiences by client
                client_xperiences_subquery = db_session.query(Xperience.id).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == Xperience.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                starred_xperiences_query = starred_xperiences_query.filter(UserXperience.xperience_id.in_(client_xperiences_subquery.select()), UserXperience.user_id.in_(user_filter.select()))

            starred_xperiences = starred_xperiences_query.all()

            # For completed quests - count distinct user_id, quest_id combinations
            completed_quests_query = db_session.query(
                UserQuest.date_completed,
                UserQuest.user_id,
                UserQuest.quest_id
            ).filter(
                UserQuest.status == 'completed',
                UserQuest.date_completed >= start_date,
                UserQuest.date_completed <= end_date
            )

            if client_id:
                ClientPackageAssociationAlias = aliased(ClientPackageAssociation)
                # Filter quests by client
                client_quests_subquery = db_session.query(Quest.id).distinct().join(
                    XperienceQuestAssociation,
                    XperienceQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == XperienceQuestAssociation.xperience_id,
                    isouter=True
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id,
                    isouter=True
                ).join(
                    ProgramQuestAssociation,
                    ProgramQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == ProgramQuestAssociation.program_id,
                    isouter=True
                ).join(
                    ClientPackageAssociationAlias,
                    ClientPackageAssociationAlias.package_id == PackageProgramAssociation.package_id,
                    isouter=True
                ).filter(
                    (ClientPackageAssociation.client_id == client_id) |
                    (ClientPackageAssociationAlias.client_id == client_id)
                ).subquery()
                
                completed_quests_query = completed_quests_query.filter(UserQuest.quest_id.in_(client_quests_subquery.select()), UserQuest.user_id.in_(user_filter.select()))

            completed_quests = completed_quests_query.distinct(UserQuest.user_id, UserQuest.quest_id).all()

            # For completed programs (no change needed)
            completed_programs_query = db_session.query(
                UserProgram.date_completed,
                UserProgram.user_id
            ).filter(
                UserProgram.status == 'completed',
                UserProgram.date_completed >= start_date,
                UserProgram.date_completed <= end_date
            )

            if client_id:
                # Filter programs by client
                client_programs_subquery = db_session.query(Program.id).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == Program.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageProgramAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                completed_programs_query = completed_programs_query.filter(UserProgram.program_id.in_(client_programs_subquery.select()), UserProgram.user_id.in_(user_filter.select()))

            completed_programs = completed_programs_query.all()

            # For completed xperiences (no change needed)
            completed_xperiences_query = db_session.query(
                UserXperience.date_completed,
                UserXperience.user_id
            ).filter(
                UserXperience.status == 'completed',
                UserXperience.date_completed >= start_date,
                UserXperience.date_completed <= end_date
            )

            if client_id:
                # Filter xperiences by client
                client_xperiences_subquery = db_session.query(Xperience.id).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == Xperience.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                completed_xperiences_query = completed_xperiences_query.filter(UserXperience.xperience_id.in_(client_xperiences_subquery.select()), UserXperience.user_id.in_(user_filter.select()))

            completed_xperiences = completed_xperiences_query.all()

            # For starred content and unique users
            # Create a dictionary to track unique users per period
            unique_starred_users_by_period = {period: set() for period in time_periods}
            
            # Track unique user-quest combinations per period
            unique_starred_quests_by_period = {period: set() for period in time_periods}

            # Process the data and aggregate by time period
            # For started quests - use the count from our query
            for quest in starred_quests:
                date_str = self._get_period_str(quest.date_created, use_daily)
                if date_str in period_to_index:
                    # Only count each user-quest combination once
                    quest_key = f"{quest.user_id}_{quest.quest_id}"
                    if quest_key not in unique_starred_quests_by_period[date_str]:
                        unique_starred_quests_by_period[date_str].add(quest_key)
                        metrics['started']['quest'][period_to_index[date_str]] += 1
                    unique_starred_users_by_period[date_str].add(quest.user_id)

            # For started programs
            for program in starred_programs:
                date_str = self._get_period_str(program.date_created, use_daily)
                if date_str in period_to_index:
                    metrics['started']['program'][period_to_index[date_str]] += 1
                    unique_starred_users_by_period[date_str].add(program.user_id)

            # For started xperiences
            for xperience in starred_xperiences:
                date_str = self._get_period_str(xperience.date_created, use_daily)
                if date_str in period_to_index:
                    metrics['started']['xperience'][period_to_index[date_str]] += 1
                    unique_starred_users_by_period[date_str].add(xperience.user_id)

            # For completed content and unique users
            # Create a dictionary to track unique users per period
            unique_completed_users_by_period = {period: set() for period in time_periods}
            
            # Track unique user-quest combinations per period
            unique_completed_quests_by_period = {period: set() for period in time_periods}

            # Process completed quests
            for quest in completed_quests:
                date_str = self._get_period_str(quest.date_completed, use_daily)
                if date_str in period_to_index:
                    # Only count each user-quest combination once
                    quest_key = f"{quest.user_id}_{quest.quest_id}"
                    if quest_key not in unique_completed_quests_by_period[date_str]:
                        unique_completed_quests_by_period[date_str].add(quest_key)
                        metrics['completed']['quest'][period_to_index[date_str]] += 1
                    unique_completed_users_by_period[date_str].add(quest.user_id)

            # Process completed programs
            for program in completed_programs:
                date_str = self._get_period_str(program.date_completed, use_daily)
                if date_str in period_to_index:
                    metrics['completed']['program'][period_to_index[date_str]] += 1
                    unique_completed_users_by_period[date_str].add(program.user_id)

            # Process completed xperiences
            for xperience in completed_xperiences:
                date_str = self._get_period_str(xperience.date_completed, use_daily)
                if date_str in period_to_index:
                    metrics['completed']['xperience'][period_to_index[date_str]] += 1
                    unique_completed_users_by_period[date_str].add(xperience.user_id)

            # Calculate unique users count for each period
            for period, idx in period_to_index.items():
                metrics['unique_users'][idx] = len(unique_completed_users_by_period[period])

            return metrics

        except Exception as e:
            logger.error(f"Error calculating engagement metrics: {str(e)}", exc_info=True)
            return None

    def _get_period_str(self, date, use_daily):
        """Helper method to get the period string from a date"""
        if use_daily:
            return date.strftime('%Y-%m-%d')
        else:
            return date.strftime('%Y-%m')

    def calculate_averages(self, metrics):
        """Calculate average values for metrics"""
        # Initialize averages structure
        averages = {
            'started': {
                'quest': 0,
                'program': 0,
                'xperience': 0
            },
            'completed': {
                'quest': 0,
                'program': 0,
                'xperience': 0
            },
            'unique_users': 0
        }
        
        # Calculate averages if there are data points
        if len(metrics['periods']) > 0:
            averages['started']['quest'] = sum(metrics['started']['quest']) / len(metrics['periods'])
            averages['started']['program'] = sum(metrics['started']['program']) / len(metrics['periods'])
            averages['started']['xperience'] = sum(metrics['started']['xperience']) / len(metrics['periods'])
            
            averages['completed']['quest'] = sum(metrics['completed']['quest']) / len(metrics['periods'])
            averages['completed']['program'] = sum(metrics['completed']['program']) / len(metrics['periods'])
            averages['completed']['xperience'] = sum(metrics['completed']['xperience']) / len(metrics['periods'])
            
            averages['unique_users'] = sum(metrics['unique_users']) / len(metrics['periods'])
        
        return averages


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/engagement-user')
class EngagementUserAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get engagement percentage metrics for quests, experiences, and programs"""
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)

        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)

        # Calculate previous period dates (same duration as current period)
        period_length = end_date - start_date
        previous_end = start_date
        previous_start = previous_end - period_length

        # Get metrics for both periods
        current_metrics = self.get_engagement_metrics(client_id, start_date, end_date) or {
            'total_active_users': 0,
            'quest': {
                'users_started': 0,
                'users_completed': 0,
                'started_percentage': 0,
                'completed_percentage': 0,
                'completion_rate': 0
            },
            'xperience': {
                'users_started': 0,
                'users_completed': 0,
                'started_percentage': 0,
                'completed_percentage': 0,
                'completion_rate': 0
            },
            'program': {
                'users_started': 0,
                'users_completed': 0,
                'started_percentage': 0,
                'completed_percentage': 0,
                'completion_rate': 0
            }
        }
        previous_metrics = self.get_engagement_metrics(client_id, previous_start, previous_end) or {
            'total_active_users': 0,
            'quest': {
                'users_started': 0,
                'users_completed': 0,
                'started_percentage': 0,
                'completed_percentage': 0,
                'completion_rate': 0
            },
            'xperience': {
                'users_started': 0,
                'users_completed': 0,
                'started_percentage': 0,
                'completed_percentage': 0,
                'completion_rate': 0
            },
            'program': {
                'users_started': 0,
                'users_completed': 0,
                'started_percentage': 0,
                'completed_percentage': 0,
                'completion_rate': 0
            }
        }

        # Format response
        return create_response('Successfully retrieved engagement metrics', data={
            'period': {
                'current': {
                    'start': start_date.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': end_date.strftime('%Y-%m-%d %H:%M:%SZ')
                },
                'previous': {
                    'start': previous_start.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': previous_end.strftime('%Y-%m-%d %H:%M:%SZ')
                }
            },
            'metrics': {
                'current': current_metrics,
                'previous': previous_metrics
            }
        })

    def get_engagement_metrics(self, client_id, start_date, end_date):
        """Calculate engagement percentage metrics for quests, experiences, and programs"""
        try:
            # Get database session
            db_session = get_db_session()
            user_filter = db_session.query(User.id).join(UserAssignment, (User.id == UserAssignment.user_id) & (UserAssignment.client_id == client_id)).subquery()
            
            # Get active users which has streak between start_date and end_date
            total_active_users_query = db_session.query(UserStreak.user_id).filter(
                UserStreak.date >= start_date,
                UserStreak.date <= end_date
            )
            if client_id:
                total_active_users_query = total_active_users_query.filter(UserStreak.user_id.in_(user_filter.select()))
            total_active_users = total_active_users_query.distinct().count() or 1

            # Get unique users who started quests (count each user only once)
            started_quest_users_query = db_session.query(
                UserQuest.user_id
            ).filter(
                UserQuest.date_created.isnot(None),
                UserQuest.date_created >= start_date,
                UserQuest.date_created < end_date
            )

            if client_id:
                ClientPackageAssociationAlias = aliased(ClientPackageAssociation)
                # Filter quests by client
                client_quests_subquery = db_session.query(Quest.id).distinct().join(
                    XperienceQuestAssociation,
                    XperienceQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == XperienceQuestAssociation.xperience_id,
                    isouter=True
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id,
                    isouter=True
                ).join(
                    ProgramQuestAssociation,
                    ProgramQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == ProgramQuestAssociation.program_id,
                    isouter=True
                ).join(
                    ClientPackageAssociationAlias,
                    ClientPackageAssociationAlias.package_id == PackageProgramAssociation.package_id,
                    isouter=True
                ).filter(
                    (ClientPackageAssociation.client_id == client_id) |
                    (ClientPackageAssociationAlias.client_id == client_id)
                ).subquery()
                
                started_quest_users_query = started_quest_users_query.filter(UserQuest.quest_id.in_(client_quests_subquery.select()), UserQuest.user_id.in_(user_filter.select()))

            started_quest_users = set(row[0] for row in started_quest_users_query.distinct(UserQuest.user_id).all())
            
            # Get unique users who completed quests (count each user only once)
            completed_quest_users_query = db_session.query(
                UserQuest.user_id
            ).filter(
                UserQuest.status == 'completed',
                UserQuest.date_completed >= start_date,
                UserQuest.date_completed < end_date
            )

            if client_id:
                ClientPackageAssociationAlias = aliased(ClientPackageAssociation)
                # Filter quests by client
                client_quests_subquery = db_session.query(Quest.id).distinct().join(
                    XperienceQuestAssociation,
                    XperienceQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == XperienceQuestAssociation.xperience_id,
                    isouter=True
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id,
                    isouter=True
                ).join(
                    ProgramQuestAssociation,
                    ProgramQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == ProgramQuestAssociation.program_id,
                    isouter=True
                ).join(
                    ClientPackageAssociationAlias,
                    ClientPackageAssociationAlias.package_id == PackageProgramAssociation.package_id,
                    isouter=True
                ).filter(
                    (ClientPackageAssociation.client_id == client_id) |
                    (ClientPackageAssociationAlias.client_id == client_id)
                ).subquery()
                
                completed_quest_users_query = completed_quest_users_query.filter(UserQuest.quest_id.in_(client_quests_subquery.select()), UserQuest.user_id.in_(user_filter.select()))

            completed_quest_users = set(row[0] for row in completed_quest_users_query.distinct(UserQuest.user_id).all())
            
            # Get users who started xperiences (no change needed as there's only one record per user-xperience)
            started_xperience_users_query = db_session.query(
                UserXperience.user_id
            ).filter(
                UserXperience.date_created.isnot(None),
                UserXperience.date_created >= start_date,
                UserXperience.date_created < end_date
            )

            if client_id:
                # Filter xperiences by client
                client_xperiences_subquery = db_session.query(Xperience.id).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == Xperience.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                started_xperience_users_query = started_xperience_users_query.filter(UserXperience.xperience_id.in_(client_xperiences_subquery.select()), UserXperience.user_id.in_(user_filter.select()))

            started_xperience_users = set(row[0] for row in started_xperience_users_query.distinct(UserXperience.user_id).all())
            
            # Get users who completed xperiences (no change needed as there's only one record per user-xperience)
            completed_xperience_users_query = db_session.query(
                UserXperience.user_id
            ).filter(
                UserXperience.status == 'completed',
                UserXperience.date_completed >= start_date,
                UserXperience.date_completed < end_date
            )

            if client_id:
                # Filter xperiences by client
                client_xperiences_subquery = db_session.query(Xperience.id).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == Xperience.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                completed_xperience_users_query = completed_xperience_users_query.filter(UserXperience.xperience_id.in_(client_xperiences_subquery.select()), UserXperience.user_id.in_(user_filter.select()))

            completed_xperience_users = set(row[0] for row in completed_xperience_users_query.distinct(UserXperience.user_id).all())
            
            # Get users who started programs (no change needed as there's only one record per user-program)
            started_program_users_query = db_session.query(
                UserProgram.user_id
            ).filter(
                UserProgram.date_created.isnot(None),
                UserProgram.date_created >= start_date,
                UserProgram.date_created < end_date
            )

            if client_id:
                # Filter programs by client
                client_programs_subquery = db_session.query(Program.id).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == Program.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageProgramAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                started_program_users_query = started_program_users_query.filter(UserProgram.program_id.in_(client_programs_subquery.select()), UserProgram.user_id.in_(user_filter.select()))

            started_program_users = set(row[0] for row in started_program_users_query.distinct(UserProgram.user_id).all())
            
            # Get users who completed programs (no change needed as there's only one record per user-program)
            completed_program_users_query = db_session.query(
                UserProgram.user_id
            ).filter(
                UserProgram.status == 'completed',
                UserProgram.date_completed >= start_date,
                UserProgram.date_completed < end_date
            )

            if client_id:
                # Filter programs by client
                client_programs_subquery = db_session.query(Program.id).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == Program.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageProgramAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                completed_program_users_query = completed_program_users_query.filter(UserProgram.program_id.in_(client_programs_subquery.select()), UserProgram.user_id.in_(user_filter.select()))

            completed_program_users = set(row[0] for row in completed_program_users_query.distinct(UserProgram.user_id).all())
            
            # Calculate counts
            quest_users_started = len(started_quest_users)
            quest_users_completed = len(completed_quest_users)
            xperience_users_started = len(started_xperience_users)
            xperience_users_completed = len(completed_xperience_users)
            program_users_started = len(started_program_users)
            program_users_completed = len(completed_program_users)
            
            # Calculate percentages
            quest_started_percentage = round((quest_users_started / total_active_users) * 100, 2)
            quest_completed_percentage = round((quest_users_completed / total_active_users) * 100, 2)
            xperience_started_percentage = round((xperience_users_started / total_active_users) * 100, 2)
            xperience_completed_percentage = round((xperience_users_completed / total_active_users) * 100, 2)
            program_started_percentage = round((program_users_started / total_active_users) * 100, 2)
            program_completed_percentage = round((program_users_completed / total_active_users) * 100, 2)
            
            # Calculate completion rates (completed users / started users)
            quest_completion_rate = round((quest_users_completed / quest_users_started) * 100, 2) if quest_users_started > 0 else 0
            xperience_completion_rate = round((xperience_users_completed / xperience_users_started) * 100, 2) if xperience_users_started > 0 else 0
            program_completion_rate = round((program_users_completed / program_users_started) * 100, 2) if program_users_started > 0 else 0

            return {
                'total_active_users': total_active_users,
                'quest': {
                    'users_started': quest_users_started,
                    'users_completed': quest_users_completed,
                    'started_percentage': quest_started_percentage,
                    'completed_percentage': quest_completed_percentage,
                    'completion_rate': quest_completion_rate
                },
                'xperience': {
                    'users_started': xperience_users_started,
                    'users_completed': xperience_users_completed,
                    'started_percentage': xperience_started_percentage,
                    'completed_percentage': xperience_completed_percentage,
                    'completion_rate': xperience_completion_rate
                },
                'program': {
                    'users_started': program_users_started,
                    'users_completed': program_users_completed,
                    'started_percentage': program_started_percentage,
                    'completed_percentage': program_completed_percentage,
                    'completion_rate': program_completion_rate
                }
            }

        except Exception as e:
            logger.error(f"Error in get_engagement_metrics: {str(e)}", exc_info=True)
            return None


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/content-completion')
class ContentCompletionAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get completion time metrics for quests, programs, and experiences"""
        # Get start and end dates from request parameters
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)
        
        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)

        # Calculate the previous period
        period_length = end_date - start_date
        previous_end = start_date
        previous_start = previous_end - period_length

        # Get metrics for both periods
        current_metrics = self.get_completion_metrics(client_id, start_date, end_date) or {
            'quest': {
                'average_time': 0,
                'total_completed': 0,
                'users_completed': 0
            },
            'xperience': {
                'average_time': 0,
                'total_completed': 0,
                'users_completed': 0
            },
            'program': {
                'average_time': 0,
                'total_completed': 0,
                'users_completed': 0
            }
        }
        previous_metrics = self.get_completion_metrics(client_id, previous_start, previous_end) or {
            'quest': {
                'average_time': 0,
                'total_completed': 0,
                'users_completed': 0
            },
            'xperience': {
                'average_time': 0,
                'total_completed': 0,
                'users_completed': 0
            },
            'program': {
                'average_time': 0,
                'total_completed': 0,
                'users_completed': 0
            }
        }

        response = {
            'period': {
                'current': {
                    'start': start_date.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': end_date.strftime('%Y-%m-%d %H:%M:%SZ')
                },
                'previous': {
                    'start': previous_start.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': previous_end.strftime('%Y-%m-%d %H:%M:%SZ')
                }
            },
            'metrics': {
                'current': current_metrics,
                'previous': previous_metrics
            }
        }

        return create_response('Successfully retrieved engagement metrics', data=response)
    
    def get_completion_metrics(self, client_id, start_date, end_date):
        """Calculate completion time metrics for quests, experiences, and programs"""
        try:
            # Get database session
            db_session = get_db_session()
            user_filter = db_session.query(User.id).join(UserAssignment, (User.id == UserAssignment.user_id) & (UserAssignment.client_id == client_id)).subquery()

            # Get completed quests with time_spent field for more accurate time calculation
            # Use distinct on user_id and quest_id to avoid counting the same quest multiple times
            completed_quests_query = db_session.query(
                UserQuest.user_id,
                UserQuest.quest_id,
                UserQuest.time_spent,
                UserQuest.date_completed,
            ).filter(
                UserQuest.status == 'completed',
                UserQuest.date_completed.isnot(None),
                UserQuest.date_completed >= start_date,
                UserQuest.date_completed < end_date,
            )

            if client_id:
                ClientPackageAssociationAlias = aliased(ClientPackageAssociation)
                # Filter quests by client
                client_quests_subquery = db_session.query(Quest.id).distinct().join(
                    XperienceQuestAssociation,
                    XperienceQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == XperienceQuestAssociation.xperience_id,
                    isouter=True
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id,
                    isouter=True
                ).join(
                    ProgramQuestAssociation,
                    ProgramQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == ProgramQuestAssociation.program_id,
                    isouter=True
                ).join(
                    ClientPackageAssociationAlias,
                    ClientPackageAssociationAlias.package_id == PackageProgramAssociation.package_id,
                    isouter=True
                ).filter(
                    (ClientPackageAssociation.client_id == client_id) |
                    (ClientPackageAssociationAlias.client_id == client_id)
                ).subquery()
                
                completed_quests_query = completed_quests_query.filter(UserQuest.quest_id.in_(client_quests_subquery.select()), UserQuest.user_id.in_(user_filter.select()))

            completed_quests = completed_quests_query.distinct(UserQuest.user_id, UserQuest.quest_id).all()
            
            # Get completed xperiences with time_spent field
            completed_xperiences_query = db_session.query(
                UserXperience.user_id,
                UserXperience.date_completed,
                UserXperience.date_started,
                UserXperience.time_spent
            ).filter(
                UserXperience.status == 'completed',
                UserXperience.date_completed.isnot(None),
                UserXperience.date_started.isnot(None),
                UserXperience.date_completed >= start_date,
                UserXperience.date_completed < end_date,
            )

            if client_id:
                # Filter xperiences by client
                client_xperiences_subquery = db_session.query(Xperience.id).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == Xperience.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                completed_xperiences_query = completed_xperiences_query.filter(
                    UserXperience.xperience_id.in_(client_xperiences_subquery.select()),
                    UserXperience.user_id.in_(user_filter.select())
                )
            
            completed_xperiences = completed_xperiences_query.all()
            
            # Get completed programs with time_spent field
            completed_programs_query = db_session.query(
                UserProgram.user_id,
                UserProgram.date_completed,
                UserProgram.date_started,
                UserProgram.time_spent
            ).filter(
                UserProgram.status == 'completed',
                UserProgram.date_completed.isnot(None),
                UserProgram.date_started.isnot(None),
                UserProgram.date_completed >= start_date,
                UserProgram.date_completed < end_date,
            )

            if client_id:
                # Filter programs by client
                client_programs_subquery = db_session.query(Program.id).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == Program.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageProgramAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                completed_programs_query = completed_programs_query.filter(
                    UserProgram.program_id.in_(client_programs_subquery.select()),
                    UserProgram.user_id.in_(user_filter.select())
                )
            
            completed_programs = completed_programs_query.all()
            
            # Calculate quest metrics
            quest_total = len(completed_quests)
            quest_users = len(set(q.user_id for q in completed_quests))
            quest_avg_time = 0
            
            if quest_total > 0:
                # Calculate average completion time in hours
                # First try to use time_spent field if available, otherwise calculate from dates
                total_time = 0
                for q in completed_quests:
                    if q.time_spent and q.time_spent > 0:
                        # time_spent is in minutes, convert to hours
                        total_time += q.time_spent
                
                quest_avg_time = round(total_time / quest_total, 2)
            
            # Calculate xperience metrics
            xperience_total = len(completed_xperiences)
            xperience_users = len(set(x.user_id for x in completed_xperiences))
            xperience_avg_time = 0
            
            if xperience_total > 0:
                # Calculate average time spent in minutes
                total_time = sum(x.time_spent for x in completed_xperiences if x.time_spent and x.time_spent > 0)
                xperience_avg_time = round(total_time / xperience_total, 2)
            
            # Calculate program metrics
            program_total = len(completed_programs)
            program_users = len(set(p.user_id for p in completed_programs))
            program_avg_time = 0
            
            if program_total > 0:
                # Calculate average time spent in minutes
                total_time = sum(p.time_spent for p in completed_programs if p.time_spent and p.time_spent > 0)
                program_avg_time = round(total_time / program_total, 2)
            
            return {
                'quest': {
                    'average_time': quest_avg_time,
                    'total_completed': quest_total,
                    'users_completed': quest_users
                },
                'xperience': {
                    'average_time': xperience_avg_time,
                    'total_completed': xperience_total,
                    'users_completed': xperience_users
                },
                'program': {
                    'average_time': program_avg_time,
                    'total_completed': program_total,
                    'users_completed': program_users
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating completion metrics: {str(e)}", exc_info=True)
            return None


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/xapa-learning-time')
class XapaLearningTimeAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """
        Get total learning time metrics for XAPA with period comparison.
        
        Calculates cumulative time spent in quests by analyzing sequential records:
        - For sequential activities within 5 minutes, counts actual time
        - For gaps > 5 minutes, adds exactly 5 minutes
        - Example: 2 mins working, hour gap (+5), 3 mins working, 2 days gap (+5), 2 mins working = 17 mins
        """
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)

        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        start_date = unquote(start_date)
        end_date = unquote(end_date)

        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%SZ')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%SZ')
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)

        # Calculate previous period
        period_length = end_date - start_date
        previous_end = start_date
        previous_start = previous_end - period_length

        # Get metrics for both periods
        current_metrics = self.get_period_metrics(client_id, start_date, end_date)
        previous_metrics = self.get_period_metrics(client_id, previous_start, previous_end)

        return create_response('Successfully retrieved XAPA learning time metrics', data={
            'period': {
                'current': {
                    'start': start_date.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': end_date.strftime('%Y-%m-%d %H:%M:%SZ')
                },
                'previous': {
                    'start': previous_start.strftime('%Y-%m-%d %H:%M:%SZ'),
                    'end': previous_end.strftime('%Y-%m-%d %H:%M:%SZ')
                }
            },
            'metrics': {
                'current': current_metrics['total'],
                'previous': previous_metrics['total'],
                'client_metrics': current_metrics['clients']
            }
        })
    
    def get_period_metrics(self, client_id, start_date, end_date):
        """Calculate learning time metrics for a specific period"""
        try:
            db_session = get_db_session()
            user_filter = db_session.query(User.id).join(UserAssignment, (User.id == UserAssignment.user_id) & (UserAssignment.client_id == client_id)).subquery()

            client_metrics = []
            total_time = 0
            total_users = 0
            total_quests = 0
            completed_quests = 0

            # Build the base query
            query = db_session.query(UserQuest).filter(
                UserQuest.date_created >= start_date,
                UserQuest.date_created < end_date
            )

            if client_id:
                ClientPackageAssociationAlias = aliased(ClientPackageAssociation)
                # Filter quests by client
                client_quests_subquery = db_session.query(Quest.id).distinct().join(
                    XperienceQuestAssociation,
                    XperienceQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == XperienceQuestAssociation.xperience_id,
                    isouter=True
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id,
                    isouter=True
                ).join(
                    ProgramQuestAssociation,
                    ProgramQuestAssociation.quest_id == Quest.id,
                    isouter=True
                ).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == ProgramQuestAssociation.program_id,
                    isouter=True
                ).join(
                    ClientPackageAssociationAlias,
                    ClientPackageAssociationAlias.package_id == PackageProgramAssociation.package_id,
                    isouter=True
                ).filter(
                    (ClientPackageAssociation.client_id == client_id) |
                    (ClientPackageAssociationAlias.client_id == client_id)
                ).subquery()
                
                query = query.filter(UserQuest.quest_id.in_(client_quests_subquery.select()), UserQuest.user_id.in_(user_filter.select()))
                
            # Get all quest activities
            quest_activities = query.order_by(UserQuest.user_id, UserQuest.quest_id, UserQuest.date_created).all()
            
            # Count total and completed quests
            total_quests = len(quest_activities)
            completed_quests = sum(1 for activity in quest_activities if activity.status == 'completed')
            
            # Group activities by user and quest
            user_quest_activities = {}
            for activity in quest_activities:
                user_id = activity.user_id
                quest_id = activity.quest_id
                
                if user_id not in user_quest_activities:
                    user_quest_activities[user_id] = {}
                
                if quest_id not in user_quest_activities[user_id]:
                    user_quest_activities[user_id][quest_id] = []
                    
                user_quest_activities[user_id][quest_id].append(activity)
            
            # Calculate total time spent for each user
            client_total_time = 0
            for user_id, quests in user_quest_activities.items():
                user_time = 0
                for quest_id, activities in quests.items():
                    # Sort activities by date_started to ensure chronological order
                    activities.sort(key=lambda x: x.date_created)
                    quest_time = self.calculate_quest_time(activities)
                    user_time += quest_time
                
                client_total_time += user_time

            unique_users = len(user_quest_activities)
            if unique_users == 0:
                unique_users = 1  # Prevent division by zero

            avg_time_per_user = round(client_total_time / unique_users, 2)
            completion_rate = round((completed_quests / total_quests) * 100, 2) if total_quests > 0 else 0

            client_metrics.append({
                'total_time': client_total_time,
                'unique_users': unique_users,
                'average_time_per_user': avg_time_per_user,
                'total_quests': total_quests,
                'completed_quests': completed_quests,
                'completion_rate': completion_rate
            })

            total_time += client_total_time
            total_users += unique_users

            # Calculate overall averages
            if total_users == 0:
                total_users = 1  # Prevent division by zero

            return {
                'total': {
                    'total_time': total_time,
                    'total_users': total_users,
                    'avg_time_per_user': round(total_time / total_users, 2),
                    'total_quests': total_quests,
                    'completed_quests': completed_quests,
                    'completion_rate': completion_rate
                },
                'clients': client_metrics
            }
        except Exception as e:
            logger.error(f"Error getting period metrics: {str(e)}", exc_info=True)
            return None
    
    def calculate_quest_time(self, activities):
        """
        Calculate time spent on a quest using the 5-minute gap rule:
        - For sequential activities within 5 minutes, counts actual time
        - For gaps > 5 minutes, adds exactly 5 minutes
        - Example: 2 mins working, hour gap (+5), 3 mins working, 2 days gap (+5), 2 mins working = 17 mins
        """
        if not activities or len(activities) == 0:
            return 0
            
        total_time = 0
        
        # Use time_spent field if available for single activities
        if len(activities) == 1 and activities[0].time_spent and activities[0].time_spent > 0:
            return activities[0].time_spent
            
        # For multiple activities, calculate based on sequence
        for i in range(len(activities) - 1):
            current = activities[i]
            next_activity = activities[i + 1]
            
            # Calculate time difference between consecutive activities
            time_diff = (next_activity.date_created - current.date_created).total_seconds()
            
            if time_diff < 300:
                # If less than 5 minutes, count actual time
                total_time += time_diff
            else:
                # If 5 minutes or more, add exactly 5 minutes
                total_time += 300
                
        # Add time for the last activity (assume 5 minutes for completion)
        total_time += 300
        
        return round(total_time)


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/top-participation')
class TopParticipationAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get participation metrics for programs and experiences with period comparison"""
        # Early validation of required parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        client_id = request.args.get('client_id', None)

        if client_id:
            client = g.db_session.query(Client).filter(Client.id == client_id).first()
            if not client:
                return create_response("Client not found", status=404)

        if not start_date or not end_date:
            return create_response("Start date and end date are required", status=400)

        try:
            # Parse and validate dates
            start_date_obj = datetime.datetime.strptime(unquote(start_date), '%Y-%m-%d %H:%M:%SZ')
            end_date_obj = datetime.datetime.strptime(unquote(end_date), '%Y-%m-%d %H:%M:%SZ')
            
            if end_date_obj <= start_date_obj:
                return create_response("End date must be after start date", status=400)
            
        except ValueError:
            return create_response("Invalid date format. Use format (e.g. 2024-02-18 00:00:00Z)", status=400)

        # Calculate previous period
        period_length = end_date_obj - start_date_obj
        previous_end = start_date_obj
        previous_start = previous_end - period_length

        try:
            # Get metrics for both periods
            current_metrics = self._get_participation_metrics(client_id, start_date_obj, end_date_obj) or {
                'total_participation': 0,
                'program_participation': 0,
                'xperience_participation': 0,
                'top_programs': [],
                'top_xperiences': []
            }
            previous_metrics = self._get_participation_metrics(client_id, previous_start, previous_end) or {
                'total_participation': 0,
                'program_participation': 0,
                'xperience_participation': 0,
                'top_programs': [],
                'top_xperiences': []
            }

            return create_response('Successfully retrieved participation metrics', data={
                'period': {
                    'current': {
                        'start': start_date_obj.strftime('%Y-%m-%d %H:%M:%SZ'),
                        'end': end_date_obj.strftime('%Y-%m-%d %H:%M:%SZ')
                    },
                    'previous': {
                        'start': previous_start.strftime('%Y-%m-%d %H:%M:%SZ'),
                        'end': previous_end.strftime('%Y-%m-%d %H:%M:%SZ')
                    }
                },
                'metrics': {
                    'current': current_metrics,
                    'previous': previous_metrics
                }
            })

        except Exception as e:
            logger.error(f"Error in top participation analysis: {str(e)}", exc_info=True)
            return create_response("Internal server error", status=500)

    def _get_participation_metrics(self, client_id, start_date, end_date):
        """Calculate participation metrics for programs and experiences"""
        from sqlalchemy import func, distinct

        try:
            db_session = get_db_session()
            user_filter = db_session.query(User.id).join(UserAssignment, (User.id == UserAssignment.user_id) & (UserAssignment.client_id == client_id)).subquery()
            
            # Get unique participants for programs and experiences
            program_users_query = db_session.query(UserProgram.user_id).filter(
                UserProgram.date_created >= start_date,
                UserProgram.date_created < end_date
            )

            if client_id:
                # Filter programs by client
                client_programs_subquery = db_session.query(Program.id).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == Program.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageProgramAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                program_users_query = program_users_query.filter(UserProgram.program_id.in_(client_programs_subquery.select()), UserProgram.user_id.in_(user_filter.select()))

            # Get unique participants for programs and experiences
            program_users = set(
                user_id for (user_id,) in program_users_query.distinct()
            )

            xperience_users_query = db_session.query(UserXperience.user_id).filter(
                UserXperience.date_created >= start_date,
                UserXperience.date_created < end_date
            )

            if client_id:
                # Filter experiences by client
                client_xperiences_subquery = db_session.query(Xperience.id).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == Xperience.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                xperience_users_query = xperience_users_query.filter(UserXperience.xperience_id.in_(client_xperiences_subquery.select()), UserXperience.user_id.in_(user_filter.select()))

            xperience_users = set(
                user_id for (user_id,) in xperience_users_query.distinct()
            )

            # Calculate total unique participants
            total_participation = len(program_users.union(xperience_users))

            # Get program participation metrics
            program_participation = len(program_users)

            # Get experience participation metrics
            xperience_participation = len(xperience_users)

            # Get top 10 programs by participation
            top_programs_query = db_session.query(
                UserProgram.program_id,
                Program.name,
                func.count(distinct(UserProgram.user_id)).label('user_count')
            ).join(
                Program, 
                Program.id == UserProgram.program_id
            ).filter(
                UserProgram.date_created >= start_date,
                UserProgram.date_created < end_date
            )

            # Filter by client's packages and users if client_id is provided
            if client_id:
                # Filter programs by client's packages
                client_programs_subquery = db_session.query(Program.id).join(
                    PackageProgramAssociation,
                    PackageProgramAssociation.program_id == Program.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageProgramAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                # Apply both program and user filters
                top_programs_query = top_programs_query.filter(
                    Program.id.in_(client_programs_subquery.select()),
                    UserProgram.user_id.in_(user_filter.select())
                )
            
            top_programs = top_programs_query.group_by(
                UserProgram.program_id,
                Program.name
            ).order_by(
                func.count(distinct(UserProgram.user_id)).desc()
            ).limit(10).all()

            # Get top 10 experiences by participation
            top_xperiences_query = db_session.query(
                UserXperience.xperience_id,
                Xperience.name,
                func.count(distinct(UserXperience.user_id)).label('user_count')
            ).join(
                Xperience, 
                Xperience.id == UserXperience.xperience_id
            ).filter(
                UserXperience.date_created >= start_date,
                UserXperience.date_created < end_date
            )

            if client_id:
                # Filter by client's packages
                client_xperiences_subquery = db_session.query(Xperience.id).join(
                    PackageXperienceAssociation,
                    PackageXperienceAssociation.xperience_id == Xperience.id
                ).join(
                    ClientPackageAssociation,
                    ClientPackageAssociation.package_id == PackageXperienceAssociation.package_id
                ).filter(
                    ClientPackageAssociation.client_id == client_id
                ).subquery()
                
                top_xperiences_query = top_xperiences_query.filter(
                    Xperience.id.in_(client_xperiences_subquery.select()),
                    UserXperience.user_id.in_(user_filter.select())
                )
            
            top_xperiences = top_xperiences_query.group_by(
                UserXperience.xperience_id,
                Xperience.name
            ).order_by(
                func.count(distinct(UserXperience.user_id)).desc()
            ).limit(10).all()

            return {
                'total_participation': total_participation,
                'program_participation': program_participation,
                'xperience_participation': xperience_participation,
                'top_programs': [
                    {
                        'id': str(program.program_id),
                        'name': str(program.name),
                        'participation_count': program.user_count
                    } for program in top_programs
                ],
                'top_xperiences': [
                    {
                        'id': str(xperience.xperience_id),
                        'name': str(xperience.name),
                        'participation_count': xperience.user_count
                    } for xperience in top_xperiences
                ]
            }

        except Exception as e:
            logger.error(f"Error calculating participation metrics: {str(e)}", exc_info=True)
            return None


posthog_query_model = cms_analyses_api.model('posthog_query_model', {
    'name': fields.String(required=True),
    'interval': fields.Integer(required=True),
    'query': fields.String(required=True)
})

@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/posthog')
@cms_analyses_api.route('/posthog/<string:name>/<int:interval>', methods=['DELETE'])
class PostHogAnalytics(Resource):
    @cache.cached(timeout=3600, key_prefix=lambda: f'{g.tenant_id}:posthog:{request.json["name"]}:{request.json["interval"]}')
    @cms_analyses_api.expect(posthog_query_model)
    def post(self):
        """Execute a custom SQL query on PostHog data"""
        try:
            # Get the SQL query from request body
            data = request.json
            posthog_query = data['query']

            # Execute the SQL query using PostHog's SQL API
            result = PosthogQuery().execute_posthog_sql(posthog_query)

            return create_response("Query executed successfully", data=result)

        except Exception as e:
            logger.error(f"Error executing PostHog SQL query: {str(e)}", exc_info=True)
        return create_response(f"Error executing PostHog SQL query: {str(e)}", status=500)

    def delete(self, name, interval):
        """Clean cache for PostHog analytics queries
        Args:
            name (str): Query name or 'all' to clear all cache
            interval (int): Time interval for the query
        """
        try:
            if not name:
                return create_response("name parameter is required", status=400)

            # Handle clearing all cache
            if name == 'all':
                cache_key = f'/posthog/{g.tenant_id}'
                cache.delete(cache_key)
                return create_response("All PostHog cache cleared successfully")

            # Handle specific cache clear
            if not interval:
                return create_response("interval parameter is required for specific cache clear", status=400)
            
            cache_key = f'/posthog/{g.tenant_id}/{name}/{interval}'
            cache.delete(cache_key)
            return create_response(f"Cache cleared for query '{name}' with interval {interval}")

        except Exception as e:
            logger.error("Error cleaning PostHog cache", exc_info=True)
            return create_response("Failed to clean cache", status=500)


@cms_analyses_api.doc(security='bearer')
@cms_analyses_api.route('/streaks')
class StreakAnalysis(Resource):
    @cms_analyses_api.expect(parser)
    @check_user_permission('super_admin')
    def get(self):
        """Get user streak analysis data for super admin analytics"""
        try:
            # Get client_id from request parameters
            client_id = request.args.get('client_id', None)
            user_filter = g.db_session.query(User.id).join(UserAssignment, (User.id == UserAssignment.user_id) & (UserAssignment.client_id == client_id)).subquery()

            if client_id:
                client = g.db_session.query(Client).filter(Client.id == client_id).first()
                if not client:
                    return create_response("Client not found", status=404)
            
            # Query all users with their streak counts and calculate metrics in SQL
            from sqlalchemy import func, case, distinct

            # Query 1: Basic metrics (total users and streak sums)
            metrics_query = g.db_session.query(
                func.count(distinct(User.id)).label('total_users'),
                func.count(distinct(case(
                    (UserStats.streaks_count > 0, User.id),
                    else_=None
                ))).label('total_active_streaks'),
                func.count(distinct(case(
                    (UserStats.streaks_count >= 7, User.id),
                    else_=None
                ))).label('users_over_7_days'),
                func.sum(UserStats.streaks_count).label('total_streak_days')
            ).select_from(User, UserStats).outerjoin(
                UserStats,
                User.id == UserStats.user_id
            )
            
            if client_id:
                metrics_query = metrics_query.filter(User.id.in_(user_filter.select()))
            
            metrics = metrics_query.first()
            total_users = metrics.total_users or 0
            total_active_streaks = metrics.total_active_streaks or 0
            users_over_7_days = metrics.users_over_7_days or 0
            total_streak_days = metrics.total_streak_days or 0

            # Query 2: Distribution query
            distribution_query = g.db_session.query(
                case(
                    (UserStats.streaks_count >= 30, '30+'),
                    (UserStats.streaks_count >= 16, '16-29'),
                    (UserStats.streaks_count >= 8, '8-15'),
                    (UserStats.streaks_count >= 4, '4-7'),
                    (UserStats.streaks_count >= 1, '1-3'),
                    else_='0'
                ).label('streak_group'),
                func.count().label('count')
            ).select_from(User, UserStats).outerjoin(
                UserStats,
                User.id == UserStats.user_id
            ).group_by('streak_group')

            if client_id:
                distribution_query = distribution_query.filter(User.id.in_(user_filter.select()))

            # Initialize streak groups
            streak_groups = {
                '30+': 0,
                '16-29': 0,
                '8-15': 0,
                '4-7': 0,
                '1-3': 0,
                '0': 0
            }

            for row in distribution_query:
                streak_groups[row.streak_group] = row.count

            # Query 3: Longest streak user
            longest_streak_query = g.db_session.query(
                User,
                UserStats.streaks_count
            ).join(UserStats, User.id == UserStats.user_id)

            if client_id:
                longest_streak_query = longest_streak_query.filter(User.id.in_(user_filter.select()))

            longest_streak_query = longest_streak_query.order_by(
                UserStats.streaks_count.desc(),
                User.first_name,
                User.last_name
            ).first()

            if longest_streak_query:
                user, longest_streak = longest_streak_query
                longest_streak_user = user.to_dict(['id', 'first_name', 'last_name', 'image', 'company'])
            else:
                longest_streak = 0
                longest_streak_user = None

            # Calculate percentages for streak groups
            streak_distribution = []
            for group, count in streak_groups.items():
                streak_distribution.append({
                    'group': group,
                    'count': count,
                    'percentage': round((count / total_users) * 100, 2) if total_users > 0 else 0
                })

            # Calculate average streak (only for users with active streaks)
            avg_streak = round(total_streak_days / total_active_streaks, 2) if total_active_streaks > 0 else 0

            # Calculate percentage of users with streak over 7 days
            percent_over_7_days = round((users_over_7_days / total_active_streaks) * 100, 2) if total_active_streaks > 0 else 0

            # Prepare response data
            analysis = {
                'streak_distribution': streak_distribution,
                'longest_streak': {
                    'days': longest_streak,
                    'user': longest_streak_user
                },
                'average_streak': {
                    'days': avg_streak,
                    'total_streak_days': total_streak_days,
                    'total_active_users': total_active_streaks
                },
                'users_over_7_days': {
                    'percentage': percent_over_7_days,
                    'count': users_over_7_days,
                    'total_active_users': total_active_streaks
                }
            }

            return create_response("Streak analysis retrieved successfully", data=analysis)

        except Exception as e:
            logger.error(f"Error calculating streak metrics: {str(e)}", exc_info=True)
            return create_response(f"Error calculating streak metrics: {str(e)}", status=500)
