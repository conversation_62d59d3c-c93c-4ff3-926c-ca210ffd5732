import logging

from flask import g, request
from flask_restx import Namespace, Resource, fields

from clientmodels import EntitlementGroup, User, Program, Xperience
from api.common.helper import create_response


logger = logging.getLogger(__name__)

cms_entitlement_groups_api = Namespace('api_cms_entitlement_group', description='Entitlement Groups API')

entitlement_group_model = cms_entitlement_groups_api.model('EntitlementGroup', {
    'name': fields.String(description='Entitlement Group Name'),
    'description': fields.String(description='Entitlement Group Description'),
    'entitlement_group_type': fields.String(description='Entitlement Group Type'),
    'directory_mapping_key': fields.String(description='Directory Mapping Key'),
    'directory_mapping_value': fields.String(description='Directory Mapping Value'),
    'user_ids': fields.List(fields.String(description='User ID')),
    'program_ids': fields.List(fields.String(description='Program ID')),
    'xperience_ids': fields.List(fields.String(description='Xperience ID')),
})

@cms_entitlement_groups_api.doc(security='bearer')
@cms_entitlement_groups_api.route('/', methods=['POST'])
@cms_entitlement_groups_api.route('/<string:id>', methods=['GET', 'PUT', 'DELETE'])
class EntitlementGroupItem(Resource):
    @cms_entitlement_groups_api.expect(entitlement_group_model)
    def post(self):
        """Create a new Entitlement Group"""
        data = request.json

        name = data.get('name', '').strip()
        
        ## check if entitlment group name is empty
        if not name:
            return create_response("Client name is required", status=400)
        
        ## check if entitlement group name already exists
        entitlement_group = g.client_db_session.query(EntitlementGroup).filter_by(name=name).first()
        if entitlement_group:
            return create_response("Entitlement group name already exists", status=400)
        
        description = data.get('description', '').strip()
        entitlement_group_type = data.get('entitlement_group_type', '').strip()
        directory_mapping_key = data.get('directory_mapping_key', '').strip()
        directory_mapping_value = data.get('directory_mapping_value', '').strip()
        user_ids = data.get('user_ids', [])
        program_ids = data.get('program_ids', [])
        xperience_ids = data.get('xperience_ids', [])

        entitlement_group = EntitlementGroup(
            name=name,
            description=description,
            entitlement_group_type=entitlement_group_type,
            directory_mapping_key=directory_mapping_key,
            directory_mapping_value=directory_mapping_value
        )

        try:
            # Get the related objects
            if user_ids:
                user_objects = g.client_db_session.query(User).filter(User.id.in_(user_ids)).all()
                entitlement_group.users = user_objects
            
            if program_ids:
                program_objects = g.client_db_session.query(Program).filter(Program.id.in_(program_ids)).all()
                entitlement_group.programs = program_objects
            
            if xperience_ids:
                xperience_objects = g.client_db_session.query(Xperience).filter(Xperience.id.in_(xperience_ids)).all()
                entitlement_group.xperiences = xperience_objects

            g.client_db_session.add(entitlement_group)
            g.client_db_session.commit()
            
        except Exception as e:
            g.client_db_session.rollback()
            logger.error(f"Error creating entitlement group: {str(e)}")
            return create_response(f"Error creating entitlement group: {str(e)}", status=500)
        
        return create_response("Entitlement group created successfully", status=201)

    
    def get(self, id):
        """Get an Entitlement Group by ID"""
        entitlement_group = g.client_db_session.query(EntitlementGroup).filter_by(id=id).first()
        if not entitlement_group:
            return create_response("Entitlement group not found", status=404)
    
        data = entitlement_group.to_dict()
        
        # Get all assignments in a single query using joins
        data.update({
            'users': [user.to_dict() for user in entitlement_group.users],
            'programs': [program.to_dict() for program in entitlement_group.programs],
            'xperiences': [xp.to_dict() for xp in entitlement_group.xperiences]
        })
        
        return create_response('Entitlement group received', data=data)

    @cms_entitlement_groups_api.expect(entitlement_group_model)
    def put(self, id):
        """Update an Entitlement Group"""
        data = request.json
        entitlement_group = g.client_db_session.query(EntitlementGroup).filter_by(id=id).first()
        if not entitlement_group:
            return create_response("Entitlement group not found", status=404)

        description = data.get('description', '').strip()
        entitlement_group_type = data.get('entitlement_group_type', '').strip()
        directory_mapping_key = data.get('directory_mapping_key', '').strip()
        directory_mapping_value = data.get('directory_mapping_value', '').strip()
        user_ids = data.get('user_ids', [])
        program_ids = data.get('program_ids', [])
        xperience_ids = data.get('xperience_ids', [])

        try:
            if description:
                entitlement_group.description = description
            if entitlement_group_type:
                entitlement_group.entitlement_group_type = entitlement_group_type
            if directory_mapping_key:
                entitlement_group.directory_mapping_key = directory_mapping_key
            if directory_mapping_value:
                entitlement_group.directory_mapping_value = directory_mapping_value

            # Update relationships
            users = g.client_db_session.query(User).filter(User.id.in_(user_ids)).all()
            entitlement_group.users = users

            programs = g.client_db_session.query(Program).filter(Program.id.in_(program_ids)).all()
            entitlement_group.programs = programs

            xperiences = g.client_db_session.query(Xperience).filter(Xperience.id.in_(xperience_ids)).all()
            entitlement_group.xperiences = xperiences

            g.client_db_session.commit()
            return create_response("Entitlement group updated successfully", status=200)

        except Exception as e:
            g.client_db_session.rollback()
            logger.error(f"Error updating entitlement group: {str(e)}")
            return create_response(f"Error updating entitlement group: {str(e)}", status=500)

    def delete(self, id):
        """Delete an Entitlement Group"""
        try:
            entitlement_group = g.client_db_session.query(EntitlementGroup).filter_by(id=id).first()
            if not entitlement_group:
                return create_response("Entitlement group not found", status=404)

            # Soft delete the entitlement group
            entitlement_group.is_deleted = True
            g.client_db_session.commit()
            return create_response("Entitlement group deleted successfully", status=200)

        except Exception as e:
            g.client_db_session.rollback()
            logger.error(f"Error deleting entitlement group: {str(e)}")
            return create_response(f"Error deleting entitlement group: {str(e)}", status=500)


entitlement_group_parser = cms_entitlement_groups_api.parser()
entitlement_group_parser.add_argument('page', type=int, location='args', default=1)
entitlement_group_parser.add_argument('limit', type=int, location='args', default=10)
entitlement_group_parser.add_argument('search', type=str, location='args', default='')
entitlement_group_parser.add_argument('sort', type=str, location='args', default='')

@cms_entitlement_groups_api.doc(security='bearer')
@cms_entitlement_groups_api.route('/list', methods=['GET'])
class EntitlementGroupList(Resource):
    @cms_entitlement_groups_api.expect(entitlement_group_parser)
    def get(self):
        """Get a list of all Entitlement Groups"""
        args = entitlement_group_parser.parse_args()
        page = args.get('page', 1)
        limit = args.get('limit', 10)
        search = args.get('search', '')
        sort = args.get('sort', '')
        offset = (page - 1) * limit

        try:
            # Build base query with eager loading of relationships
            query = g.client_db_session.query(EntitlementGroup).filter_by(is_deleted=False)
            
            if search:
                query = query.filter(EntitlementGroup.name.ilike(f'%{search}%'))

            if sort:
                if sort.startswith('-'):
                    query = query.order_by(getattr(EntitlementGroup, sort[1:]).desc())
                else:
                    query = query.order_by(getattr(EntitlementGroup, sort))
            else:
                query = query.order_by(EntitlementGroup.name)

            # Get total count before pagination
            total_count = query.count()

            # Apply pagination and get results
            entitlement_groups = query.offset(offset).limit(limit).all()

            # Process results efficiently
            result = [group.to_dict() for group in entitlement_groups]

            return create_response(
                'Entitlement Groups received', 
                data=result, 
                status=200, 
                page=page, 
                limit=limit, 
                total=total_count
            )

        except Exception as e:
            logger.error(f"Error retrieving entitlement groups: {str(e)}")
            return create_response(f"Error retrieving entitlement groups: {str(e)}", status=500)
