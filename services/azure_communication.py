from flask import jsonify
from bs4 import BeautifulSoup
from azure.communication.email import <PERSON>ail<PERSON><PERSON>

from services.azure_key_vault import AzureKeyVault


class AzureCommunicationEmail:
    def __init__(self):
        connection_string = AzureKeyVault().get_secret('azure-communication-connection-string')

        self.email_client = EmailClient.from_connection_string(connection_string)
        self.sender = '<EMAIL>'
        

    def send_email(self, to_email, subject, content, **kwargs):
        to_emails = kwargs.get('to_emails', None)
        sender = kwargs.get('sender', None)
        if sender:
            self.sender = sender
        
        recipients = []
        if to_emails:
            for email in to_emails:
                recipients.append({
                    'address': email,
                    'displayName': email
                })

        if to_email:
            recipients.append({
                'address': to_email,
                'displayName': to_email
            })

        ## remove duplicates
        recipients = [dict(t) for t in {tuple(d.items()) for d in recipients}]

        if not recipients or not subject or not content:
            return jsonify({'message': 'to_email, subject, and content are required'}), 400

        

        if self._is_html(content):
            plain_text_content = BeautifulSoup(content, 'html.parser').get_text()
            email_content = {
                'subject': subject,
                'plainText': plain_text_content,
                'html': content
            }
        else:
            email_content = {
                'subject': subject,
                'plainText': content,
                'html': f'<html><head></head><body>{content}</body></html>'
            }

        email_message = {
            'content': email_content,
            'recipients': {
                'to': recipients
            },
            'senderAddress': self.sender
        }

        try:
            response = self.email_client.begin_send(email_message)
            return jsonify({'message': 'Email sent successfully'}), 200
        except Exception as e:
            print(e)
            return jsonify({'message': str(e)}), 500

    def _is_html(self, text):
        soup = BeautifulSoup(text, 'html.parser')
        return bool(soup.find())

