import requests
import os
import time
from typing import Dict, Any

class PosthogQuery:
    # List of API keys to rotate through when hitting rate limits
    API_KEYS = [
        'phx_b2gDvfPKzwBKdSHFCjgU4pr2ZBx5KTXrff8GqADYFsoCkgK',
        'phx_DwktMDeFPPTI7FgVxMNbhRREdZa6NIHlE3MinwWSJu17c5S',
        'phx_OastZnMr2SxM1x3CBkxnPLSYjblojlnNnlkgin0Kg5J24sM',
        'phx_er9Yk7bEvUx63Hlauiul6W7Pp1EMlTp5YMAdzMIbEpBx7wl',
        'phx_M0mauDz2LiQAp534oaTllnaIxlTO4IEGBz0C6fA4CKWzMyg'
    ]

    def __init__(self):
        self.host = os.environ.get('POSTHOG_HOST', 'https://us.posthog.com')
        self.current_key_index = 0
        self.max_retries = 5
        self.retry_delay = 1  # seconds
    
    def get_next_api_key(self) -> str:
        """Rotate to the next available API key"""
        valid_keys = [key for key in self.API_KEYS if key]
        if not valid_keys:
            raise ValueError("No valid PostHog API keys available")
        
        self.current_key_index = (self.current_key_index + 1) % len(valid_keys)
        return valid_keys[self.current_key_index]

    def execute_posthog_sql(self, sql: str) -> Dict[str, Any]:
        """Execute a HogQL query with automatic retry and API key rotation on rate limits
        
        Args:
            sql: The HogQL query to execute
            
        Returns:
            Dict containing query results and columns
            
        Raises:
            requests.exceptions.RequestException: If all retries fail
            ValueError: If no valid API keys are available
        """
        retries = 0
        last_error = None

        while retries < self.max_retries:
            try:
                url = f"{self.host}/api/projects/@current/query"
                headers = {
                    "Authorization": f"Bearer {self.API_KEYS[self.current_key_index]}",
                    "Content-Type": "application/json"
                }
                payload = {
                    "query": {
                        "kind": "HogQLQuery",
                        "query": sql
                    }
                }
                
                response = requests.post(url, headers=headers, json=payload)
                
                if response.status_code != 200:
                    retries += 1
                    if retries == self.max_retries:
                        break

                    # Rate limit: try next API key
                    if response.status_code == 429:
                        try:
                            self.get_next_api_key()
                        except ValueError as e:
                            raise e
                    
                    # Wait before retry
                    time.sleep(self.retry_delay)
                    continue
                
                result = response.json()
                return {
                    'results': result['results'],
                    'columns': result['columns']
                }
                
            except requests.exceptions.RequestException as e:
                last_error = e
                retries += 1
                if retries == self.max_retries:
                    break
                
                # For connection errors, try next API key
                try:
                    self.get_next_api_key()
                except ValueError as e:
                    raise e
                
                time.sleep(self.retry_delay)
                continue
        
        # If we've exhausted all retries
        raise last_error or ValueError("Failed to execute query after all retries")

if __name__ == "__main__":
    sql = '''
WITH
    toDateTime('2025-04-10 00:00:00') AS base_date,
    toDateTime('2025-04-16 23:59:59') AS end_date,
    '067ef523-d58f-7948-8000-d343c83dacb3' AS group_id
SELECT
    toStartOfWeek(first_week) AS cohort_week,
    interval AS weeks_since_first,
    count(DISTINCT person_id) AS retained_users
FROM (
    SELECT
        person_id,
        toStartOfWeek(min(timestamp)) AS first_week,
        dateDiff('week', toStartOfWeek(min(timestamp)), toStartOfWeek(timestamp)) AS interval
    FROM events
    WHERE
        event = '$screen'
        AND timestamp >= base_date
        AND timestamp <= end_date
        AND properties['$group_0'] = group_id
    GROUP BY person_id, toStartOfWeek(timestamp)
)
GROUP BY cohort_week, interval
ORDER BY cohort_week ASC, interval ASC
LIMIT 10000
    '''
    result = PosthogQuery().execute_posthog_sql(sql)
    print(result['results'])
