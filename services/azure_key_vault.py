import os
import logging
from typing import Optional

from azure.identity import EnvironmentCredential, DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from azure.core.exceptions import ClientAuthenticationError, ResourceNotFoundError

logger = logging.getLogger(__name__)

class AzureKeyVault:
    def __init__(self):
        self.client = None
        
        self.key_vault_name = os.environ.get("KEY_VAULT_NAME")
        if not self.key_vault_name:
            logger.warning("KEY_VAULT_NAME environment variable not set")
            return
            
        self.key_vault_uri = f"https://{self.key_vault_name}.vault.azure.net"
        
        # Try EnvironmentCredential first, then fall back to DefaultAzureCredential
        try:
            self.credential = EnvironmentCredential()
            self.client = SecretClient(vault_url=self.key_vault_uri, credential=self.credential)
        except ClientAuthenticationError:
            logger.info("EnvironmentCredential failed, trying DefaultAzureCredential")
            self.credential = DefaultAzureCredential()
            self.client = SecretClient(vault_url=self.key_vault_uri, credential=self.credential)
            

    def get_secret(self, secret_name: str) -> Optional[str]:
        """
        Get a secret from Azure Key Vault or local environment variable.
        
        Args:
            secret_name: Name of the secret to retrieve
            
        Returns:
            Secret value as string or None if not found
        """
        # Try to get from environment variable first (for local development)
        env_var_name = f"AZURE_SECRET_{secret_name.replace('-', '_').upper()}"
        env_value = os.environ.get(env_var_name)
        
        # If we're in local debug mode or the client failed to initialize, use env vars
        if self.client is None:
            if env_value:
                logger.debug(f"Using environment variable {env_var_name} for secret {secret_name}")
                return env_value
            else:
                logger.warning(f"Secret {secret_name} not found in environment variables")
                return None
        
        # Otherwise try to get from Azure Key Vault
        try:
            secret = self.client.get_secret(secret_name)
            return secret.value
        except ResourceNotFoundError:
            logger.warning(f"Secret {secret_name} not found in Azure Key Vault")
            # Fall back to environment variable if Azure Key Vault lookup fails
            if env_value:
                logger.debug(f"Using environment variable {env_var_name} as fallback for secret {secret_name}")
                return env_value
            return None
        except Exception as e:
            logger.error(f"Error retrieving secret {secret_name} from Azure Key Vault: {str(e)}")
            # Fall back to environment variable on error
            if env_value:
                logger.debug(f"Using environment variable {env_var_name} as fallback for secret {secret_name}")
                return env_value
            return None
