import datetime
import json
import time

import requests
from flask import session, g

from api.common.file import FileHandler
from app import storage
from clientmodels import UserGraphToken
from services.azure_key_vault import AzureKeyVault

get_secret = AzureKeyVault().get_secret

class GraphAPI:
    def __init__(self, user_id, tenant_id_key):
        ## get user token from graph token table
        graph_token = (
            g.db_session.query(UserGraphToken)
            .filter(UserGraphToken.user_id == user_id)
            .first()
        )

        if not graph_token:
            return None

        graphapi_token = {
            "access_token": graph_token.access_token,
            "refresh_token": graph_token.refresh_token,
            "date_expired": graph_token.date_expired
        }

        try:
            sso_setting_key = "sso-" + tenant_id_key + "-settings"
            sso_setting = get_secret(sso_setting_key)
        except Exception as e:
            ## if sso setting is not found, try to load from default
            sso_setting_key = "sso-global-microsoft-settings"
            sso_setting = get_secret(sso_setting_key)

        try:
            sso_setting = json.loads(sso_setting)
            self.client_id = sso_setting.get("client_id")
            self.client_secret = sso_setting.get("client_secret")
            self.tenant_id = sso_setting.get("tenant_id")
        except Exception as e:
            print(e)
            return None
        
        self.access_token = self.get_access_token(graphapi_token)

    def obtain_access_token(self):
        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        payload = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default'
        }
        response = requests.post(url, data=payload)
        # response.raise_for_status()  # raise exception if invalid response
        data = response.json()
        if 'error' in data:
            return None
        else:
            graphapi_token = {}
            graphapi_token['access_token'] = data.get('access_token')
            graphapi_token['expires_in'] = time.time() + int(data.get('expires_in'))

            session['graphapi_token'] = json.dumps(graphapi_token)
            self.access_token = response.json().get('access_token')
            return self.access_token
        
    def refresh_access_token(self, refresh_token):
        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        payload = {
            'grant_type': 'refresh_token',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'refresh_token': refresh_token,
            'scope': 'https://graph.microsoft.com/.default'
        }
        response = requests.post(url, data=payload)
        # response.raise_for_status()  # raise exception if invalid response
        data = response.json()
        if 'error' in data:
            return None
        else:
            graphapi_token = {}
            graphapi_token['access_token'] = data.get('access_token')
            graphapi_token['refresh_token'] = data.get('refresh_token')
            graphapi_token['expires_in'] = data.get('expires_in')

            session['graphapi_token'] = json.dumps(graphapi_token)
            self.access_token = response.json().get('access_token')
            return self.access_token
        
    def get_access_token(self, graphapi_token):
        access_token = None
        if graphapi_token:
            expire_time = graphapi_token.get('date_expired', 0)
            if 'access_token' in graphapi_token and datetime.datetime.now() <= expire_time:
                print("token is not expired")
                access_token = graphapi_token['access_token']
            elif 'refresh_token' in graphapi_token:
                print("refresh token")
                access_token = self.refresh_access_token(graphapi_token["refresh_token"])
            else:
                return None
        else:
            return None
        
        # print(access_token)
        return access_token
        
    def make_request(self, method, url, **kwargs):
        headers = kwargs.get('headers', {})
        headers['Authorization'] = f'Bearer {self.access_token}'
        headers['ConsistencyLevel'] = "eventual"
        kwargs['headers'] = headers
        # print(headers)
        return requests.request(method, url, **kwargs)
    

    def get_users(self, filter_query=None, select_query=None, expand_query=None, top_query=None, skip_query=None, orderby_query=None, count_query=None, search_query=None, format_query=None, custom_query=None, options=None, page=1):
        users = []
        custom_query = custom_query if custom_query else []
        total = 0

        url = 'https://graph.microsoft.com/v1.0/users?$count=true&$select=userPrincipalName,mail,givenName,surname,department,jobTitle,businessPhones,officeLocation,mobilePhone,manager,directReports,companyName'
        if filter_query:
            url += f'?$filter={filter_query}' if '?' not in url else f'&$filter={filter_query}'

        if select_query:
            url += f'?$select={select_query}' if '?' not in url else f'&$select={select_query}'

        if expand_query:
            url += f'?$expand={expand_query}' if '?' not in url else f'&$expand={expand_query}'

        if top_query:
            url += f'?$top={top_query}' if '?' not in url else f'&$top={top_query}'

        # if skip_query:
        #     url += f'?$skip={skip_query}' if '?' not in url else f'&$skip={skip_query}'
        
        if orderby_query:
            url += f'?$orderby={orderby_query}' if '?' not in url else f'&$orderby={orderby_query}'

        if count_query:
            url += f'?$count={count_query}' if '?' not in url else f'&$count={count_query}'

        if search_query:
            url += f'?$search={search_query}' if '?' not in url else f'&$search={search_query}'

        if format_query:
            url += f'?$format={format_query}' if '?' not in url else f'&$format={format_query}'

        print(url)

        if page > 1:
            api_page = 1
            while api_page < page:
                response = self.make_request('GET', url)
                data = response.json()

                # Use @odata.nextLink to fetch the next page
                url = data.get('@odata.nextLink', None)
                print(url)

                ## save total count to session
                page_total = response.json().get("@odata.count", 0)
                if page_total:
                    total = page_total

                if not url:
                    break

                api_page += 1

        if url:
            response = self.make_request('GET', url)
        else:
            data = {
                "users": [],
                "total": total
            }
            return data

        if response.status_code == 200:
            graph_users = response.json().get("value", [])
            
            for graph_user in graph_users:
                find_user = {
                    "email": graph_user['userPrincipalName'],
                    "last_name": graph_user.get('surname', ''),
                    "first_name": graph_user.get('givenName', ''),
                    "department": graph_user.get('department', ''),
                    "title": graph_user.get('jobTitle', ''),
                    "phone": graph_user.get('businessPhones', [''])[0] if graph_user.get('businessPhones', ['']) else '',
                    "office_location": graph_user.get('officeLocation', ''),
                    "mobile_number": graph_user.get('mobilePhone', ''),
                    "manager": graph_user.get('manager', {}).get('userPrincipalName', ''),
                    "direct_reports": graph_user.get('directReports', []),
                    "company": graph_user.get('companyName', ''),
                }

                ## if a value is null, return empty string
                for key, value in find_user.items():
                    if value is None:
                        find_user[key] = ''

                if custom_query:
                    user = {}
                    for query in custom_query:
                        user[query] = find_user.get(query, "")
                else:
                    user = find_user

                users.append(user)
        else:
            print(response.status_code)
        
        page_total = response.json().get("@odata.count", 0)
        if page_total:
            total = page_total

        data = {
            "users": users,
            "total": total
        }
        return data


    def init_user_image(self, user):
        try:
            if not user.image:
                response = self.make_request('GET', f'https://graph.microsoft.com/v1.0/users/{user.email}/photo/$value')
                if response.status_code == 200:
                    image = response.content
                    image_type = response.headers['Content-Type']
                    image_name = f"{user.id}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
                    image_extension = FileHandler.MIME_TO_EXTENSION.get(image_type, '')
                    image_name += image_extension

                    
                    folder_name = f"graph/{user.id}/profile_images"
                    new_image_name = storage.upload_file(folder_name, image_name, image)
                    new_file_name = folder_name + '/' + new_image_name
                    print("upload user image to storage")
                    return new_file_name
            else:
                print("find user image in database")
            
            return None

        except Exception as e:
            print(e)
            return None
    
    def init_user_profile(self, user):
        try:
            response = self.make_request('GET', f'https://graph.microsoft.com/v1.0/users/{user.email}?$select=userPrincipalName,mail,givenName,surname,department,jobTitle,businessPhones,officeLocation,mobilePhone,manager,directReports, companyName')
            if response.status_code == 200:
                user_profile = response.json()
                user.first_name = user_profile.get('givenName', '')
                user.last_name = user_profile.get('surname', '')
                user.department = user_profile.get('department', '')
                user.title = user_profile.get('jobTitle', '')
                user.phone = user_profile.get('businessPhones', [''])[0] if user_profile.get('businessPhones', ['']) else ''
                # user.bio = user_profile.get('aboutMe', '')
                user.image = self.init_user_image(user) if not user.image else user.image
                if user_profile.get('companyName'):
                    user.company = user_profile.get('companyName')
  
                print("update user profile")
                g.db_session.add(user)
                g.db_session.commit()

            return user
        except Exception as e:
            print(e)
            return user
        