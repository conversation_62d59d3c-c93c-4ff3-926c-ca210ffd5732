from azure.storage.queue import QueueServiceClient

from services.azure_key_vault import AzureKeyVault


class AzureQueue:
    def __init__(self):
        self.connection_string = AzureKeyVault().get_secret("azure-storage-connection-string")
        self.queue_service_client = QueueServiceClient.from_connection_string(self.connection_string)
    
    def send_message(self, message, queue_name, visibility_timeout=0):
        queue_client = self.queue_service_client.get_queue_client(queue_name)
        message = queue_client.send_message(message, visibility_timeout=visibility_timeout)
        return message

    def get_message_by_id(self, queue_name, message_id):
        queue_client = self.queue_service_client.get_queue_client(queue_name)
        messages = queue_client.receive_messages()
        for message in messages:
            if message.id == message_id:
                return message.content
        return None

    def delete_message_by_id(self, queue_name, message_id):
        queue_client = self.queue_service_client.get_queue_client(queue_name)
        messages = queue_client.receive_messages()
        for message in messages:
            if message.id == message_id:
                queue_client.delete_message(message)
                return True
        return False


if __name__ == "__main__":
    azure_queue = AzureQueue()

    # Example: Enqueue a notification
    notification_data = {
        "title": "Test Notification",
        "body": "This is a test notification",
        "token": "your_device_token"
    }
    queue_name = "notification-queue"
    message = azure_queue.send_message(notification_data, queue_name)
    print(f"Notification enqueued with ID: {message.id}")
