<!DOCTYPE html>
<html lang="en">
<head>
    {% block head %}
    <title>XAPA Backend Document</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous" >
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    {% endblock %}
</head>
<body>
  {% block navigation %}
    <nav class="navbar navbar-expand-md navbar-light bg-light mb-3">
        <div class="container">
          <a class="navbar-brand" href="/">
            <img src="{{ url_for('static', filename='images/xapa.png') }}" alt="" width="30" class="d-inline-block align-text-top"> 
          </a>
          {% if session.get('user_info') %}
          <p>Welcome, {{ session.get('user_info').get('first_name') }} {{ session.get('user_info').get('last_name') }}</p> 
          {% else %}
          <p>Welcome to XAPA Backend Document</p> 
          {% endif %}
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="collapse navbar-collapse" id="navbarCollapse">                
            <ul class="navbar-nav mb-2 mb-md-0 ms-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="dropdown07XL" data-bs-toggle="dropdown" aria-expanded="true" style="width: 130px">Pages</a>
                    <ul class="dropdown-menu" aria-labelledby="dropdown07XL" data-bs-popper="none">
                      {% if session.get('user_info') %}
                      <li><a class="dropdown-item" href="/api/cms/document">CMS API Document</a></li>
                      <li><a class="dropdown-item" href="/api/app/document">APP API Document</a></li>
                      <li><a class="dropdown-item" href="/api/account/document">Account API Document</a></li>
                      <li><a class="dropdown-item" href="/api/file/document">File API Document</a></li>
                      <li><a class="dropdown-item" href="/api/test/document">Test API Document</a></li>
                      {% else %}
                      <li><a class="dropdown-item" href="/api/account/document">Account API Document</a></li>
                      {% endif %}
                </ul>
                </li>
            </ul>
          </div>
        </div>
      </nav>
  {% endblock %}

  {% block content %}
      <main class="container">    
        <div class="mb-5">
          <form id="authForm">
            <div class="mb-1">
              <label class="form-label">Login with AUTH0</label>
            </div>
            
            <button type="submit" class="btn btn-primary" style="background-color: #6705BD">Login</button>
          </form>
      </div>
      </main>     
      <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>       
      <script>
        $(document).ready(function(){
            $("#userForm").on('submit', function(e){
                e.preventDefault();
        
                $.ajax({
                    url: '/api/account/authenticate',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({ 'user_name': $('#apiusername').val(), 'login_type': 'api' }),
                    beforeSend: function() {
                        // Show loading bar
                        $('#loading').show();
                    },
                    success: function(response) {
                        if (response.status == 200) {
                            window.location.href = response.data.login_url;
                        }
                    },
                    complete: function() {
                        // Hide loading bar
                        $('#loading').hide();
                    }
                });
            });

            $("#authForm").on('submit', function(e){
                e.preventDefault();
        
                $.ajax({
                    url: '/api/account/authenticate',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({ 'sso_key': 'auth0', 'login_type': 'api' }),
                    beforeSend: function() {
                        // Show loading bar
                        $('#loading').show();
                    },
                    success: function(response) {
                        if (response.status == 200) {
                            window.location.href = response.data.login_url;
                        }
                    },
                    complete: function() {
                        // Hide loading bar
                        $('#loading').hide();
                    }
                });
            });
        });
    </script>
    {% endblock %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>
  </body>
</html>
