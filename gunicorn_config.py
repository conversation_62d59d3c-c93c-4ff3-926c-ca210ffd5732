import multiprocessing
import psutil

# Gunicorn configuration file
timeout = 240
bind = "0.0.0.0:5000"

# Auto-detect system resources
cpu_count = multiprocessing.cpu_count()
memory_bytes = psutil.virtual_memory().total
memory_gb = memory_bytes / (1024**3)

# Dynamic worker calculation for I/O-bound image processing:
# Base formula: (2 * cpu_count) + 1
# Memory constraint: Limit based on available RAM (each worker uses ~200-500MB)
base_workers = (2 * cpu_count) + 1
memory_limit_workers = max(1, int(memory_gb // 0.5))  # 500MB per worker
workers = min(base_workers, memory_limit_workers)

# Optimal threads per worker for image processing
# More threads help with I/O operations during image read/write
if workers <= 2:
    threads = 4  # More threads for fewer workers
elif workers <= 4:
    threads = 3
else:
    threads = 2  # Fewer threads for many workers to avoid context switching

# Production optimizations
worker_class = "gthread"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
preload_app = True
keepalive = 65

# Memory and performance tuning
worker_tmp_dir = "/dev/shm"  # Use shared memory for temp files if available

print(f"Auto-configured Gunicorn: {workers} workers, {threads} threads per worker")
print(f"System: {cpu_count} CPUs, {memory_gb:.1f}GB RAM")
