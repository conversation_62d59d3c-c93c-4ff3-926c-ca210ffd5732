name: Trigger auto deployment for xapabackend

# When this action will be executed
on:
  # Automatically trigger it when detected changes in repo
  push:
    branches: 
      [ prod ]
    paths:
    - '**'
    - '.github/workflows/xapabackend-AutoDeployTrigger-fa01be3b-9549-4bf4-aeff-77cb95883575.yml'

  # Allow manual trigger 
  workflow_dispatch:
    inputs:
      build-and-deploy:
          description: 'Deploy Prod Backend'
          required: true
          type: boolean
          default: true

jobs:
  build-and-deploy:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed') || ${{ github.event.inputs.build-and-deploy == 'true' }}
    runs-on: ubuntu-latest
    name: Deploy Prod Backend
    outputs:
      commit-author: ${{ steps.commit_info.outputs.COMMITED_AUTHOR }}
      commit-branch: ${{ steps.commit_info.outputs.COMMITED_BRANCH }}
      commit-message: ${{ steps.commit_info.outputs.COMMITED_MESSAGE }}
      commit-short-sha: ${{ steps.commit_info.outputs.COMMITED_SHORT_SHA }}

    steps:
      - name: Checkout to the branch
        uses: actions/checkout@v4
        with:
          submodules: true
          lfs: false

      - name: Get commit info
        id: commit_info
        run: |
          echo "COMMITED_AUTHOR=$(git log -1 --pretty=format:'%an')" >> "$GITHUB_OUTPUT"
          echo "COMMITED_BRANCH=$(git branch --show-current)" >> "$GITHUB_OUTPUT"
          echo "COMMITED_MESSAGE=$(git log -1 --pretty=format:'%s')" >> "$GITHUB_OUTPUT"
          echo "COMMITED_SHORT_SHA=$(git rev-parse --short HEAD)" >> "$GITHUB_OUTPUT"

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.XAPABACKEND_AZURE_CREDENTIALS }}

      - name: Build and push container image to registry
        uses: azure/container-apps-deploy-action@v2
        with:
          appSourcePath: ${{ github.workspace }}
          _dockerfilePathKey_: _dockerfilePath_
          registryUrl: xapabackendcontainerregistry.azurecr.io
          registryUsername: ${{ secrets.XAPABACKEND_REGISTRY_USERNAME }}
          registryPassword: ${{ secrets.XAPABACKEND_REGISTRY_PASSWORD }}
          containerAppName: xapabackend
          resourceGroup: xapa_backend
          imageToBuild: xapabackendcontainerregistry.azurecr.io/xapabackend:${{ github.sha }}
          
  slack_notification:
    if: ${{ always() }}
    runs-on: ubuntu-latest
    name: Slack Notification
    needs: [build-and-deploy]
    steps:
      - name: Set up curl
        run: sudo apt-get update && sudo apt-get install -y curl
      - name: Slack Notification
        run: |
          is_cancelled=false
          if [ "${{ needs.build-and-deploy.result }}" == "cancelled" ]; then
            exit 0
          fi
          SLACK_MESSAGE=":dog2: Backend Updated%0A"
          SLACK_MESSAGE+=":dog2: Branch: ${{ needs.build-and-deploy.outputs.commit-branch }}"
          SLACK_MESSAGE+=" | "
          SLACK_MESSAGE+="%23$GITHUB_RUN_NUMBER -${{ needs.build-and-deploy.outputs.commit-short-sha }}"
          SLACK_MESSAGE+=" | "
          SLACK_MESSAGE+="${{ needs.build-and-deploy.outputs.commit-author }}%0A"
          SLACK_MESSAGE+=":dog2: Commit Message: ${{ needs.build-and-deploy.outputs.commit-message }}%0A"
          SLACK_MESSAGE+=":dog2: Action Url: $GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID%0A"
          SLACK_MESSAGE+="________________________________%0A"
          curl -d "text=$SLACK_MESSAGE" -d "channel=xapa-ci" -H "Authorization: Bearer ${{ secrets.SLACK_TOKEN }}" -X POST https://slack.com/api/chat.postMessage >/dev/null 2>&1
